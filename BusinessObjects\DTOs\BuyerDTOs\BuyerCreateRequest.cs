﻿using MongoDB.Bson.Serialization.Attributes;
using System.ComponentModel.DataAnnotations;

namespace BusinessObjects.DTOs.BuyerDTOs
{
	public class BuyerCreateRequest
	{
		[Required]
		public string name { get; set; }

		[Required]
		[BsonElement("phone")]
		[Phone]
		public string phone { get; set; }
		public string? email { get; set; }
		[Required]
		public string address { get; set; }
		[Required]
		public List<string>? idVerifications { get; set; }
	
		[Required]
		public string? bankCode { get; set; }
		[Required]
		public string? bankName { get; set; }
		[Required]
		public string? taxCode { get; set; }
		public string? faxCode { get; set; }
	}
}
