using BusinessObjects.DTOs;
using BusinessObjects.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Services.Interfaces;
using System.ComponentModel;
using System.Text.Json;

namespace Controllers.Controllers
{
    [Route("api/contracts")]
    [ApiController]
    [Authorize(Roles = "Admin,Saler")]
    public class ContractController : ControllerBase
    {
        private readonly IContractService _contractService;
        public ContractController(IContractService rentalContractService)
        {
            _contractService = rentalContractService;
        }
        [HttpGet]
        public async Task<IActionResult> GetAllContracts(
            int pageNumber = 1, int pageSize = 10,
            string? propertyId = null, string? ownerId = null, string? tenantId = null, string? buyerId = null, string? salerId = null, ContractType? contractType = null)
        {
            var contracts = await _contractService.GetAllContractsAsync(pageNumber, pageSize, propertyId, ownerId, tenantId, buyerId, salerId, contractType);
            return Ok(new ApiResponse { code = 200, status = true, message = "Success", data = contracts });
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<ApiResponse>> GetContract(string id)
        {
            var contract = await _contractService.GetContractByIdAsync(id);
            if (contract == null)
                return NotFound(new ApiResponse { code = 404, status = false, message = "Contract not found", data = null });

            return Ok(new ApiResponse { code = 200, status = true, message = "Success", data = contract });
        }

        [HttpGet("active")]
        public async Task<IActionResult> GetActiveContracts(int pageNumber = 1, int pageSize = 10)
        {
            var contracts = await _contractService.GetActiveContractsAsync(pageNumber, pageSize);
            return Ok(new ApiResponse { code = 200, status = true, message = "Success", data = contracts });
        }

        [HttpGet("expiring/{daysToExpire}")]
        public async Task<IActionResult> GetExpiringContracts(int daysToExpire, int pageNumber = 1, int pageSize = 10)
        {
            var contracts = await _contractService.GetExpiringContractsAsync(daysToExpire, pageNumber, pageSize);
            return Ok(new ApiResponse { code = 200, status = true, message = "Success", data = contracts });
        }

        [HttpGet("deleted")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> GetDeletedContracts(int pageNumber = 1, int pageSize = 10)
        {
            var contracts = await _contractService.GetDeletedContractsAsync(pageNumber, pageSize);
            return Ok(new ApiResponse { code = 200, status = true, message = "Success", data = contracts });
        }

        [HttpGet("saler")]
        public async Task<IActionResult> GetContractsForSaler(int pageNumber = 1, int pageSize = 10, string? propertyId = null, string? ownerId = null, string? tenantId = null, string? buyerId = null, ContractType? contractType = null)
        {
            var contracts = await _contractService.GetAllContractForSalerAsync(pageNumber, pageSize, propertyId, ownerId, tenantId, buyerId, contractType);
            return Ok(new ApiResponse { code = 200, status = true, message = "Success", data = contracts });
        }

        [HttpPost("rental")]   
        public async Task<ActionResult<ApiResponse>> CreateContract([FromBody] CreateRentalContractDTO createRentalContractDTO)
        {
            try
            {
                var createdContract = await _contractService.CreateRentalContractAsync(createRentalContractDTO);
                return CreatedAtAction(nameof(GetContract), new { id = createdContract.id }, new ApiResponse { code = 201, status = true, message = "Hợp đồng đã được tạo thành công", data = createdContract });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse { code = 400, status = false, message = ex.Message, data = null });
            }
        }
        [HttpPost("sale")]
        public async Task<ActionResult<ApiResponse>> CreateSaleContract([FromBody] CreateSaleContractDTO createSaleContractDTO)
        {
            try
            {
                var createdContract = await _contractService.CreateSaleContractAsync(createSaleContractDTO);
                return CreatedAtAction(nameof(GetContract), new { id = createdContract.id }, new ApiResponse { code = 201, status = true, message = "Hợp đồng đã được tạo thành công", data = createdContract });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse { code = 400, status = false, message = ex.Message, data = null });
            }
        }

        [HttpPut("rental/{id}")]
        public async Task<ActionResult<ApiResponse>> UpdateRentalContract(string id, UpdateRentalContractDTO updateRentalContractDTO)
        {
            var updatedContract = await _contractService.UpdateRentalContractAsync(id, updateRentalContractDTO);
            if (updatedContract == null)
                return NotFound(new ApiResponse { code = 404, status = false, message = "Contract not found", data = null });

            return Ok(new ApiResponse { code = 200, status = true, message = "Contract updated successfully", data = updatedContract });
        }

		[HttpPut("sale/{id}")]
		public async Task<ActionResult<ApiResponse>> UpdateSaleContract(string id, UpdateSaleContractDTO updateSaleContractDTO)
		{
			var updatedContract = await _contractService.UpdateSaleContractAsync(id, updateSaleContractDTO);
			if (updatedContract == null)
				return NotFound(new ApiResponse { code = 404, status = false, message = "Contract not found", data = null });

			return Ok(new ApiResponse { code = 200, status = true, message = "Contract updated successfully", data = updatedContract });
		}

		[HttpPost("{id}/renew")]
        public async Task<ActionResult<ApiResponse>> RenewContract(string id, RenewContractDTO renewContractDTO)
        {
            var renewedContract = await _contractService.RenewContractAsync(id, renewContractDTO);
            if (renewedContract == null)
                return NotFound(new ApiResponse { code = 404, status = false, message = "Contract not found", data = null });

            return Ok(new ApiResponse { code = 200, status = true, message = "Contract renewed successfully", data = renewedContract });
        }

        [HttpPost("{id}/restore")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ApiResponse>> RestoreContract(string id)
        {
            var restoredContract = await _contractService.RestoreContractAsync(id);
            if (restoredContract == null)
                return NotFound(new ApiResponse { code = 404, status = false, message = "Contract not found", data = null });

            return Ok(new ApiResponse { code = 200, status = true, message = "Contract restored successfully", data = restoredContract });
        }

        [HttpDelete("{id}")]
        public async Task<ActionResult<ApiResponse>> DeleteContract(string id)
        {
            var deletedContract = await _contractService.SoftDeleteContractAsync(id);
            if (deletedContract == null)
                return NotFound(new ApiResponse { code = 404, status = false, message = "Contract not found", data = null });

            return Ok(new ApiResponse { code = 200, status = true, message = "Contract deleted successfully", data = deletedContract });
        }
        [HttpDelete("{id}/permanent")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ApiResponse>> PermanentDeleteContract(string id)
        {
            var result = await _contractService.DeleteContractAsync(id);
            if (!result)
                return NotFound(new ApiResponse { code = 404, status = false, message = "Contract not found", data = null });

            return Ok(new ApiResponse { code = 204, status = true, message = "Contract permanently deleted", data = null });
        }

        [HttpPut("{id}/pdf")]
        public async Task<ActionResult<ApiResponse>> UpdateContractPdf(string id, IFormFile pdfContract)
        {
            try
            {
                if (pdfContract == null)
                {
                    return BadRequest(new ApiResponse { code = 400, status = false, message = "Không có file PDF được cung cấp", data = null });
                }

                // Kiểm tra kích thước file
                if (pdfContract.Length > 10 * 1024 * 1024) // 10MB
                {
                    return BadRequest(new ApiResponse { code = 400, status = false, message = "File PDF quá lớn. Kích thước tối đa là 10MB", data = null });
                }

                // Kiểm tra định dạng file
                var extension = Path.GetExtension(pdfContract.FileName).ToLowerInvariant();
                if (extension != ".pdf")
                {
                    return BadRequest(new ApiResponse { code = 400, status = false, message = "Chỉ chấp nhận file PDF", data = null });
                }

                var updatedContract = await _contractService.UpdateContractPdfAsync(id, pdfContract);
                if (updatedContract == null)
                    return NotFound(new ApiResponse { code = 404, status = false, message = "Không tìm thấy hợp đồng", data = null });

                return Ok(new ApiResponse { code = 200, status = true, message = "File PDF hợp đồng đã được cập nhật", data = updatedContract });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse { code = 400, status = false, message = ex.Message, data = null });
            }
        }
      
        [HttpPost("send-contract-direct")]
        public async Task<ActionResult<ApiResponse>> SendContractByEmailDirect([FromBody] SendContractByEmailDTO sendContractByEmailDTO)
        {
            try
            {
                var result = await _contractService.SendContractByEmailAsync(sendContractByEmailDTO.email, sendContractByEmailDTO.contractLink);
                if (result)
                {
                    return Ok(new ApiResponse { code = 200, status = true, message = "Đường dẫn hợp đồng đã được gửi thành công qua email", data = null });
                }
                else
                {
                    return NotFound(new ApiResponse { code = 404, status = false, message = "Không thể gửi email hợp đồng", data = null });
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse { code = 400, status = false, message = "Lỗi khi gửi hợp đồng qua email: " + ex.Message, data = null });
            }
        }

        [HttpPost("{id}/pay")]
        [AllowAnonymous]
        public async Task<IActionResult> PayRentalContract(string id)
        {
            try
            {
                var paymentUrl = await _contractService.CreatePayRentalContraclRequestAsync(id);
                if (string.IsNullOrEmpty(paymentUrl))
                    return NotFound(new ApiResponse { code = 404, status = false, message = "Không tìm thấy hợp đồng", data = null });
                return Ok(new ApiResponse { code = 200, status = true, message = "Tạo yêu cầu thanh toán thành công", data = paymentUrl });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse { code = 400, status = false, message = ex.Message, data = null });
            }
        }


        [HttpGet("ipn")]
        [AllowAnonymous]
        public async Task<IActionResult> ProcessIpn()
        {
            try
            {
                var result = await _contractService.ProcessIpnAsync(Request.Query);
                if (result.Success)
                    return Ok(new ApiResponse { code = 200, status = true, message = result.Message, data = null });
                else
                    return BadRequest(new ApiResponse { code = 400, status = false, message = result.Message, data = null });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse { code = 400, status = false, message = ex.Message, data = null });
            }
        }
    }
}