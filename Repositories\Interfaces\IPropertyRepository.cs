﻿using BusinessObjects.Models;
using BusinessObjects.QueryObject;

namespace Repositories.Interfaces
{
    public interface IPropertyRepository : IGenericRepository<Property>
    {

        Task InsertManyAsync(IEnumerable<Property> properties); // import xlsx of properties

        Task<Property> GetByTransactionId(string transactionId);
        Task<List<Property>> GetByIdsAsync(List<string> ids);
        Task<(List<Property> properties, int totalCount)> GetAllPropertiesAsync(QueryProperty query, string? userRole = null);

        Task<(List<Property> properties, int totalCount)> GetPropertiesBySellerAsync(QueryProperty query, string sellerId);

        Task<(List<Property> properties, int totalCount)> GetPropertiesByOwnerAsync(QueryProperty query, string ownerId);
        Task<(List<Property> properties, int totalCount)> GetDraftPropertiesByUserAsync(string userId, string role, int pageSize, int currentPage);
        Task InitializeIndexesAsync();

    }
}
