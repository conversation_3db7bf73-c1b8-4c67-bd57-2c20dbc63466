﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace BusinessObjects.Models
{
    public class DealsActivityLog
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public ObjectId id { get; set; } = ObjectId.GenerateNewId();

        [BsonElement("dealId")]
        [BsonRepresentation(BsonType.ObjectId)]
        public string dealId { get; set; }

        [BsonElement("fromStatus")]
        [BsonRepresentation(BsonType.String)]
        public Deal.DealStatus fromStatus { get; set; }

        [BsonElement("toStatus")]
        [BsonRepresentation(BsonType.String)]
        public Deal.DealStatus toStatus { get; set; }

        [BsonElement("isDeleteAction")]
        public bool isDeleteAction { get; set; } = false;

        [BsonElement("timestamp")]
        public DateTime timestamp { get; set; } = DateTime.UtcNow;

        [BsonElement("triggeredBy")]
        [BsonRepresentation(BsonType.ObjectId)]
        public string triggeredBy { get; set; } // SalesRepId
    }
}
