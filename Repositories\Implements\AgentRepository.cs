﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.DTOs.AgentDTOs;
using BusinessObjects.Models;
using Microsoft.EntityFrameworkCore;
using MongoDB.Driver;
using Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Repositories.Implements
{
	public class AgentRepository : GenericRepository<Agent>, IAgentRepository
	{
		private readonly IMongoCollection<Agent> _mongoCollectionAgent;
		private readonly IMongoCollection<TeamAgentLink> _mongoCollectionTeamAgentLinks;
		private readonly IMongoCollection<AgentCompanyLink> _mongoCollectionAgentCompanyLinks;

		public AgentRepository(RevoLandDbContext context) : base(context, "Agents")
		{
			_mongoCollectionTeamAgentLinks = context.GetCollection<TeamAgentLink>("TeamAgentLinks");
			_mongoCollectionAgent = context.GetCollection<Agent>("Agents");
			_mongoCollectionAgentCompanyLinks = context.GetCollection<AgentCompanyLink>("AgentCompanyLinks");
		}

		public async Task AssignAgentToTeam(AddAgentToTeamRequest request)
		{
			var teamAgentLink = new TeamAgentLink
			{
				teamId = request.teamId,
				agentId = request.agentId,
				role = request.role,
			};
			await _mongoCollectionTeamAgentLinks.InsertOneAsync(teamAgentLink);
		}

		public async Task DeleteAgentFromTeam(string teamId, string agentId)
		{
			var filter = Builders<TeamAgentLink>.Filter.And(
				Builders<TeamAgentLink>.Filter.Eq(t => t.teamId, teamId),
				Builders<TeamAgentLink>.Filter.Eq(t => t.agentId, agentId)
			);

			await _mongoCollectionTeamAgentLinks.DeleteOneAsync(filter);
		}

		public Task<Agent> GetAgentByEmailAsync(string email)
		{
			var filter = Builders<Agent>.Filter.Eq(agent => agent.email, email);
			return _mongoCollectionAgent.Find(filter).FirstOrDefaultAsync();
		}

		public async Task<List<Agent>> GetAllAgentsByCompanyIdAsync(string companyId)
		{
			// 1. Lấy danh sách agentId thuộc company
			var agentIds = await _mongoCollectionAgentCompanyLinks
				.Find(link => link.companyId == companyId)
				.Project(link => link.agentId)
				.ToListAsync();

			// 2. Lấy danh sách agent từ agentIds
			var agents = await _mongoCollectionAgent
				.Find(agent => agentIds.Contains(agent.id))
				.ToListAsync();

			return agents;
		}


	}
}
