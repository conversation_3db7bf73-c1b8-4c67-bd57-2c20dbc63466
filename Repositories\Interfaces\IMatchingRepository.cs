﻿using BusinessObjects.Models;
using FirebaseAdmin.Messaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Repositories.Interfaces
{
    public interface IMatchingRepository : IGenericRepository<Matching>
    {
        Task<List<Matching>> GetMatchesForUserAsync(string userId);
        Task<bool> DeactivateMatchAsync(string matchId, string userId);
        Task<List<User>> GetRoommateSuggestionsAsync(string userId, int limit = 20, int skip = 0);
    }
}
