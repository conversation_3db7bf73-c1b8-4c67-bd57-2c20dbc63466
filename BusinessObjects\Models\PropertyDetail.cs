﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace BusinessObjects.Models
{
    public class PropertyDetail
    {
        [BsonElement("bedrooms")]
        public int? bedrooms { get; set; }

        [BsonElement("bathrooms")]
        public int? bathrooms { get; set; }

        [BsonElement("livingRooms")]
        public int? livingRooms { get; set; }

        [BsonElement("kitchens")]
        public int? kitchens { get; set; }

        [BsonElement("landArea")]
        public double? landArea { get; set; }

        [BsonElement("landWidth")]
        public double? landWidth { get; set; }

        [BsonElement("landLength")]
        public double? landLength { get; set; }

        [BsonElement("buildingArea")]
        public double? buildingArea { get; set; }

        [BsonElement("numberOfFloors")]
        public int? numberOfFloors { get; set; }

        [BsonElement("hasBasement")]
        public bool? hasBasement { get; set; }

        [BsonElement("floorNumber")]
        public int? floorNumber { get; set; }

        [BsonElement("apartmentOrientation")]
        [BsonRepresentation(BsonType.String)]
        public ApartmentOrientation apartmentOrientation { get; set; }

        [BsonElement("furnished")]
        public bool? furnished { get; set; }
    }

    public enum ApartmentOrientation
    {
        North,
        South,
        East,
        West,
        NorthEast,
        NorthWest,
        SouthEast,
        SouthWest
    }


}
