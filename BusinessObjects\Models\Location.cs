﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace BusinessObjects.Models
{
    public class Location
    {
        [BsonElement("address")]
        public string address { get; set; }

        [BsonElement("city")]
        public string city { get; set; }

        [BsonElement("district")]
        public string district { get; set; }

        [BsonElement("ward")]
        public string ward { get; set; }

        [BsonElement("latitude")]
        public double latitude { get; set; }

        [BsonElement("longitude")]
        public double longitude { get; set; }
    }

}
