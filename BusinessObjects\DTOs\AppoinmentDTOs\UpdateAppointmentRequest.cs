﻿using BusinessObjects.Models;
using System.ComponentModel.DataAnnotations;

namespace BusinessObjects.DTOs.AppoinmentDTOs
{
    public class UpdateAppointmentRequest
    {
        /// <summary>
        /// New appointment date and time
        /// </summary>
        public DateTime? date { get; set; }

        /// <summary>
        /// New appointment status
        /// </summary>
        public AppointmentStatus? status { get; set; }

        /// <summary>
        /// New property ID (if changing property)
        /// </summary>
        public string? propertyId { get; set; }

        /// <summary>
        /// New saler ID (if reassigning)
        /// </summary>
        public string? salerId { get; set; }

        /// <summary>
        /// Update reason (for audit trail and status history)
        /// </summary>
        [MaxLength(200, ErrorMessage = "Update reason must not exceed 200 characters")]
        public string? updateReason { get; set; }

        /// <summary>
        /// Additional notes (will be added to status history if status changes)
        /// </summary>
        [MaxLength(500, ErrorMessage = "Notes must not exceed 500 characters")]
        public string? notes { get; set; }

        /// <summary>
        /// Whether to send notifications to participants
        /// </summary>
        public bool sendNotifications { get; set; } = true;

        /// <summary>
        /// Whether to update calendar events
        /// </summary>
        public bool updateCalendarEvents { get; set; } = true;

    }

}
