﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.Models
{
    public class Notification
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string id { get; set; }
        public string userId { get; set; }
        public NotificationType type { get; set; }
        public string content { get; set; }
        public DateTime sentAt { get; set; }
        public bool isDelivered { get; set; }
    }

    public enum NotificationType
    {
        email,
        sms,
        inApp
    }
}
