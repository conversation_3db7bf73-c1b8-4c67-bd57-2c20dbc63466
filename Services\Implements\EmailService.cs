﻿using BusinessObjects.Settings;
using MailKit.Net.Smtp;
using Microsoft.Extensions.Options;
using MimeKit;
using Services.Interfaces;
using Microsoft.Extensions.Logging;

namespace Services.Implements
{
    public class EmailService : IEmailService
    {
        private readonly EmailSettings _emailSettings;
        private readonly ILogger<EmailService> _logger;

        public EmailService(IOptions<EmailSettings> emailSettings, ILogger<EmailService> logger)
        {
            _emailSettings = emailSettings.Value;
            _logger = logger;
        }
        public async Task SendEmailAsync(string toEmail, string subject, string content)
        {
            _logger.LogInformation("Bắt đầu gửi email tới {ToEmail} với tiêu đề {Subject}", toEmail, subject);
            
            var emailMessage = new MimeMessage();
            emailMessage.From.Add(new MailboxAddress("RevoLand", _emailSettings.SmtpUser));
            emailMessage.To.Add(new MailboxAddress("", toEmail));
            emailMessage.Subject = subject;

            var bodyBuilder = new BodyBuilder { HtmlBody = content };
            emailMessage.Body = bodyBuilder.ToMessageBody();

            using (var client = new SmtpClient())
            {
                try
                {
                    _logger.LogInformation("Kết nối tới máy chủ SMTP {SmtpServer}:{SmtpPort}", _emailSettings.SmtpServer, _emailSettings.SmtpPort);
                    await client.ConnectAsync(_emailSettings.SmtpServer, _emailSettings.SmtpPort, MailKit.Security.SecureSocketOptions.StartTls);
                    _logger.LogInformation("Xác thực với máy chủ SMTP");
                    await client.AuthenticateAsync(_emailSettings.SmtpUser, _emailSettings.SmtpPass);
                    _logger.LogInformation("Gửi email tới {ToEmail}", toEmail);
                    await client.SendAsync(emailMessage);
                    _logger.LogInformation("Gửi email thành công tới {ToEmail}", toEmail);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Lỗi khi gửi email tới {ToEmail}: {ErrorMessage}", toEmail, ex.Message);
                    throw;
                }
                finally
                {
                    _logger.LogInformation("Ngắt kết nối với máy chủ SMTP");
                    await client.DisconnectAsync(true);
                }
            }
        }
    }
}
