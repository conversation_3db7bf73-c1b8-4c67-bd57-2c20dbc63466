﻿using BusinessObjects.Validations;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.DTOs.LandlordDTOs
{
    public class LandlordCreateRequest
    {
        [Required]
        [ValidName]

        public string? name { get; set; }

        [EmailAddress]
        [Required]
        public string? email { get; set; }
        [PhoneVN]
        public string? phone { get; set; }

        [Required]
        public string? address { get; set; }

        public DateTime createdAt { get; set; }

        public DateTime updatedAt { get; set; }
    }
}
