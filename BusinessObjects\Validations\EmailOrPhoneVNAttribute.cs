﻿using System.ComponentModel.DataAnnotations;

namespace BusinessObjects.Validations
{
    public class EmailOrPhoneVNAttribute : ValidationAttribute
    {
        protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
        {
            if (value == null || string.IsNullOrWhiteSpace(value.ToString()))
            {
                return new ValidationResult("Email or Phone number is required");
            }

            string input = value.ToString()!.Trim();

            // Check if it's a valid email
            if (IsValidEmail(input))
            {
                return ValidationResult.Success;
            }

            // Check if it's a valid Vietnamese phone number using existing logic
            var phoneValidator = new PhoneVNAttribute();
            var phoneValidationResult = phoneValidator.IsValid(input);

            if (phoneValidationResult)
            {
                return ValidationResult.Success;
            }

            return new ValidationResult("Must be a valid email address or Vietnamese phone number");
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var emailAttribute = new EmailAddressAttribute();
                return emailAttribute.IsValid(email);
            }
            catch
            {
                return false;
            }
        }
    }
}
