﻿using BusinessObjects.Models;

namespace Repositories.Interfaces
{
    public interface IGoogleCrendentialRepository : IGenericRepository<GoogleCredentialModel>
    {
        Task<GoogleCredentialModel?> GetByUserIdAsync(string userId);
        Task<GoogleCredentialModel?> GetActiveByUserIdAsync(string userId);
        Task<bool> DeactivateByUserIdAsync(string userId);
        Task<bool> UpdateTokenAsync(string userId, string accessToken, DateTime? expiry);
        Task<List<GoogleCredentialModel>> GetExpiredTokensAsync();
        Task<bool> UpdateLastUsedAsync(string userId);
        Task<bool> IsTokenValidAsync(string userId);
        Task<GoogleCredentialModel?> RefreshTokenAsync(string userId, string newAccessToken, DateTime? newExpiry);
    }
}
