﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using MongoDB.Bson;
using MongoDB.Driver;
using Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Repositories.Implements
{
    public class ContractRepository : GenericRepository<Contract>, IContractRepository
    {
        private readonly IMongoCollection<Contract> _contractCollection;

        public ContractRepository(RevoLandDbContext context) : base(context, "Contracts")
        {
            _contractCollection = context.Contracts;
        }

        public override async Task<IEnumerable<Contract>> GetAllAsync()
        {
            // Chỉ trả về các bản ghi chưa bị xóa mềm
            var filter = Builders<Contract>.Filter.Eq(x => x.isDeleted, false);
            return await _contractCollection.Find(filter).ToListAsync();
        }

        public override async Task<Contract> GetByIdAsync(string id)
        {
            var objectId = new ObjectId(id);
            var filter = Builders<Contract>.Filter.And(
                Builders<Contract>.Filter.Eq(x => x.id, objectId),
                Builders<Contract>.Filter.Eq(x => x.isDeleted, false)
            );
            
            return await _contractCollection.Find(filter).FirstOrDefaultAsync();
        }

        public async Task<IEnumerable<Contract>> GetActiveContractsAsync()
        {
            var filter = Builders<Contract>.Filter.And(
                Builders<Contract>.Filter.Eq(x => x.status, ContractStatus.Active),
                Builders<Contract>.Filter.Eq(x => x.isDeleted, false)
            );
            return await _contractCollection.Find(filter).ToListAsync();
        }

        public async Task<IEnumerable<Contract>> GetExpiringContractsAsync(int daysToExpire)
        {
            var expiryDate = DateTime.UtcNow.AddDays(daysToExpire);
            var filter = Builders<Contract>.Filter.And(
                Builders<Contract>.Filter.Eq(x => x.status, ContractStatus.Active),
                Builders<Contract>.Filter.Lte(x => x.contractEndDate, expiryDate),
                Builders<Contract>.Filter.Eq(x => x.isDeleted, false)
            );
            
            return await _contractCollection.Find(filter).ToListAsync();
        }

        public async Task<List<Contract>> GetExpiringContractsAsync(DateTime expirationDate)
        {
            var filter = Builders<Contract>.Filter.And(
                Builders<Contract>.Filter.Eq(x => x.status, ContractStatus.Active),
                Builders<Contract>.Filter.Lte(x => x.contractEndDate, expirationDate),
                Builders<Contract>.Filter.Gt(x => x.contractEndDate, DateTime.UtcNow), // Chưa hết hạn hiện tại
                Builders<Contract>.Filter.Eq(x => x.isDeleted, false)
            );
            
            return await _contractCollection.Find(filter).ToListAsync();
        }

        public async Task<Contract> AddRenewalLogAsync(string contractId, RenewalLog renewalLog)
        {
            var objectId = new ObjectId(contractId);
            var filter = Builders<Contract>.Filter.And(
                Builders<Contract>.Filter.Eq(x => x.id, objectId),
                Builders<Contract>.Filter.Eq(x => x.isDeleted, false)
            );
            
            var update = Builders<Contract>.Update
                .Push(x => x.renewalLogs, renewalLog)
                .Set(x => x.contractEndDate, renewalLog.newEndDate)
                .Set(x => x.updatedAt, DateTime.UtcNow);
            
            await _contractCollection.UpdateOneAsync(filter, update);
            
            return await _contractCollection.Find(filter).FirstOrDefaultAsync();
        }

        public async Task<Contract> SoftDeleteAsync(string id)
        {
            var objectId = new ObjectId(id);
            var filter = Builders<Contract>.Filter.And(
                Builders<Contract>.Filter.Eq(x => x.id, objectId),
                Builders<Contract>.Filter.Eq(x => x.isDeleted, false)
            );
            
            var update = Builders<Contract>.Update
                .Set(x => x.isDeleted, true)
                .Set(x => x.deletedAt, DateTime.UtcNow)
                .Set(x => x.updatedAt, DateTime.UtcNow);
            
            await _contractCollection.UpdateOneAsync(filter, update);
            
            // Trả về đối tượng đã được cập nhật
            return await _contractCollection.Find(Builders<Contract>.Filter.Eq(x => x.id, objectId)).FirstOrDefaultAsync();
        }
        
        public async Task<IEnumerable<Contract>> GetDeletedContractsAsync()
        {
            var filter = Builders<Contract>.Filter.Eq(x => x.isDeleted, true);
            return await _contractCollection.Find(filter).ToListAsync();
        }

        public async Task<Contract> RestoreContractAsync(string id)
        {
            var objectId = new ObjectId(id);
            var filter = Builders<Contract>.Filter.And(
                Builders<Contract>.Filter.Eq(x => x.id, objectId),
                Builders<Contract>.Filter.Eq(x => x.isDeleted, true)
            );
            
            var update = Builders<Contract>.Update
                .Set(x => x.isDeleted, false)
                .Set(x => x.deletedAt, null)
                .Set(x => x.updatedAt, DateTime.UtcNow);
            
            await _contractCollection.UpdateOneAsync(filter, update);
            
            // Trả về đối tượng đã được cập nhật
            return await _contractCollection.Find(Builders<Contract>.Filter.Eq(x => x.id, objectId)).FirstOrDefaultAsync();
        }
    }
} 