﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace BusinessObjects.Models
{
	public class Swipe
	{
		[BsonId]
		[BsonRepresentation(BsonType.ObjectId)]
		public string id { get; set; } = ObjectId.GenerateNewId().ToString();
		public string fromUserId { get; set; }
		public string toUserId { get; set; }
		public bool isLike { get; set; }
		public DateTime swipedAt { get; set; }
	}
}
