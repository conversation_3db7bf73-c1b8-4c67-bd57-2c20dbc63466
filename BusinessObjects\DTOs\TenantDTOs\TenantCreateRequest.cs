﻿using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.DTOs.TenantDTOs
{
    public class TenantCreateRequest
    {
        [Required]
        public string name { get; set; }

        [Required]
        [BsonElement("phone")]
        [Phone]
        public string phone { get; set; }

        public string? email { get; set; }
        public string address { get; set; }
        [Required]
        public List<string> idVerification { get; set; }
        [Required]
        public DateTime leaseStartDate { get; set; }

        [Required]
        public DateTime leaseEndDate { get; set; }

    }
}
