﻿using BusinessObjects.DTOs.StatisticDTOs;

namespace BusinessObjects.Tools
{
    public class StatisticsBuilder
    {
        private readonly Dictionary<string, object> _metrics = new();
        private readonly StatisticsMetadata _metadata;

        public StatisticsBuilder(DatePeriod period, Dictionary<string, object> filters = null)
        {
            _metadata = new StatisticsMetadata
            {
                period = period,
                filters = filters ?? new Dictionary<string, object>()
            };
        }

        public StatisticsBuilder AddNumberMetric(string key, decimal value, NumberMetricOptions options)
        {
            var changePercentage = options.previousValue.HasValue
                ? ((value - options.previousValue.Value) / options.previousValue.Value) * 100
                : (decimal?)null;

            var trend = changePercentage switch
            {
                > 5 => "up",
                < -5 => "down",
                _ => "stable"
            };

            _metrics[key] = new NumberMetric
            {
                value = value,
                label = options.label,
                description = options.description,
                previousValue = options.previousValue,
                changePercentage = changePercentage,
                trend = trend,
                format = options.format ?? "number"
            };

            return this;
        }

        public StatisticsBuilder AddChartMetric(string key, decimal value, ChartMetricOptions options)
        {
            _metrics[key] = new ChartMetric
            {
                value = value,
                label = options.label,
                description = options.description,
                chartData = options.chartData ?? new List<ChartDataPoint>(),
                chartType = options.chartType ?? "line"
            };

            return this;
        }

        public StatisticsBuilder AddStatusMetric(string key, string value, StatusMetricOptions options)
        {
            _metrics[key] = new StatusMetric
            {
                value = value,
                label = options.label,
                description = options.description,
                status = options.status,
                icon = options.icon
            };

            return this;
        }

        public StatisticsBuilder AddListMetric(string key, List<ListItem> items, ListMetricOptions options)
        {
            var limitedItems = options.limit.HasValue
                ? items.Take(options.limit.Value).ToList()
                : items;

            _metrics[key] = new ListMetric
            {
                value = items.Count,
                label = options.label,
                description = options.description,
                items = limitedItems,
                limit = options.limit
            };

            return this;
        }

        public StatisticsBuilder AddCustomMetric(string key, object metric)
        {
            _metrics[key] = metric;
            return this;
        }

        public StatisticsResponse Build()
        {
            return new StatisticsResponse
            {
                dictionary = _metrics,
                metadata = _metadata
            };
        }

        public T GetMetric<T>(string key) where T : class
        {
            return _metrics.TryGetValue(key, out var metric) ? metric as T : null;
        }

        public IEnumerable<string> GetKeys() => _metrics.Keys;
    }

    // Options classes
    public class NumberMetricOptions
    {
        public string label { get; set; }
        public string description { get; set; }
        public decimal? previousValue { get; set; }
        public string format { get; set; }
    }

    public class ChartMetricOptions
    {
        public string label { get; set; }
        public string description { get; set; }
        public List<ChartDataPoint> chartData { get; set; }
        public string chartType { get; set; }
    }

    public class StatusMetricOptions
    {
        public string label { get; set; }
        public string description { get; set; }
        public string status { get; set; }
        public string icon { get; set; }
    }

    public class ListMetricOptions
    {
        public string label { get; set; }
        public string description { get; set; }
        public int? limit { get; set; }
    }

}
