using BusinessObjects.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Repositories.Interfaces
{
    public interface IRentalContractNotificationRepository
    {
        Task<List<RentalContractNotification>> GetNotificationsAsync();
        Task<RentalContractNotification> CreateNotificationAsync(RentalContractNotification notification);
        Task SendNotificationAsync(RentalContractNotification notification);
    }
} 