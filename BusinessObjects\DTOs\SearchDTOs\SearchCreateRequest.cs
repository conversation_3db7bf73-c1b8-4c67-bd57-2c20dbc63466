﻿using BusinessObjects.Models;
using BusinessObjects.QueryObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.DTOs.SearchDTOs
{
    public class SearchCreateRequest
    {
        public string? userId { get; set; }
        public string? searchTerm { get; set; }
        public bool isDescending { get; set; } = false;
        public List<PropertyStatus>? status { get; set; } = new();


        public List<PropertyType>? type { get; set; } = new();


        public List<PropDetailBoolFilter>? propertyDetailFilters { get; set; }


        public List<AmenityFilter>? amenityFilters { get; set; }

        public int? bedrooms { get; set; }
        public int? bathrooms { get; set; }
        public int? livingRooms { get; set; }
        public int? kitchens { get; set; }
        public double? landArea { get; set; }
        public double? landWidth { get; set; }
        public double? landLength { get; set; }
        public double? buildingArea { get; set; }
        public int? numberOfFloors { get; set; }
        public int? floorNumber { get; set; }
        public string? apartmentOrientation { get; set; }
        public PropertyEnumSortBy sortBy { get; set; } = PropertyEnumSortBy.name;
    }
}
