﻿using BusinessObjects.DTOs.TeamDTOs;
using BusinessObjects.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Services.Interfaces
{
	public interface ITeamService
	{
		Task<TeamCreateResponse> CreateTeamAsync(TeamCreateRequest request, string companyId);
		Task<TeamUpdateResponse> UpdateTeamAsync(TeamUpdateRequest request, string teamId);
		Task DeleteTeamAsync(string teamId);
		Task<IEnumerable<TeamResponse>> GetAll();
		Task<TeamResponse> GetTeamByIdAsync(string teamId);
	}
}
