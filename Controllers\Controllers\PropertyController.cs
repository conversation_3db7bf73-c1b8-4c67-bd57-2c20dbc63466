﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.PropertyDTO;
using BusinessObjects.QueryObject;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MongoDB.Bson;
using Services.Implements;
using Services.Interface;
using System.Security.Claims;

[Route("api/properties")]
[ApiController]
public class PropertyController : ControllerBase
{
    private readonly IPropertyService _propertyService;
    private readonly FirebaseService _firebaseService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public PropertyController(IPropertyService propertyService, FirebaseService firebaseService, IHttpContextAccessor httpContextAccessor)
    {
        _propertyService = propertyService;
        _firebaseService = firebaseService;
    }

    private ApiResponse CreateResponse(int code, bool status, string message, object data = null)
    {
        return new ApiResponse { code = code, status = status, message = message, data = data };
    }


    /// <summary>
    /// Get All VERIFIED properties
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpGet]
    public async Task<IActionResult> GetAllProperties([FromQuery] QueryProperty query)
    {


        if (!ModelState.IsValid)
        {
            return BadRequest(CreateResponse(400, false, "Dữ liệu property không hợp lệ", ModelState));
        }
        try
        {

            var properties = await _propertyService.GetAllPropertiesAsync(query);
            return Ok(CreateResponse(200, true, "Thành công", properties));
        }
        catch (InvalidDataException ex)
        {
            return NotFound(CreateResponse(404, false, ex.Message));
        }
        catch (InvalidOperationException ex)
        {
            return Conflict(CreateResponse(409, false, ex.Message));
        }
        catch (ArgumentException ex)
        {
            return BadRequest(CreateResponse(400, false, ex.Message));
        }
    }


    /// <summary>
    /// Get All properties current saler posted
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [Authorize]
    [HttpGet("saler")]
    public async Task<IActionResult> GetAllPropertiesBySalerId([FromQuery] QueryProperty query)
    {


        if (!ModelState.IsValid)
        {
            return BadRequest(CreateResponse(400, false, "Dữ liệu property không hợp lệ", ModelState));
        }
        try
        {
            var currentSalerId = User.Claims.FirstOrDefault(c => c.Type == "UserID")?.Value;
            var properties = await _propertyService.GetPropertiesBySalerIdAsync(query, currentSalerId);
            return Ok(CreateResponse(200, true, "Thành công", properties));
        }
        catch (InvalidDataException ex)
        {
            return NotFound(CreateResponse(404, false, ex.Message));
        }
        catch (InvalidOperationException ex)
        {
            return Conflict(CreateResponse(409, false, ex.Message));
        }
        catch (ArgumentException ex)
        {
            return BadRequest(CreateResponse(400, false, ex.Message));
        }
    }


    /// <summary>
    /// Get All owner's properties 
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpGet("owners/{id}")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> GetAllPropertiesByOwnerId(string id, [FromQuery] QueryProperty query)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(CreateResponse(400, false, "Dữ liệu property không hợp lệ", ModelState));
        }
        try
        {
            var properties = await _propertyService.GetPropertiesByOwnerIdAsync(query, id);
            return Ok(CreateResponse(200, true, "Thành công", properties));
        }
        catch (InvalidDataException ex)
        {
            return NotFound(CreateResponse(404, false, ex.Message));
        }
        catch (InvalidOperationException ex)
        {
            return Conflict(CreateResponse(409, false, ex.Message));
        }
        catch (ArgumentException ex)
        {
            return BadRequest(CreateResponse(400, false, ex.Message));
        }
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetPropertyById(string id)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(CreateResponse(400, false, "Yêu cầu không hợp lệ", ModelState));
        }

        if (!ObjectId.TryParse(id, out ObjectId objectId))
        {
            return BadRequest(CreateResponse(400, false, "ID không đúng định dạng"));
        }
        try
        {
            var userId = User.Claims.FirstOrDefault(c => c.Type == "UserID")?.Value;
            var role = User.FindFirst(ClaimTypes.Role)?.Value ?? "Role";
            var property = await _propertyService.GetPropertyByIdAsync(id, userId, role);

            return Ok(CreateResponse(200, true, "Thành công", property));
        }
        catch (InvalidDataException ex)
        {
            return NotFound(CreateResponse(404, false, ex.Message));
        }
        catch (InvalidOperationException ex)
        {
            return Conflict(CreateResponse(409, false, ex.Message));
        }
        catch (ArgumentException ex)
        {
            return BadRequest(CreateResponse(400, false, ex.Message));
        }

    }

    [Authorize(Roles = "Admin,Saler,Owner,User")]
    [HttpPost]
    public async Task<IActionResult> AddProperty(PropertyCreateRequest property)
    {

        if (!ModelState.IsValid)
        {
            return BadRequest(CreateResponse(400, false, "Dữ liệu không hợp lệ", ModelState));
        }

        try
        {
            var userId = User.Claims.FirstOrDefault(c => c.Type == "UserID")?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized(CreateResponse(401, false, "Người dùng chưa được xác thực"));
            }
            var createdProperty = await _propertyService.AddPropertyAsync(userId, property);
            return Ok(CreateResponse(201, true, "Thành công", createdProperty));
        }
        catch (InvalidDataException ex)
        {
            return NotFound(CreateResponse(404, false, ex.Message));
        }
        catch (InvalidOperationException ex)
        {
            return Conflict(CreateResponse(409, false, ex.Message));
        }
        catch (ArgumentException ex)
        {
            return BadRequest(CreateResponse(400, false, ex.Message));
        }
    }


    [Authorize]
    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateProperty(string id, PropertyUpdateRequest property)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(CreateResponse(400, false, "Dữ liệu property không hợp lệ", ModelState));
        }
        try
        {
            var currentSalerId = User.Claims.FirstOrDefault(c => c.Type == "UserID")?.Value;

            var updatedProperty = await _propertyService.UpdatePropertyAsync(id, currentSalerId, property);
            return Ok(CreateResponse(200, true, "Thành công", updatedProperty));
        }
        catch (InvalidDataException ex)
        {
            return NotFound(CreateResponse(404, false, ex.Message));
        }
        catch (InvalidOperationException ex)
        {
            return Conflict(CreateResponse(409, false, ex.Message));
        }
        catch (UnauthorizedAccessException ex)
        {
            return Unauthorized(CreateResponse(403, false, ex.Message));
        }
    }


    [Authorize]
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteProperty(string id)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(CreateResponse(400, false, "Yêu cầu không hợp lệ", ModelState));
        }

        try
        {
            var currentSalerId = User.Claims.FirstOrDefault(c => c.Type == "UserID")?.Value;
            var result = await _propertyService.DeletePropertyAsync(id, currentSalerId);
            if (!result)
                return NotFound(CreateResponse(404, false, "Không tìm thấy property"));

            return Ok(CreateResponse(200, true, "Thành công"));

        }
        catch (InvalidDataException ex)
        {
            return NotFound(CreateResponse(404, false, ex.Message));
        }
        catch (InvalidOperationException ex)
        {
            return Conflict(CreateResponse(409, false, ex.Message));
        }
        catch (UnauthorizedAccessException ex)
        {
            return Unauthorized(CreateResponse(403, false, ex.Message));
        }
    }

    [Authorize(Roles = "Admin")]
    [HttpPatch("{id}/verify")]
    public async Task<IActionResult> VerifyProperty(string id)
    {
        try
        {
            await _propertyService.VerifyPropertyByIdAsync(id);
            return Ok(CreateResponse(200, true, "Thành công"));
        }
        catch (Exception ex)
        {
            return BadRequest(CreateResponse(500, false, ex.Message));
        }
    }

    [Authorize(Roles = "Admin")]
    [HttpPatch("{id}/unverify")]
    public async Task<IActionResult> UnverifyProperty(string id)
    {
        try
        {
            await _propertyService.UnverifyPropertyByIdAsync(id);
            return Ok(CreateResponse(200, true, "Thành công"));
        }
        catch (Exception ex)
        {
            return BadRequest(CreateResponse(500, false, ex.Message));
        }
    }

    [Authorize(Roles = "Admin")]
    [HttpPatch("{id}/feature")]
    public async Task<IActionResult> FeatureProperty(string id)
    {
        try
        {
            await _propertyService.FeaturePropertyByIdAsync(id);
            return Ok(CreateResponse(200, true, "Thành công"));
        }
        catch (Exception ex)
        {
            return BadRequest(CreateResponse(500, false, ex.Message));
        }
    }

    [Authorize(Roles = "Admin")]
    [HttpPatch("{id}/unfeature")]
    public async Task<IActionResult> UnfeatureProperty(string id)
    {
        try
        {
            await _propertyService.UnfeaturePropertyByIdAsync(id);
            return Ok(CreateResponse(200, true, "Thành công"));
        }
        catch (Exception ex)
        {
            return BadRequest(CreateResponse(500, false, ex.Message));
        }
    }
}
