﻿using BusinessObjects.Models;
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.DTOs.TransactionHistoryDTO
{
    public class TransactionHistoryUpdateRequest
    {
        public DateTime transactionDate { get; set; }
        public TransactionType transactionType { get; set; } // Sale, Rent, Lease
        public double price { get; set; }
        public ObjectId? buyerId { get; set; }
        public ObjectId? sellerId { get; set; }
    }
}
