using BusinessObjects.DTOs.UserDTOs;
using BusinessObjects.Models;
using BusinessObjects.QueryObject;
using Microsoft.AspNetCore.Http;
using Repositories.Interfaces;
using Services.Interfaces;

namespace Services.Implements
{
	public class UserService : IUserService
	{
		private readonly IUnitOfWork _unitOfWork;
		private readonly IUserRepository _userRepository;
		private readonly FirebaseService _firebaseService;

		public UserService(IUnitOfWork unitOfWork, IUserRepository userRepository, FirebaseService firebaseService)
		{
			_unitOfWork = unitOfWork;
			_userRepository = userRepository;
			_firebaseService = firebaseService;
			_unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
			_userRepository = userRepository ?? throw new ArgumentNullException(nameof(userRepository));
		}

		public async Task<User> FindUserByEmailAsync(string email)
		{
			return await _userRepository.GetUserByEmailAsync(email);
		}

		public async Task<object> GetSellersAsync(QuerySeller query)
		{
			var result = await _userRepository.GetSellersAsync(query);
			return new
			{
				data = result.data,
				totalCount = result.totalCount,
				totalPages = result.totalPages,
				currentPage = query.pageNumber,
				pageSize = query.pageSize
			};
		}

		public async Task<User> FindUserByIdAsync(string id)
		{
			return await _unitOfWork.Users.GetByIdAsync(id);
		}

		public async Task<UserProfileResponse> GetUserProfileAsync(string id)
		{
			var user = await FindUserByIdAsync(id);
			var user_convert = new UserProfileResponse()
			{
				userName = user.userName,
				about = user.about,
				avatar = user.avatar,
				birthdate = user.birthdate,
				email = user.email,
				fullName = user.fullName,
				joinedAt = user.joinedAt,
				phoneNumber = user.phoneNumber,
				role = user.role,
				status = user.status
			};
			return user_convert;
		}

		public async Task<User> FindUserByPhonenumberAsync(string phonenumber)
		{
			return await _userRepository.GetUserByPhonenumberAsync(phonenumber);
		}

		public async Task<User> FindUserByUsernameAsync(string username)
		{
			return await _userRepository.GetUserByUsernameAsync(username);
		}

		public async Task SaveUserAsync(User user)
		{
			await _unitOfWork.Users.AddAsync(user);
		}

		public async Task UpdateUserAsync(string id, User user)
		{
			//check unique phone and email 

			await _unitOfWork.Users.UpdateAsync(id, user);
		}
		// Thêm phương thức mới để cập nhật profile
		public async Task<UserActionResponse> UpdateUserProfileAsync(string userId, UserUpdateRequest request, IFormFile? avatarFile)
		{
			try
			{
				var userExist = await FindUserByIdAsync(userId);
				if (userExist == null)
				{
					return new UserActionResponse
					{
						statusCode = 404,
						success = false,
						message = "Người dùng không tồn tại"
					};
				}

				bool isUpdated = false;

				// Kiểm tra username
				if (request.userName != userExist.userName && request.userName != null)
				{
					var existingUsername = await FindUserByUsernameAsync(request.userName);
					if (existingUsername != null && existingUsername.id != userId)
					{
						return new UserActionResponse
						{
							statusCode = 400,
							success = false,
							message = "Username đã tồn tại"
						};
					}
					userExist.userName = request.userName;
					isUpdated = true;
				}

				// Kiểm tra email
				if (request.email != userExist.email && !string.IsNullOrEmpty(request.email))
				{
					var existingEmail = await FindUserByEmailAsync(request.email);
					if (existingEmail != null && existingEmail.id != userId)
					{
						return new UserActionResponse
						{
							statusCode = 400,
							success = false,
							message = "Email đã tồn tjai"
						};
					}
					userExist.email = request.email;
					isUpdated = true;
				}

				// Kiểm tra số điện thoại
				if (request.phoneNumber != userExist.phoneNumber && !string.IsNullOrEmpty(request.phoneNumber))
				{
					var existingPhone = await FindUserByPhonenumberAsync(request.phoneNumber);
					if (existingPhone != null && existingPhone.id != userId)
					{
						return new UserActionResponse
						{
							statusCode = 400,
							success = false,
							message = "Số điện thoại đã tồn tjai"
						};
					}
					userExist.phoneNumber = request.phoneNumber;
					isUpdated = true;
				}

				// Cập nhật các thông tin khác
				if (request.fullName != userExist.fullName && request.fullName != null)
				{
					userExist.fullName = request.fullName;
					isUpdated = true;
				}

				// Xử lý avatar
				if (avatarFile != null)
				{
					using var stream = avatarFile.OpenReadStream();
					var avatarUrl = await _firebaseService.UploadAvatarAsync(stream,
						Guid.NewGuid() + Path.GetExtension(avatarFile.FileName));

					if (avatarUrl != userExist.avatar)
					{
						userExist.avatar = avatarUrl;
						isUpdated = true;
					}
				}

				if (request.about != userExist.about && request.about != null)
				{
					userExist.about = request.about;
					isUpdated = true;
				}

				if (request.birthDate != userExist.birthdate && request.birthDate != null)
				{
					userExist.birthdate = request.birthDate;
					isUpdated = true;
				}

				if (request.status != userExist.status && request.status != null)
				{
					userExist.status = request.status;
					isUpdated = true;
				}

				if (!isUpdated)
				{
					return new UserActionResponse
					{
						statusCode = 400,
						success = false,
						message = "Không có gì thay đổi"
					};
				}

				await UpdateUserAsync(userId, userExist);

				return new UserActionResponse
				{
					statusCode = 200,
					success = true,
					message = "Thay đổi thành công"
				};
			}
			catch (Exception ex)
			{
				return new UserActionResponse
				{
					statusCode = 400,
					success = false,
					message = $"Lỗi khi cập nhật: {ex.Message}"
				};
			}
		}

		public async Task<UserRoomateInfoResponse> GetUserRoomateInfoAsync(string userId)
		{
			var user = await FindUserByIdAsync(userId);

			var user_convert = new UserRoomateInfoResponse()
			{
				fullName = user.fullName,
				about = user.about,
				avatar = user.avatar,
				birthdate = user.birthdate,
				budgetMax = user.budgetMax,
				gender = user.gender,
				hasPets = user.hasPets,
				introduction = user.introduction,
				isRoommateActive = user.isRoommateActive,
				isSmoking = user.isSmoking,
				lifestyles = user.lifestyles,
				occupation = user.occupation,
				phoneNumber = user.phoneNumber,
				roleMatching = user.roleMatching,
				sleepingHabit = user.sleepingHabit,
				status = user.status,
				updatedAtRoommateInfo = user.updatedAtRoommateInfo,
				currentLocation = user.currentLocation,
				preferredLocation = user.preferredLocation,
				locationPictures = user.locationPictures
			};
			return user_convert;
		}

		public async Task<User> UpdateUserRoomateInfoAsync(string userId, UserUpdateRoomateInfoRequest request)
		{
			var userExist = await FindUserByIdAsync(userId);

			if (userExist == null)
				throw new KeyNotFoundException($"Không tìm thấy người dùng với ID = {userId}");

			userExist.introduction = request.introduction;
			userExist.occupation = request.occupation;
			userExist.budgetMax = request.budgetMax;
			userExist.hasPets = request.hasPets;
			userExist.isSmoking = request.isSmoking;
			userExist.sleepingHabit = request.sleepingHabit;
			userExist.lifestyles = request.lifestyles;
			userExist.roleMatching = request.roleMatching;
			userExist.currentLocation = request.currentLocation;
			userExist.preferredLocation = request.preferredLocation;
			userExist.locationPictures = request.locationPictures;
			userExist.updatedAtRoommateInfo = DateTime.UtcNow;
			userExist.selfPictures = request.selfPictures;
			userExist.interests = request.interests;

			await UpdateUserAsync(userId, userExist);

			return userExist;
		}

        public async Task<UserActionResponse> ToggleRoommateAsync(string userId, bool isActive)
        {
            try
            {
                var userExist = await FindUserByIdAsync(userId);
                if (userExist == null)
                {
                    return new UserActionResponse
                    {
                        statusCode = 404,
                        success = false,
                        message = "Người dùng không tồn tại"
                    };
                }
                // If user hasn't set roommate info yet and trying to activate            
                if (isActive && userExist.updatedAtRoommateInfo == null)
                {
                    return new UserActionResponse
                    {
                        statusCode = 400,
                        success = false,
                        message = "Vui lòng cập nhật thông tin ở ghép trước khi kích hoạt chế độ ở ghép"
                    };
                }

                // No change if status is the same
                if (userExist.isRoommateActive == isActive)
                {
                    string status = isActive ? "đã được kích hoạt" : "đã bị vô hiệu hóa";
                    return new UserActionResponse
                    {
                        statusCode = 400,
                        success = false,
                        message = $"Chế độ ở ghép {status} rồi"
                    };
                }

                // Update roommate active status
                userExist.isRoommateActive = isActive;
                await UpdateUserAsync(userId, userExist);

                string message = isActive ? "Kích hoạt chế độ ở ghép thành công" : "Vô hiệu hóa chế độ ở ghép thành công";
                return new UserActionResponse
                {
                    statusCode = 200,
                    success = true,
                    message = message
                };
            }
            catch (Exception ex)
            {
                return new UserActionResponse
                {
                    statusCode = 500,
                    success = false,
                    message = $"Lỗi khi cập nhật: {ex.Message}"
                };
            }
        }
	}
}
