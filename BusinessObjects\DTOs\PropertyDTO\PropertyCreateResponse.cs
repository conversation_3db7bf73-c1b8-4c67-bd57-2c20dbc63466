﻿using BusinessObjects.Models;

public class PropertyCreateResponse
{
    public string id { get; set; }
    public UserResponse saler { get; set; }
    public string title { get; set; }
    public string name { get; set; }
    public string description { get; set; }
    public string transactionType { get; set; }
    public string type { get; set; }
    public string status { get; set; }
    public string adminNote { get; set; }
    public string code { get; set; }
    public UserResponse owner { get; set; }
    public LocationResponse location { get; set; }
    public PropertyDetail propertyDetails { get; set; }
    public PriceDetail priceDetails { get; set; }
    public Amenity amenities { get; set; }
    public List<string>? imageUrls { get; set; }
    public List<string>? floorPlanUrls { get; set; }
    public PropertyVideo? video { get; set; }
    public int? yearBuilt { get; set; }
    public List<string>? legalDocumentUrls { get; set; }
    public List<TransactionHistory> transactionHistory { get; set; }
    public bool isFeatured { get; set; }
    public bool isVerified { get; set; }
    public string contactName { get; set; }
    public string contactPhone { get; set; }
    public string contactEmail { get; set; }
    public DateTime createdAt { get; set; }
    public DateTime updatedAt { get; set; }

    public bool? isFavorite { get; set; } = false;
    public bool? isViewed { get; set; } = false;
}

public class LocationResponse
{
    public string address { get; set; }
    public string city { get; set; }
    public string district { get; set; }
    public string ward { get; set; }
    public double? latitude { get; set; }
    public double? longitude { get; set; }
}

public class UserResponse
{
    public string id { get; set; }
    public string fullName { get; set; }
    public string email { get; set; }
    public string? avatar { get; set; }
    public string phoneNumber { get; set; }
    public string gender { get; set; }

}

public class OwnerResponse
{
    public string id { get; set; }
    public string name { get; set; }
    public string phone { get; set; }
    public string email { get; set; }
    public string address { get; set; }
    public Gender? gender { get; set; }
    public string nationality { get; set; }
}

public class TransactionHistoryResponse
{
    public DateTime transactionDate { get; set; }
    public string description { get; set; }
    public decimal amount { get; set; }
}
