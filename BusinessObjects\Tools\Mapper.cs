﻿using BusinessObjects.DTOs.DealDTOs;
using BusinessObjects.DTOs.FacebookDTOs;
using BusinessObjects.DTOs.LeadDTOs;
using BusinessObjects.Models;

namespace Services.Tools
{
    public static class Mapper
    {
        /// <summary>
        /// Maps a <see cref="Deal"/> object to a <see cref="DealResponse"/> object.
        /// </summary>
        /// <param name="deal">The deal domain model to map.</param>
        /// <returns>A DealResponseDTO that contains selected properties from the deal.</returns>
        /// <exception cref="ArgumentNullException">Thrown if the deal is null.</exception>
        public static DealResponse ToDealResponseDto(Deal deal, Lead customer, User? saler)
        {
            if (deal == null)
            {
                throw new ArgumentNullException(nameof(deal));
            }

            // Convert the domain model to a DTO.
            var dealResponse = new DealResponse
            {
                id = deal.id.ToString(), // ObjectId converted to a string
                title = deal.title,
                description = deal.description,
                properties = deal.properties?.Select(property => new DealPropertyResponse
                {
                    id = property.id.ToString(),
                    createdAt = property.createdAt,
                    updatedAt = property.updatedAt,
                }).ToList(),
                status = deal.status.ToString(),
                position = deal.position,
                createdAt = deal.createdAt,
                updatedAt = deal.updatedAt,
                priority = deal.priority.ToString(),
                salesRepId = deal.salesRepId,
                leadId = deal.leadId,

                customer = ToLeadResponseDto(customer),
                saler = new UserResponse
                {
                    id = deal.salesRepId, // Assuming salesRepId is a string representing the user's ID
                    avatar = saler.avatar,
                    fullName = saler.fullName,
                    email = saler.email,
                    phoneNumber = saler.phoneNumber

                },
                notes = deal.notes.Select(note => new DealNoteResponse
                {
                    // Assuming DealNote has an id field of type ObjectId
                    id = note.id.ToString(), // ObjectId converted to a string
                    title = note.title,
                    content = note.content,
                    createdBy = note.createdBy,
                    createdAt = note.createdAt,
                    updatedAt = note.updatedAt
                }).ToList()
            };

            return dealResponse;
        }
        //To Customer Response
        public static LeadResponse ToLeadResponseDto(Lead lead)
        {
            if (lead == null)
            {
                throw new ArgumentNullException(nameof(lead));
            }
            // Convert the domain model to a DTO.
            var leadResponse = new LeadResponse
            {
                id = lead.id.ToString(), // ObjectId converted to a string
                name = lead.name,
                email = lead.email,
                phone = lead.phone,
                address = lead.address,
                source = lead.source,
                score = lead.score,
            };
            return leadResponse;
        }

        /// <summary>
        /// Maps a <see cref="DealsActivityLog"/> object to a <see cref="DealsLogResponse"/> object.
        /// </summary>
        /// <param name="log">The DealsLog domain model to map.</param>
        /// <returns>A DealsLogResponse that contains selected properties from the Deals log.</returns>
        /// <exception cref="ArgumentNullException">Thrown if the log is null.</exception>
        public static DealsLogResponse ToDealsLogResponse(DealsActivityLog log, User user)
        {
            if (log == null)
            {
                throw new ArgumentNullException(nameof(log));
            }
            UserResponse userResponse = new UserResponse
            {
                id = user.id.ToString(),
                fullName = user.fullName,
                email = user.email,
                phoneNumber = user.phoneNumber,
                avatar = user.avatar
            };
            // Convert the domain model to a DTO.
            var response = new DealsLogResponse
            {
                id = log.id.ToString(), // ObjectId converted to a string
                dealId = log.dealId,
                fromStatus = log.fromStatus,
                toStatus = log.toStatus,
                isDeleteAction = log.isDeleteAction,
                timestamp = log.timestamp,
                triggeredBy = userResponse

            };

            return response;
        }

        /// <summary>
        /// Map từ “plain” payload (với entry[]/messaging[]) về DTO phẳng
        /// Chỉ lấy phần tử đầu tiên của entry và messaging.
        /// </summary>
        public static MessengerWebhookPayload ToFlat(
            this MessengerWebhookPayloadPlain plain)
        {
            if (plain == null)
                throw new ArgumentNullException(nameof(plain));

            var entry = plain.entry?
                .FirstOrDefault()
                ?? throw new InvalidOperationException("Payload must have at least 1 entry");

            var messaging = entry.messaging?
                .FirstOrDefault()
                ?? throw new InvalidOperationException("Entry must have at least 1 messaging event");

            return new MessengerWebhookPayload
            {
                sender = new BusinessObjects.DTOs.FacebookDTOs.MessengerSender
                {
                    id = messaging.sender.id
                },
                recipient = new BusinessObjects.DTOs.FacebookDTOs.MessengerRecipient
                {
                    id = messaging.recipient.id
                },
                timestamp = messaging.timestamp,
                message = new BusinessObjects.DTOs.FacebookDTOs.MessengerMessage
                {
                    mid = messaging.message.mid,
                    text = messaging.message.text,
                    isEcho = messaging.message.isEcho,
                    attachments = messaging.message.attachments?.Select(a => new BusinessObjects.DTOs.FacebookDTOs.MessengerAttachment
                    {
                        type = a.type,
                        payload = new BusinessObjects.DTOs.FacebookDTOs.MessengerAttachmentPayload
                        {
                            url = a.payload.url,
                            //attachmentId = a.payload.attachmentId
                        }
                    }).ToList()
                }
            };
        }

    }
}
