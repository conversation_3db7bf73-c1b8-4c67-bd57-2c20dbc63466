﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System.ComponentModel.DataAnnotations;

namespace BusinessObjects.Models
{
    public class GoogleCredentialModel
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string id { get; set; } = ObjectId.GenerateNewId().ToString();

        [BsonElement("userId")]
        [Required]
        public string userId { get; set; }

        [BsonElement("accessToken")]
        [Required]
        [StringLength(2048)]
        public string accessToken { get; set; }

        [BsonElement("refreshToken")]
        [StringLength(512)]
        public string? refreshToken { get; set; }

        [BsonElement("calendarId")]
        [StringLength(255)]
        public string calendarId { get; set; } = "primary";

        [BsonElement("tokenExpiry")]
        public DateTime? tokenExpiry { get; set; }

        [BsonElement("scope")]
        [StringLength(500)]
        public string? scope { get; set; }

        [BsonElement("tokenType")]
        public string tokenType { get; set; } = "Bearer";

        [BsonElement("isActive")]
        public bool isActive { get; set; } = true;

        [BsonElement("createdAt")]
        public DateTime createdAt { get; set; } = DateTime.UtcNow;

        [BsonElement("updatedAt")]
        public DateTime updatedAt { get; set; } = DateTime.UtcNow;

        // Metadata
        [BsonElement("lastUsed")]
        public DateTime? lastUsed { get; set; }

        [BsonElement("userAgent")]
        public string? userAgent { get; set; }

        [BsonElement("ipAddress")]
        public string? ipAddress { get; set; }
    }
}
