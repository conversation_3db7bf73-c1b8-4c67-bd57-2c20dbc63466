﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using MongoDB.Driver;
using Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Repositories.Implements
{
    public class SearchRepository : GenericRepository<Search>, ISearchRepository
    {
        private readonly IMongoCollection<Search> _searchs;

        public SearchRepository(RevoLandDbContext context) : base(context, "Searchs")
        {
            _searchs = context.GetCollection<Search>("Searchs");
        }

    }
}
