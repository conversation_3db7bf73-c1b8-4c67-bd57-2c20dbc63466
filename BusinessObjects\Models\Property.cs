﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace BusinessObjects.Models
{
    public class Property
    {
        public readonly object TransactionHistory;

        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public ObjectId id { get; set; }

        [BsonElement("title")]
        public string? title { get; set; }

        [BsonElement("salerId")]
        public string? salerId { get; set; }

        [BsonElement("name")]
        public string? name { get; set; }

        [BsonElement("slug")]
        public string? slug { get; set; }

        [BsonElement("description")]
        public string? description { get; set; }

        [BsonElement("transactionType")]
        [BsonRepresentation(BsonType.String)]
        public PropertyTransactionType? transactionType { get; set; }

        [BsonElement("type")]
        [BsonRepresentation(BsonType.String)]
        public PropertyType? type { get; set; }

        [BsonElement("status")]
        [BsonRepresentation(BsonType.String)]
        public PropertyStatus? status { get; set; }

        [BsonElement("adminNote")]
        public string? adminNote { get; set; }

        [BsonElement("code")]
        public string? code { get; set; }

        [BsonElement("ownerId")]
        [BsonRepresentation(BsonType.ObjectId)]
        public string? ownerId { get; set; }

        [BsonElement("location")]
        public Location? location { get; set; }

        [BsonElement("propertyDetails")]
        public PropertyDetail? propertyDetails { get; set; }

        [BsonElement("priceDetails")]
        public PriceDetail? priceDetails { get; set; }

        [BsonElement("amenities")]
        public Amenity? amenities { get; set; }

        [BsonElement("images")]
        public List<string>? images { get; set; }
        [BsonElement("floorPlans")]
        public List<string>? floorPlans { get; set; } = new List<string>();
        [BsonElement("video")]
        public PropertyVideo? video { get; set; }

        [BsonElement("yearBuilt")]
        public int? yearBuilt { get; set; }

        [BsonElement("legalDocuments")]
        public List<string>? legalDocuments { get; set; }

        [BsonElement("transactionHistory")]
        public List<TransactionHistory>? transactionHistory { get; set; }

        [BsonElement("createdAt")]
        public DateTime createdAt { get; set; } = DateTime.UtcNow;

        [BsonElement("updatedAt")]
        public DateTime updatedAt { get; set; } = DateTime.UtcNow;

        [BsonElement("contactName")]
        public string? contactName { get; set; }

        [BsonElement("contactPhone")]
        public string? contactPhone { get; set; }

        [BsonElement("contactEmail")]
        public string? contactEmail { get; set; }

        [BsonElement("isFeatured")]
        public bool isFeatured { get; set; }


        [BsonElement("isVerified")]
        public bool isVerified { get; set; }
        [BsonElement("isDraft")]
        public bool isDraft { get; set; } = false;

    }

    public class PropertyVideo
    {
        [BsonElement("videoUrl")]
        public string videoUrl { get; set; }
        [BsonElement("title")]
        public string title { get; set; }
        [BsonElement("description")]
        public string description { get; set; }
    }

    public enum PropertyTransactionType
    {
        [BsonElement("forRent")] ForRent,
        [BsonElement("forSale")] ForSale,
        [BsonElement("project")] Project
    }

    public enum PropertyType
    {
        [BsonElement("apartment")] Apartment,
        [BsonElement("miniServiceApartment")] MiniServiceApartment,
        [BsonElement("commercialTownhouse")] CommercialTownhouse,
        [BsonElement("motel")] Motel,
        [BsonElement("airbnb")] Airbnb,
        [BsonElement("house")] House,
        [BsonElement("townhouse")] Townhouse,
        [BsonElement("villa")] Villa,
        [BsonElement("shopHouse")] ShopHouse,
        [BsonElement("landPlot")] LandPlot,
        [BsonElement("projectLand")] ProjectLand,
        [BsonElement("office")] Office,
        [BsonElement("warehouse")] Warehouse,
        [BsonElement("factory")] Factory,
        [BsonElement("industrial")] Industrial,
        [BsonElement("hotel")] Hotel,
        [BsonElement("socialHousing")] SocialHousing,
        [BsonElement("newUrbanArea")] NewUrbanArea,
        [BsonElement("ecoResort")] EcoResort,
        [BsonElement("other")] Other,
        [BsonElement("other")] Project,

    }

    public enum PropertyStatus
    {
        [BsonElement("available")] Available,
        [BsonElement("pending")] Pending,
        [BsonElement("sold")] Sold,
        [BsonElement("rented")] Rented
    }

}
