﻿using BusinessObjects.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.DTOs.LeadDTOs
{
    public class LeadResponse
    {
        public string id { get; set; }
        public string? name { get; set; }
        public string? email { get; set; }
        public string? phone { get; set; }
        public string? address { get; set; }
        public LeadSource? source { get; set; }
        public LeadScore? score { get; set; }
        public List<LeadSeller>? assignedTo { get; set; }
    }

    public class LeadSeller
    {
        public string? id { get; set; }
        public string? name { get; set; }
        public string? email { get; set; }
        public string? phone { get; set; }
        public string? avatar { get; set; }
        public string? about { get; set; }
        public DateTime? birthdate { get; set; }
    }
}
