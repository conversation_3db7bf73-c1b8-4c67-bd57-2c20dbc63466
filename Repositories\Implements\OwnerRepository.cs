﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using MongoDB.Driver;
using Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using System.Text;
using System.Threading.Tasks;

namespace Repositories.Implements
{
    public class OwnerRepository : GenericRepository<Owner>, IOwnerRepository
    {

        private readonly IMongoCollection<Owner> _owners;

        public OwnerRepository(RevoLandDbContext context) : base(context, "Owners")
        {
            _owners = context.GetCollection<Owner>("Owners");
        }

        public async Task<Owner> GetByUserIdAsync(string userId)
        {
            var filter = Builders<Owner>.Filter.Eq(o => o.userId, userId);
            return await _owners.Find(filter).FirstOrDefaultAsync();
        }

        public async Task<Owner> GetOwnerByPhoneAsync(string phone)
        {
            var filter = Builders<Owner>.Filter.Eq(o => o.phone, phone);
            return await _owners.Find(filter).FirstOrDefaultAsync();
        }
    }
}
