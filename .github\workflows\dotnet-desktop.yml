name: .NET and Deploy

on:
  push:
    branches: [ "main" ]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Cache .NET packages
      uses: actions/cache@v3
      with:
        path: ~/.nuget/packages
        key: ${{ runner.os }}-nuget-${{ hashFiles('**/*.csproj') }}
        restore-keys: |
          ${{ runner.os }}-nuget-
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: 8.0.x

    - name: REAL-ESTATE-PLATFORM restore dependencies
      run: |
        dotnet restore
    
    - name: REAL-ESTATE-PLATFORM build
      run: |
        dotnet build --no-restore

    - name: REAL-ESTATE-PLATFORM test
      run: |
        dotnet test --no-build --verbosity normal
  deploy:
    name: Deploy to EC2
    needs: build
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy to EC2 instance
        env:
          PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}

        run: |
          echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
          
          ssh -o StrictHostKeyChecking=no -i private_key <EMAIL> << 'EOF'
            
            set -e 
            echo "Install Docker"
            if ! command -v docker &> /dev/null
            then
              echo "Docker not found, installing..."
              sudo yum update -y
              sudo yum install -y docker
              sudo systemctl start docker
              sudo systemctl enable docker
            else
              echo "Docker is already installed."
            fi
            echo "Docker Version"
            docker --version 

            echo "Grant current user permission"
            sudo usermod -aG docker ec2-user
            newgrp docker


            echo "Download Docker Compose Plugin"
            DOCKER_CONFIG=${DOCKER_CONFIG:-$HOME/.docker}
            mkdir -p $DOCKER_CONFIG/cli-plugins
            curl -SL https://github.com/docker/compose/releases/download/v2.29.6/docker-compose-linux-x86_64 -o $DOCKER_CONFIG/cli-plugins/docker-compose 
            chmod +x $DOCKER_CONFIG/cli-plugins/docker-compose
            docker compose version


            echo "Download Git"
            if ! command -v git &> /dev/null
            then
              echo "Git not found, installing...."
              sudo yum install -y git
            else
              echo "Git is already installed."
            fi
            git --version


            echo "Build and publish endpoint"
            mkdir -p realestateapp
            cd realestateapp
            if [ ! -d "real-estate-platform" ]; then
              git clone https://<EMAIL>/kztoan01/real-estate-platform.git
            fi          
            cd real-estate-platform
            git remote set-url origin https://<EMAIL>/kztoan01/real-estate-platform.git
            git checkout main
            git pull    
            docker compose stop quinland-service-api
            docker compose build quinland-service-api
            docker compose up -d quinland-service-api

            
          EOF
      - name: Rollback on failure
        if: failure()
        run: |
          ssh -o StrictHostKeyChecking=no -i private_key <EMAIL> << 'EOF'
            cd realestateapp/REAL-ESTATE-PLATFORM
            docker compose down
            docker compose up -d previous
          EOF