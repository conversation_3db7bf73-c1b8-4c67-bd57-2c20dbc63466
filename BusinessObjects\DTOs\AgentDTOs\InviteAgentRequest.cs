﻿using BusinessObjects.Models;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.DTOs.AgentDTOs
{
	public class InviteAgentRequest
	{
		public string email { get; set; }
		public int validHours { get; set; } = 72;
		[BsonRepresentation(BsonType.String)]
		public AgentCompanyRole role { get; set; }
	}
}
