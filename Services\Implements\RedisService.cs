﻿using Services.Interfaces;
using StackExchange.Redis;
using System;
using System.Threading.Tasks;

public class RedisService : IRedisService
{
    private readonly IDatabase _db;
    public RedisService(IConnectionMultiplexer connectionMultiplexer)
    {
        _db = connectionMultiplexer.GetDatabase();
    }
    public async Task SetStringAsync(string key, string value, TimeSpan? expiry = null)
    {
        await _db.StringSetAsync(key, value, expiry);
    }
    public async Task<string?> GetStringAsync(string key)
    {
        return await _db.StringGetAsync(key);
    }
    public async Task DeleteKeyAsync(string key)
    {
        await _db.KeyDeleteAsync(key);
    }
}
