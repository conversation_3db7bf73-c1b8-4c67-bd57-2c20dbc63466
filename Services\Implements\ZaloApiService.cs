﻿using Microsoft.Extensions.Configuration;
using Services.Interfaces;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Services.Implements
{
    public class ZaloApiService : IZaloApiService
    {
        private readonly IZaloTokenService _zaloTokenService;
        private readonly HttpClient _httpClient;
        private readonly IRedisService _redisService;
        private readonly IConfiguration _config;

        public ZaloApiService(HttpClient httpClient, IZaloTokenService zaloTokenService, IRedisService redisService, IConfiguration config)
        {
            _zaloTokenService = zaloTokenService;
            _httpClient = httpClient;
            _redisService = redisService;
            _config = config;
        }

        public async Task SendTextMessageAsync(string userId, string message)
        {
            var payload = new
            {
                recipient = new { user_id = userId },
                message = new { text = message }
            };

            var request = await CreateRequestWithAccessTokenAsync(
                HttpMethod.Post,
                "https://openapi.zalo.me/v3.0/oa/message/cs",
                JsonSerializer.Serialize(payload)
            );

            var response = await _httpClient.SendAsync(request);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                throw new Exception($"Gửi tin nhắn tới Zalo thất bại: {response.StatusCode} - {responseContent}");
            }
        }

        public async Task<ZaloUserProfile?> GetUserProfileAsync(string userId)
        {
            var query = $"https://openapi.zalo.me/v3.0/oa/user/detail?data={{\"user_id\":\"{userId}\"}}";

            var request = await CreateRequestWithAccessTokenAsync(HttpMethod.Get, query);

            var response = await _httpClient.SendAsync(request);
            var content = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                throw new Exception($"Lấy thông tin người dùng Zalo thất bại: {response.StatusCode} - {content}");
            }

            var result = JsonSerializer.Deserialize<ZaloGetProfileResponse>(content);

            return result?.data;
        }


        private async Task<HttpRequestMessage> CreateRequestWithAccessTokenAsync(HttpMethod method, string url, string? body = null)
        {
            var accessToken = await _zaloTokenService.GetValidAccessTokenAsync();

            var request = new HttpRequestMessage(method, url);

            request.Headers.Add("access_token", accessToken);

            if (!string.IsNullOrEmpty(body))
            {
                request.Content = new StringContent(body, Encoding.UTF8, "application/json");
            }
            return request;
        }

        public async Task SendOTPAsync(string phone)
        {
            if (phone.StartsWith("0"))
            {
                phone = "84" + phone.Substring(1);
            }
            var random = new Random();
            var otpCode = random.Next(100000, 999999).ToString();
            var redisKey = $"OTP:{phone}";
            string? cachedOtp = await _redisService.GetStringAsync(redisKey);
            if (cachedOtp != null)
            {
                throw new Exception("Mã OTP đã được gửi. Vui lòng chờ trong giây lát trước khi gửi yêu cầu mới.");
            }

            await _redisService.SetStringAsync(redisKey, otpCode, TimeSpan.FromMinutes(1));
            var payload = new
            {
                phone = phone,
                template_id = "434532",
                template_data = new
                {
                    otp = otpCode,
                    minute = 1
                },
                tracking_id = Guid.NewGuid().ToString()
            };

            var request = await CreateRequestWithAccessTokenAsync(
                HttpMethod.Post,
                "https://business.openapi.zalo.me/message/template",
                JsonSerializer.Serialize(payload)
            );

            var response = await _httpClient.SendAsync(request);
            var responseContent = await response.Content.ReadAsStringAsync();
            var zaloResponse = JsonSerializer.Deserialize<ZNSResponse>(responseContent);

            if (zaloResponse?.error != 0)
            {
                throw new Exception($"Gửi mã OTP Zalo thất bại: {response.StatusCode} - {zaloResponse.message}");
                //sau nay tao callback neu gui otp bang zalo that bai thi dung sms
            }

        }

    }

    public class ZaloGetProfileResponse
    {
        [JsonPropertyName("error")]
        public int error { get; set; }

        [JsonPropertyName("message")]
        public string message { get; set; }

        [JsonPropertyName("data")]
        public ZaloUserProfile? data { get; set; }
    }

    public class ZaloUserProfile
    {
        [JsonPropertyName("user_id")]
        public string userId { get; set; } = string.Empty;

        [JsonPropertyName("display_name")]
        public string displayName { get; set; } = string.Empty;

        [JsonPropertyName("avatar")]
        public string avatar { get; set; } = string.Empty;
    }

    public class ZNSResponse
    {
        public int error { get; set; }
        public string message { get; set; }
        public Data data { get; set; }
    }

    public class Data
    {
        public string msg_id { get; set; }
        public string sent_time { get; set; }
        public string sending_mode { get; set; }
        public Quota quota { get; set; }
    }

    public class Quota
    {
        public string dailyQuota { get; set; }
        public string remainingQuota { get; set; }
    }
}
