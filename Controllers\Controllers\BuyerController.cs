﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.BuyerDTOs;
using BusinessObjects.Models;
using BusinessObjects.QueryObject;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Services.Interfaces;

namespace Controllers.Controllers
{
	[Route("api/buyer")]
	[ApiController]
	public class BuyerController : ControllerBase
	{
		private readonly IBuyerService _buyerService;

		public BuyerController(IBuyerService buyerService)
		{
			_buyerService = buyerService;
		}
		private ApiResponse CreateResponse(int code, bool status, string message, object data = null)
		{
			return new ApiResponse { code = code, status = status, message = message, data = data };
		}

		[Authorize]
		[HttpGet]
		public async Task<IActionResult> GetAllBuyers([FromQuery] QueryBuyer query)
		{
			try
			{
				if (!ModelState.IsValid)
					return BadRequest(CreateResponse(400, false, "Invalid model", ModelState));

				var tenants = await _buyerService.GetAllBuyersAsync(query);
				return Ok(CreateResponse(200, true, "List of buyer retrieved successfully", tenants));
			}
			catch (Exception ex)
			{
				return StatusCode(500, CreateResponse(500, false, "Internal server error: " + ex.Message));
			}
		}
		[Authorize]
		[HttpGet("{id}")]
		public async Task<IActionResult> GetBuyerById(string id)
		{
			try
			{
				var tenant = await _buyerService.GetBuyerByIdAsync(id);
				if (tenant == null)
					return NotFound(CreateResponse(404, false, "Buyer not found"));

				return Ok(CreateResponse(200, true, "Buyer retrieved successfully", tenant));
			}
			catch (Exception ex)
			{
				return StatusCode(500, CreateResponse(500, false, "Internal server error: " + ex.Message));
			}
		}
		[Authorize]
		[HttpGet("phone")]
		public async Task<IActionResult> GetBuyerByPhone(string phone)
		{
			try
			{
				var tenant = await _buyerService.GetBuyerByPhoneAsync(phone);
				if (tenant == null)
					return NotFound(CreateResponse(404, false, "Buyer not found"));

				return Ok(CreateResponse(200, true, "Buyer retrieved successfully", tenant));
			}
			catch (Exception ex)
			{
				return StatusCode(500, CreateResponse(500, false, "Internal server error: " + ex.Message));
			}
		}
		[Authorize]
		[HttpPost]
		public async Task<IActionResult> AddBuyer([FromBody] BuyerCreateRequest buyer)
		{
			try
			{
				var createdBuyer = await _buyerService.AddBuyerAsync(buyer);
				await _buyerService.CreateUserFromBuyerAsync(buyer);

				return CreatedAtAction(nameof(GetBuyerById), new { id = createdBuyer.id }, new ApiResponse { code = 201, status = true, message = "Buyer added successfully", data = createdBuyer });
			}
            catch (ArgumentException argEx)
            {
                return BadRequest(CreateResponse(400, false, argEx.Message));
            }
            catch (Exception ex)
			{
				return StatusCode(500, CreateResponse(500, false, "Internal server error: " + ex.Message));
			}
		}
		[Authorize]
		[HttpPut("{id}")]
		public async Task<IActionResult> UpdateBuyer(string id, [FromForm] BuyerUpdateRequest buyer)
		{
			try
			{
				var updated = await _buyerService.UpdateBuyerAsync(id, buyer);
				if (!updated)
					return NotFound(CreateResponse(404, false, "Buyer does not exist or no changes were made"));

				return Ok(CreateResponse(200, true, "Buyer updated successfully"));
			}
			catch (Exception ex)
			{
				return StatusCode(500, CreateResponse(500, false, "Internal server error: " + ex.Message));
			}
		}
		[Authorize]
		[HttpDelete("{id}")]
		public async Task<IActionResult> DeleteBuyer(string id)
		{
			try
			{
				await _buyerService.DeleteBuyerAsync(id);
				return Ok(CreateResponse(200, true, "Buyer deleted successfully"));
			}
			catch (Exception ex)
			{
				return StatusCode(500, CreateResponse(500, false, "Internal server error: " + ex.Message));
			}
		}
	}
}
