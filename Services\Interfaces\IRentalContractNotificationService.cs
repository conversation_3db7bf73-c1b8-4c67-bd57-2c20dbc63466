using System.Threading.Tasks;

namespace Services.Interfaces
{
    public interface IRentalContractNotificationService
    {
        Task SendNotificationAsync(string userId, string message, string type);
        Task SendEmailNotificationAsync(string userId, string message);
        Task SendSmsNotificationAsync(string userId, string message);
        Task SendInAppNotificationAsync(string userId, string message, string type);
    }
} 