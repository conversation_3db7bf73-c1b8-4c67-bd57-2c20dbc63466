﻿using BusinessObjects.Models;
using Repositories.Interfaces;
using Services.Interface;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Services.Implements
{
    public class PropertyDetailService : IPropertyDetailService
    {
        private readonly IUnitOfWork _unitOfWork;

        public PropertyDetailService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<PropertyDetail>> GetAllPropertyDetailsAsync()
        {
            return await _unitOfWork.PropertyDetails.GetAllAsync();
        }

        public async Task<PropertyDetail> GetPropertyDetailByIdAsync(string id)
        {
            return await _unitOfWork.PropertyDetails.GetByIdAsync(id);
        }

        public async Task<PropertyDetail> AddPropertyDetailAsync(PropertyDetail propertyDetail)
        {
            await _unitOfWork.PropertyDetails.AddAsync(propertyDetail);
            await _unitOfWork.SaveAsync();
            return propertyDetail;
        }

        public async Task<PropertyDetail> UpdatePropertyDetailAsync(string id, PropertyDetail propertyDetail)
        {
            await _unitOfWork.PropertyDetails.UpdateAsync(id, propertyDetail);
            await _unitOfWork.SaveAsync();
            return propertyDetail;
        }

        public async Task<bool> DeletePropertyDetailAsync(string id)
        {
            await _unitOfWork.PropertyDetails.DeleteAsync(id);
            await _unitOfWork.SaveAsync();
            return true;
        }
    }
}
