﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Repositories.Implements
{
    public class TransactionHistoryRepository : GenericRepository<TransactionHistory>, ITransactionHistoryRepository
    {
        private readonly RevoLandDbContext _context;

        public TransactionHistoryRepository(RevoLandDbContext context) : base(context, "Transaction_histories")
        {
            _context = context;
        }
    }
}
