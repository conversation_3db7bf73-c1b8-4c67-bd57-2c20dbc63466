﻿using BusinessObjects.DTOs;
using BusinessObjects.Models;
using BusinessObjects.QueryObject;

namespace Repositories.Interfaces
{
    public interface IUserRepository : IGenericRepository<User>
    {
        Task<User> GetUserByUsernameAsync(string username);
        Task<User> GetUserByEmailAsync(string email);
        Task<User> GetUserByPhonenumberAsync(string phonenumber);
        Task<List<User>> GetByIdsAsync(List<string> userIds);
        Task<PagedResult<User>> GetSellersAsync(QuerySeller query);
        Task<List<string>> GetAdminUserIdsAsync();

        Task<List<User>> GetUsersByIdsAsync(List<string> userIds);
    }
}
