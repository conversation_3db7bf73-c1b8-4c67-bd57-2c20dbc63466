﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.Models
{
	public class TeamAgentLink
	{
		[BsonId]
		[BsonRepresentation(BsonType.ObjectId)]
		public string id { get; set; } = ObjectId.GenerateNewId().ToString();

		[BsonRepresentation(BsonType.ObjectId)]
		[Required]
		public string teamId { get; set; }

		[BsonRepresentation(BsonType.ObjectId)]
		[Required]
		public string agentId { get; set; }

		[BsonRepresentation(BsonType.String)]
		public AgentTeamRole role { get; set; }

		public bool isAccepted { get; set; } = false;

		public DateTime invitedAt { get; set; } = DateTime.UtcNow.AddHours(7);
		public DateTime? acceptedAt { get; set; }
		public DateTime? removedAt { get; set; }
	}

	public enum AgentTeamRole
	{
		Admin,
		Sales,
		TeamLead
	}
}
