﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using Microsoft.EntityFrameworkCore;
using MongoDB.Driver;
using Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Repositories.Implements
{
    public class RefreshTokenRepository:GenericRepository<RefreshToken>,IRefreshTokenRepository
    {
        private readonly IMongoCollection<RefreshToken> _refreshtoken;

        public RefreshTokenRepository(RevoLandDbContext context) : base(context, "RefrehTokens")
        {
            _refreshtoken = context.RefreshTokens;
        }

        public async Task<RefreshToken> GetRefreshToken(string refreshtoken)
        {
            try
            {
                var filter = Builders<RefreshToken>.Filter.Eq(x => x.token, refreshtoken);
                return await _refreshtoken.Find(filter).FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
    }
}
