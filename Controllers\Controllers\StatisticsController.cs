﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.StatisticDTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Services.Interfaces;

namespace Controllers.Controllers
{
    [Route("api/statistics")]
    [ApiController]
    public class StatisticsController : ControllerBase
    {
        private readonly IStatisticsService _statisticsService;
        public StatisticsController(IStatisticsService statisticsService)
        {
            _statisticsService = statisticsService;
        }
        private ApiResponse CreateResponse(int code, bool status, string message, object data = null)
        {
            return new ApiResponse { code = code, status = status, message = message, data = data };
        }

        [HttpGet("topKPIs")]
        [Authorize(Roles = "Admin,Saler")]
        public async Task<IActionResult> GetDashboardStats([FromQuery] StatisticsQuery query)
        {
            try
            {
                var statistics = await _statisticsService.GetTopKPIsAsync(query);
                return Ok(CreateResponse(200, true, "Dashboard statistics fetched successfully", statistics));
            }
            catch (UnauthorizedAccessException)
            {
                return Forbid();
            }
            catch (ArgumentException ex)
            {
                return BadRequest(CreateResponse(400, false, ex.Message));
            }
            catch (InvalidDataException ex)
            {
                return NotFound(CreateResponse(404, false, ex.Message));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateResponse(500, false, "An error occurred while fetching dashboard statistics"));
            }
        }
        [HttpGet("todayTasksAndScheduledPropertyViewings")]
        [Authorize(Roles = "Saler")]
        public async Task<IActionResult> GetTodayTaskAndScheduledPropertyViewings()
        {
            try
            {
                //get user id from claims
                var salerId = User.Claims.FirstOrDefault(c => c.Type == "UserId")?.Value;
                var statistics = await _statisticsService.GetTodayTaskAndScheduledPropertyViewings(salerId);
                return Ok(CreateResponse(200, true, "Today's tasks and scheduled property viewings fetched successfully", statistics));
            }
            catch (UnauthorizedAccessException)
            {
                return Forbid();
            }
            catch (ArgumentException ex)
            {
                return BadRequest(CreateResponse(400, false, ex.Message));
            }
            catch (InvalidDataException ex)
            {
                return NotFound(CreateResponse(404, false, ex.Message));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateResponse(500, false, "An error occurred while fetching today's tasks and scheduled property viewings"));
            }
        }

    }
}
