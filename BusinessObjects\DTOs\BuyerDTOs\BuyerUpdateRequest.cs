﻿using MongoDB.Bson.Serialization.Attributes;
using System.ComponentModel.DataAnnotations;

namespace BusinessObjects.DTOs.BuyerDTOs
{
	public class BuyerUpdateRequest
	{
		public string? name { get; set; }

		public string? phone { get; set; }

		public string? email { get; set; }

		public string? address { get; set; }

		public List<string>? idVerifications { get; set; }

		public string? bankCode { get; set; }

		public string? bankName { get; set; }

		public string? taxCode { get; set; }

		public string? faxCode { get; set; }

	}
}
