﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.DTOs;
using BusinessObjects.DTOs.CompanyDTOs;
using BusinessObjects.Models;
using BusinessObjects.Settings;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using MongoDB.Bson;
using Repositories.Interfaces;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;

namespace Repositories.Tools
{
    public class TokenTool
    {
        private readonly KeySettings _appsettings;
        private readonly IUserRepository _userRepository;
        private readonly RevoLandDbContext _context;
        public TokenTool(IOptionsMonitor<KeySettings> appsettings, IUserRepository userRepository, RevoLandDbContext context)
        {
            _appsettings = appsettings.CurrentValue;
            _userRepository = userRepository;
            _context = context;
        }

        public async Task<TokenSetting> GenerateToken(UserDTO user)
        {
            var jwtTokenHandler = new JwtSecurityTokenHandler();
            var secretKeyBytes = Encoding.UTF8.GetBytes(_appsettings.SecretKey);

            var claims = new List<Claim>
            {
                new Claim("UserID", user.Id.ToString()),
                new Claim(ClaimTypes.Name, user.userName),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                new Claim("JoinedAt",user.joinedAt.ToString()),
                new Claim("FullName",user.fullName),
                new Claim("Status", user.status.ToString()),
                new Claim("TokenId", Guid.NewGuid().ToString()),
                new Claim(ClaimTypes.Role, user.role.ToString())
            };

            var tokenDescription = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.Now.AddDays(8),
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(secretKeyBytes), SecurityAlgorithms.HmacSha512Signature)
            };

            var token = jwtTokenHandler.CreateToken(tokenDescription);
            var accessToken = jwtTokenHandler.WriteToken(token);
            var refreshToken = GenerateRefreshToken();

            var refreshTokenEntity = new RefreshToken
            {
                id = ObjectId.GenerateNewId().ToString(),
                jwtId = token.Id,
                userId = user.Id.ToString(),
                token = refreshToken,
                isUsed = false,
                isRevoked = false,
                issuedAt = DateTime.UtcNow,
                expiredAt = DateTime.UtcNow.AddDays(8)
            };
            await _context.RefreshTokens.InsertOneAsync(refreshTokenEntity);

            var tokenResponse = new TokenSetting
            {
                AccessToken = accessToken,
                RefreshToken = refreshToken

            };

            return tokenResponse;

        }

        private string GenerateRefreshToken()
        {
            var random = new byte[32];
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(random);
                return Convert.ToBase64String(random);
            }
        }

		public async Task<TokenSetting> GenerateCompanyToken(CompanyDTO company)
		{
			var jwtTokenHandler = new JwtSecurityTokenHandler();
			var secretKeyBytes = Encoding.UTF8.GetBytes(_appsettings.SecretKey);

			var claims = new List<Claim>
			{
				new Claim("CompanyID", company.id.ToString()),
				new Claim(ClaimTypes.Name, company.name),
				new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
				new Claim("OwnerId",company.ownerId.ToString()),
				new Claim("TokenId", Guid.NewGuid().ToString())
			};

			var tokenDescription = new SecurityTokenDescriptor
			{
				Subject = new ClaimsIdentity(claims),
				Expires = DateTime.Now.AddDays(8),
				SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(secretKeyBytes), SecurityAlgorithms.HmacSha512Signature)
			};

			var token = jwtTokenHandler.CreateToken(tokenDescription);
			var accessToken = jwtTokenHandler.WriteToken(token);
			var refreshToken = GenerateRefreshToken();

			var refreshTokenEntity = new RefreshCompanyToken
			{
				id = ObjectId.GenerateNewId().ToString(),
				jwtId = token.Id,
				companyId = company.id.ToString(),
				token = refreshToken,
				isUsed = false,
				isRevoked = false,
				issuedAt = DateTime.UtcNow,
				expiredAt = DateTime.UtcNow.AddDays(8)
			};
			await _context.RefreshCompanyTokens.InsertOneAsync(refreshTokenEntity);

			var tokenResponse = new TokenSetting
			{
				AccessToken = accessToken,
				RefreshToken = refreshToken

			};
			return tokenResponse;
		}
	}
}
