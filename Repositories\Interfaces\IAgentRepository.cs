﻿using BusinessObjects.DTOs.AgentDTOs;
using BusinessObjects.Models;
using Repositories.Implements;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Repositories.Interfaces
{
	public interface IAgentRepository : IGenericRepository<Agent>
	{
		Task AssignAgentToTeam(AddAgentToTeamRequest request);
		Task DeleteAgentFromTeam(string teamId, string agentId);
		Task<List<Agent>> GetAllAgentsByCompanyIdAsync(string companyId);
		Task<Agent> GetAgentByEmailAsync(string email);
	}
}
