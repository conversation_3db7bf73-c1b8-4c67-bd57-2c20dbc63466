﻿namespace BusinessObjects.DTOs.StatisticDTOs
{
    public class StatisticsResponse
    {
        public Dictionary<string, object> dictionary { get; set; } = new();

        public StatisticsMetadata metadata { get; set; }
    }

    public class StatisticsMetadata
    {
        public DateTime generatedAt { get; set; } = DateTime.UtcNow;
        public DatePeriod period { get; set; }
        public Dictionary<string, object> filters { get; set; }
        public string version { get; set; } = "1.0.0";
    }

    public class DatePeriod
    {
        public DateTime from { get; set; }
        public DateTime to { get; set; }
    }
}
