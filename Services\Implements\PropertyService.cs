using BusinessObjects.DTOs.PropertyDTO;
using BusinessObjects.Models;
using BusinessObjects.QueryObject;
using Microsoft.AspNetCore.Http;
using NPOI.XSSF.UserModel;
using Repositories.Interfaces;
using Services.Interface;
using Services.Interfaces;
using System.Data;
using System.Globalization;
using System.Security.Claims;
using System.Text;
using System.Text.RegularExpressions;

namespace Services.Implements
{
    public class PropertyService : IPropertyService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ISearchService _searchService;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IPropertyViewHistoryService _propertyViewHistory;
        private readonly IDraftPropertyService _draftPropertyService;
        private static readonly Dictionary<string, string> ColumnMappings = new()
        {
            { "Property Name", "name" },
            { "Owner's Name", "owner.name" },
            { "Phone", "owner.phone" },
            { "Email", "owner.email" },
            { "Owner's Address", "owner.address" },
            { "Property Type", "type" },
            { "House Type", "houseType" },
            { "Price", "price_details.sale_price" },
            { "Gender", "owner.gender" },
            { "Nationality", "owner.nationality" }
        };

        public PropertyService(IUnitOfWork unitOfWork, ISearchService searchService, IHttpContextAccessor httpContextAccessor, IPropertyViewHistoryService propertyViewHistoryService, IDraftPropertyService draftPropertyService)
        {
            _unitOfWork = unitOfWork;
            _searchService = searchService;
            _httpContextAccessor = httpContextAccessor;
            _propertyViewHistory = propertyViewHistoryService;
            _draftPropertyService = draftPropertyService;
        }

        public async Task<PropertySearchResponse> GetAllPropertiesAsync(QueryProperty query)
        {
            if (query.searchTerm == null)
            {
                query.searchTerm = "";
            }
            else
            {
                _searchService.AddSearchAsync(query);
            }

            string role = _httpContextAccessor.HttpContext?.User.FindFirst(ClaimTypes.Role)?.Value;
            var userId = _httpContextAccessor.HttpContext?.User?.FindFirstValue("UserID");

            // Lấy cả properties và total count trong 1 lần gọi
            var (properties, totalCount) = await _unitOfWork.Properties.GetAllPropertiesAsync(query, role);

            // Convert sang response format
            var propertyList = new List<PropertyCreateResponse>();
            foreach (var property in properties)
            {
                propertyList.Add(await ToPropertyResponse(property));
            }

            if (userId != null && role == "User")
            {
                var savedProperties = await _unitOfWork.FavoriteProperties.GetByUserIdAsync(userId);
                foreach (var property in propertyList)
                {
                    property.isFavorite = savedProperties.Any(sp => sp.propertyId == property.id);
                }

                var viewHistories = await _propertyViewHistory.GetViewHistoryByUserIdAsync(userId);
                foreach (var property in propertyList)
                {
                    property.isViewed = viewHistories.Contains(property.id.ToString());
                }
            }

            return new PropertySearchResponse
            {
                properties = propertyList,
                page = query.pageNumber,
                limit = query.pageSize,
                totalPages = (int)Math.Ceiling(totalCount / (double)query.pageSize),
                count = totalCount
            };
        }

        public async Task<PropertySearchResponse> GetPropertiesBySalerIdAsync(QueryProperty query, string salerId)
        {
            if (query.searchTerm == null)
            {
                query.searchTerm = "";
            }
            else
            {
                _searchService.AddSearchAsync(query);
            }

            var (properties, totalCount) = await _unitOfWork.Properties.GetPropertiesBySellerAsync(query, salerId);
            var propertyList = new List<PropertyCreateResponse>();
            foreach (var property in properties)
            {
                propertyList.Add(await ToPropertyResponse(property));
            }
            return new PropertySearchResponse
            {
                properties = propertyList,
                page = query.pageNumber,
                limit = query.pageSize,
                totalPages = (int)Math.Ceiling(totalCount / (double)query.pageSize),
                count = totalCount
            };
        }

        public async Task<PropertySearchResponse> GetPropertiesByOwnerIdAsync(QueryProperty query, string ownerId)
        {
            if (query.searchTerm == null)
            {
                query.searchTerm = "";
            }
            else
            {
                _searchService.AddSearchAsync(query);
            }

            var (properties, totalCount) = await _unitOfWork.Properties.GetPropertiesByOwnerAsync(query, ownerId);
            var propertyList = new List<PropertyCreateResponse>();
            foreach (var property in properties)
            {
                propertyList.Add(await ToPropertyResponse(property));
            }
            return new PropertySearchResponse
            {
                properties = propertyList,
                page = query.pageNumber,
                limit = query.pageSize,
                totalPages = (int)Math.Ceiling(totalCount / (double)query.pageSize),
                count = totalCount
            };
        }

        public async Task<PropertyCreateResponse> GetPropertyByIdAsync(string id, string userId, string role)
        {
            if (userId != null && role == "User")
            {
                var existingHistory = await _unitOfWork.PropertyViewHistories.GetByUserIdAndPropertyId(userId, id);
                if (existingHistory == null)
                {
                    await _propertyViewHistory.AddViewHistoryAsync(userId, id);
                }
            }
            var prop = await _unitOfWork.Properties.GetByIdAsync(id) ?? throw new InvalidDataException("Không tìm thấy property");
            var result = await ToPropertyResponse(prop);
            if (userId != null)
            {
                var foundFavoriteProperty = await _unitOfWork.FavoriteProperties.GetByUserIdAndPropertyIdAsync(userId, id);
                if (foundFavoriteProperty != null)
                {
                    result.isFavorite = true;
                }
            }
            return result;
        }

        public async Task<PropertyCreateResponse> AddPropertyAsync(string userId, PropertyCreateRequest property)
        {
            var existedUser = await _unitOfWork.Users.GetByIdAsync(userId) ?? throw new InvalidDataException("User is not found");
            var prop = await CreateToProperty(existedUser, property);
            await _unitOfWork.Properties.AddAsync(prop);
            await _unitOfWork.SaveAsync();
            return await ToPropertyResponse(prop);
        }

        //public async Task<PagedResult<PropertyCreateResponse>> GetDraftPropertiesByUserAsync(string userId, string role, int pageSize, int currentPage)
        //{
        //    var (result, total) = await _unitOfWork.Properties.GetDraftPropertiesByUserAsync(userId, role, pageSize, currentPage);
        //    var propertyResponses = new List<PropertyCreateResponse>();
        //    foreach (var property in result)
        //    {
        //        propertyResponses.Add(await ToPropertyResponse(property));
        //    }
        //    return new PagedResult<PropertyCreateResponse>
        //    {
        //        data = propertyResponses,
        //        totalCount = total,
        //        totalPages = (int)Math.Ceiling(total / (double)pageSize),
        //        currentPage = currentPage,
        //        pageSize = pageSize
        //    };
        //}

        private async Task<PropertyCreateResponse> ToPropertyResponse(Property property)
        {
            User owner = null;
            User seller = null;
            UserResponse ownerResponse = null;
            UserResponse sellerResponse = null;
            if (property.ownerId != null)
            {
                owner = await _unitOfWork.Users.GetByIdAsync(property.ownerId);
                ownerResponse = new UserResponse
                {
                    avatar = owner.avatar,
                    email = owner.email,
                    fullName = owner.fullName,
                    id = owner.id,
                    phoneNumber = owner.phoneNumber,
                    gender = owner.gender.ToString(),
                };
            }
            if (property.salerId != null)
            {
                seller = await _unitOfWork.Users.GetByIdAsync(property.salerId);
                sellerResponse = new UserResponse
                {
                    avatar = seller.avatar,
                    email = seller.email,
                    fullName = seller.fullName,
                    id = seller.id,
                    phoneNumber = seller.phoneNumber,
                    gender = seller.gender.ToString(),
                };
            }

            return new PropertyCreateResponse
            {
                id = property.id.ToString(),
                saler = sellerResponse,
                title = property.title,
                name = property.name,
                description = property.description,
                type = property.type.ToString(),
                status = property.status.ToString(),
                adminNote = property.adminNote,
                code = property.code,
                owner = ownerResponse,
                location = property.location == null ? null : new LocationResponse
                {
                    address = property.location.address,
                    city = property.location.city,
                    district = property.location.district,
                    ward = property.location.ward,
                    latitude = property.location.latitude,
                    longitude = property.location.longitude
                },
                propertyDetails = property.propertyDetails,
                priceDetails = property.priceDetails,
                amenities = property.amenities,
                imageUrls = property.images,
                floorPlanUrls = property.floorPlans,
                yearBuilt = property.yearBuilt,
                legalDocumentUrls = property.legalDocuments,
                video = property.video,
                transactionHistory = property.transactionHistory?.Select(th => new TransactionHistory
                {
                    transactionDate = th.transactionDate,
                    transactionType = th.transactionType,
                    price = th.price,
                    buyerId = th.buyerId,
                    sellerId = th.sellerId
                }).ToList(),
                contactName = property.contactName,
                contactPhone = property.contactPhone,
                contactEmail = property.contactEmail,
                createdAt = property.createdAt,
                updatedAt = property.updatedAt,
                isFeatured = property.isFeatured,
                transactionType = property.transactionType.ToString(),
                isVerified = property.isVerified,
            };
        }


        private async Task<Property> CreateToProperty(User user, PropertyCreateRequest property)
        {
            if (property == null)
            {
                throw new InvalidOperationException("Property is required");
            }

            if (property.transactionType.Equals(PropertyTransactionType.ForRent))
            {
                if (property.priceDetails.rentalPrice <= 0)
                {
                    throw new ArgumentException("Rental price must be higher than 0");
                }
            }

            if (property.transactionType.Equals(PropertyTransactionType.ForSale))
            {
                if (property.priceDetails.salePrice <= 0)
                {
                    throw new ArgumentException("Sale price must be higher than 0");
                }
            }

            var rs = new Property
            {
                salerId = user.role == UserRole.Saler || user.role == UserRole.Admin || user.role == UserRole.User ? user.id : null,
                ownerId = user.role == UserRole.Owner ? user.id : null,
                title = property.title,
                name = property.name,
                slug = CreateSlug(property.name),
                description = property.description,
                transactionType = property.transactionType,
                type = property.type,
                status = property.status,
                images = property.images,
                legalDocuments = property.legalDocuments,
                floorPlans = property.floorPlans,
                video = property.video,
                adminNote = property.adminNote,
                code = property.code,
                yearBuilt = property.yearBuilt,
                contactName = user.userName,
                contactPhone = user.phoneNumber,
                contactEmail = user.email,
                createdAt = DateTime.UtcNow,
                updatedAt = DateTime.UtcNow,
                location = CreateToLocation(property.location),
                propertyDetails = property.propertyDetails,
                amenities = property.amenities,
                isFeatured = false, //auto false
            };


            rs.priceDetails = CreateToPriceDetail(property.priceDetails);

            return rs;
        }

        private PriceDetail CreateToPriceDetail(PriceDetailCreateRequest request)
        {
            var priceDetail = new PriceDetail
            {
                currency = request.currency
            };
            if (request.depositAmount != null)
            {
                priceDetail.depositAmount = request.depositAmount;
            }
            if (request.maintenanceFee != null)
            {
                priceDetail.maintenanceFee = request.maintenanceFee;
            }
            if (request.paymentMethods != null)
            {
                priceDetail.paymentMethods = request.paymentMethods;
            }
            if (request.pricePerSquareMeter != null)
            {
                priceDetail.pricePerSquareMeter = request.pricePerSquareMeter;
            }
            if (request.salePrice != null)
            {
                priceDetail.salePrice = request.salePrice;
            }
            if (request.rentalPrice != null)
            {
                priceDetail.rentalPrice = request.rentalPrice;
            }
            return priceDetail;


        }

        private Location CreateToLocation(LocationCreateRequest request)
        {
            var location = new Location
            {
                address = request.address,
                city = request.city,
                district = request.district,
                ward = request.ward,
                latitude = request.latitude ?? 0,
                longitude = request.longitude ?? 0
            };
            return location;
        }

        //slug hellper
        private string CreateSlug(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
                return string.Empty;

            text = text.ToLowerInvariant();
            text = RemoveDiacritics(text);
            text = text.Replace("đ", "d");

            text = Regex.Replace(text, @"[^a-z0-9\s]", "");
            text = Regex.Replace(text, @"\s+", "-");

            return text.Trim('-');
        }

        private string RemoveDiacritics(string text)
        {
            var normalizedString = text.Normalize(NormalizationForm.FormD);
            var stringBuilder = new StringBuilder();

            foreach (var c in normalizedString)
            {
                var unicodeCategory = CharUnicodeInfo.GetUnicodeCategory(c);
                if (unicodeCategory != UnicodeCategory.NonSpacingMark)
                {
                    stringBuilder.Append(c);
                }
            }

            return stringBuilder.ToString().Normalize(NormalizationForm.FormC);
        }

        public async Task<PropertyCreateResponse> UpdatePropertyAsync(string id, string salerId, PropertyUpdateRequest request)
        {
            var prop = await _unitOfWork.Properties.GetByIdAsync(id);
            if (prop == null) throw new InvalidDataException("Không tìm thấy property");



            // check role 
            if (string.IsNullOrWhiteSpace(salerId))
            {
                throw new UnauthorizedAccessException("Bạn không có quyền thực hiện hành động này");
            }

            var user = await _unitOfWork.Users.GetByIdAsync(salerId);
            //if (user?.role.ToString() == "User")
            //{
            //    var owner = await _unitOfWork.Owners.GetByUserIdAsync(salerId);
            //    if (owner == null)
            //    {
            //        throw new UnauthorizedAccessException("Update denied - You don't own this property");
            //    }

            //    var ownerProps = await _unitOfWork.Properties.GetByOwnerIdAsync(owner.id.ToString());
            //    if (ownerProps == null || !ownerProps.Any(p => p.id == prop.id))
            //    {
            //        throw new UnauthorizedAccessException("Update denied - Only allowed to update your own property");
            //    }
            //}


            if (request.title != null)
            {
                prop.title = request.title;
            }
            if (request.name != null)
            {
                if (!prop.name.Equals(request.name))
                {
                    prop.name = request.name;
                    prop.slug = CreateSlug(request.name);
                }
            }
            if (request.description != null) prop.description = request.description;
            if (request.transactionType != null) prop.transactionType = request.transactionType.Value;
            if (request.type != null) prop.type = request.type.Value;
            if (request.status != null) prop.status = request.status.Value;
            if (request.adminNote != null) prop.adminNote = request.adminNote;
            if (request.code != null) prop.code = request.code;
            if (request.yearBuilt != null) prop.yearBuilt = request.yearBuilt;
            if (request.isFeatured.HasValue) prop.isFeatured = request.isFeatured.Value;

            if (request.priceDetails != null && prop.priceDetails != null)
            {
                prop.priceDetails.salePrice = request.priceDetails.salePrice ?? prop.priceDetails.salePrice;
                prop.priceDetails.rentalPrice = request.priceDetails.rentalPrice ?? prop.priceDetails.rentalPrice;
                prop.priceDetails.pricePerSquareMeter = request.priceDetails.pricePerSquareMeter ?? prop.priceDetails.pricePerSquareMeter;
                prop.priceDetails.currency = request.priceDetails.currency ?? prop.priceDetails.currency;
                prop.priceDetails.depositAmount = request.priceDetails.depositAmount ?? prop.priceDetails.depositAmount;
                prop.priceDetails.maintenanceFee = request.priceDetails.maintenanceFee ?? prop.priceDetails.maintenanceFee;
                prop.priceDetails.paymentMethods = request.priceDetails.paymentMethods ?? prop.priceDetails.paymentMethods;
            }

            if (request.location != null && prop.location != null)
            {
                prop.location.address = request.location.address ?? prop.location.address;
                prop.location.city = request.location.city ?? prop.location.city;
                prop.location.district = request.location.district ?? prop.location.district;
                prop.location.ward = request.location.ward ?? prop.location.ward;
                prop.location.latitude = request.location.latitude ?? prop.location.latitude;
                prop.location.longitude = request.location.longitude ?? prop.location.longitude;
            }

            if (request.propertyDetails != null && prop.propertyDetails != null)
            {
                prop.propertyDetails.bedrooms = request.propertyDetails.bedrooms ?? prop.propertyDetails.bedrooms;
                prop.propertyDetails.bathrooms = request.propertyDetails.bathrooms ?? prop.propertyDetails.bathrooms;
                prop.propertyDetails.livingRooms = request.propertyDetails.livingRooms ?? prop.propertyDetails.livingRooms;
                prop.propertyDetails.kitchens = request.propertyDetails.kitchens ?? prop.propertyDetails.kitchens;
                prop.propertyDetails.landArea = request.propertyDetails.landArea ?? prop.propertyDetails.landArea;
                prop.propertyDetails.landWidth = request.propertyDetails.landWidth ?? prop.propertyDetails.landWidth;
                prop.propertyDetails.landLength = request.propertyDetails.landLength ?? prop.propertyDetails.landLength;
                prop.propertyDetails.buildingArea = request.propertyDetails.buildingArea ?? prop.propertyDetails.buildingArea;
                prop.propertyDetails.numberOfFloors = request.propertyDetails.numberOfFloors ?? prop.propertyDetails.numberOfFloors;
                prop.propertyDetails.hasBasement = request.propertyDetails.hasBasement ?? prop.propertyDetails.hasBasement;
                prop.propertyDetails.floorNumber = request.propertyDetails.floorNumber ?? prop.propertyDetails.floorNumber;
                if (request.propertyDetails.apartmentOrientation != null)
                {
                    prop.propertyDetails.apartmentOrientation = request.propertyDetails.apartmentOrientation;
                }
                prop.propertyDetails.furnished = request.propertyDetails.furnished ?? prop.propertyDetails.furnished;
            }

            if (request.amenities != null && prop.amenities != null)
            {
                prop.amenities.parking = request.amenities.parking;
                prop.amenities.elevator = request.amenities.elevator;
                prop.amenities.swimmingPool = request.amenities.swimmingPool ?? prop.amenities.swimmingPool;
                prop.amenities.gym = request.amenities.gym ?? prop.amenities.gym;
                prop.amenities.securitySystem = request.amenities.securitySystem ?? prop.amenities.securitySystem;
                prop.amenities.airConditioning = request.amenities.airConditioning ?? prop.amenities.airConditioning;
                prop.amenities.balcony = request.amenities.balcony ?? prop.amenities.balcony;
                prop.amenities.garden = request.amenities.garden ?? prop.amenities.garden;
                prop.amenities.playground = request.amenities.playground ?? prop.amenities.playground;
                prop.amenities.backupGenerator = request.amenities.backupGenerator ?? prop.amenities.backupGenerator;
            }

            if (request.images != null && request.images.Any())
            {
                if (prop.images == null)
                {
                    prop.images = new List<string>();
                }

                prop.images = request.images.ToList();
            }

            if (request.legalDocuments != null && request.legalDocuments.Any())
            {
                if (prop.legalDocuments == null)
                {
                    prop.legalDocuments = new List<string>();
                }
                prop.legalDocuments = request.legalDocuments.ToList();
            }

            if (request.floorPlans != null && request.floorPlans.Any())
            {
                if (prop.floorPlans == null)
                {
                    prop.floorPlans = new List<string>();
                }
                prop.floorPlans = request.floorPlans.ToList();
            }


            if (request.video != null)
            {
                prop.video = request.video;
            }

            prop.updatedAt = DateTime.UtcNow;

            await _unitOfWork.Properties.UpdateAsync(id, prop);
            await _unitOfWork.SaveAsync();

            return await ToPropertyResponse(prop);
        }



        public async Task<bool> DeletePropertyAsync(string id, string salerId)
        {
            var property = await _unitOfWork.Properties.GetByIdAsync(id);
            if (property == null) return false;

            if (string.IsNullOrWhiteSpace(salerId))
            {
                throw new UnauthorizedAccessException("Bạn không có quyền thực hiện hành động này");
            }

            var user = await _unitOfWork.Users.GetByIdAsync(salerId);
            //if (user?.role.ToString() == "User")
            //{
            //    var owner = await _unitOfWork.Owners.GetByUserIdAsync(salerId);
            //    if (owner == null)
            //    {
            //        throw new UnauthorizedAccessException("Delete denied - You don't own this property");
            //    }

            //    var ownerProps = await _unitOfWork.Properties.GetByOwnerIdAsync(owner.id.ToString());
            //    if (ownerProps == null || !ownerProps.Any(p => p.id == property.id))
            //    {
            //        throw new UnauthorizedAccessException("Delete denied - Only allowed to delete your own property");
            //    }
            //}

            await _unitOfWork.Properties.DeleteAsync(id);
            await _unitOfWork.SaveAsync();
            return true;
        }

        //public async Task<List<Property>> ImportExcelAsync(IFormFile file)
        //{
        //    if (file == null || file.Length == 0)
        //        return null;

        //    var dataTable = ReadExcelFile(file);
        //    var properties = ConvertToProperties(dataTable);

        //    if (properties.Any())
        //    {
        //        await _unitOfWork.Properties.InsertManyAsync(properties);
        //    }

        //    return properties;
        //}

        private DataTable ReadExcelFile(IFormFile file)
        {
            DataTable dt = new DataTable();
            using var stream = file.OpenReadStream();
            using var workbook = new XSSFWorkbook(stream);
            var sheet = workbook.GetSheetAt(0);


            var headerRow = sheet.GetRow(0);
            foreach (var cell in headerRow.Cells)
            {
                dt.Columns.Add(cell.ToString());
            }


            for (int rowIndex = 1; rowIndex <= sheet.LastRowNum; rowIndex++)
            {
                var sheetRow = sheet.GetRow(rowIndex);
                if (sheetRow == null) continue;

                var dataRow = dt.NewRow();
                for (int colIndex = 0; colIndex < sheetRow.LastCellNum; colIndex++)
                {
                    var cell = sheetRow.GetCell(colIndex);
                    dataRow[colIndex] = cell?.ToString();
                }

                dt.Rows.Add(dataRow);
            }

            return dt;
        }


        //private List<Property> ConvertToProperties(DataTable dataTable)
        //{
        //    var properties = new List<Property>();

        //    foreach (DataRow row in dataTable.Rows)
        //    {
        //        var property = new Property { createdAt = DateTime.UtcNow };

        //        foreach (var (key, value) in ColumnMappings)
        //        {
        //            if (!dataTable.Columns.Contains(key)) continue;
        //            string cellValue = row[key]?.ToString();
        //            if (string.IsNullOrEmpty(cellValue)) continue;

        //            switch (value)
        //            {
        //                case "name":
        //                    property.name = cellValue;
        //                    break;
        //                case "owner.name":
        //                    property.owner ??= new Owner();
        //                    property.owner.name = cellValue;
        //                    break;
        //                case "owner.phone":
        //                    property.owner ??= new Owner();
        //                    property.owner.phone = cellValue;
        //                    break;
        //                case "owner.email":
        //                    property.owner ??= new Owner();
        //                    property.owner.email = cellValue;
        //                    break;
        //                case "owner.address":
        //                    property.owner ??= new Owner();
        //                    property.owner.address = cellValue;
        //                    break;
        //                case "type":
        //                    if (Enum.TryParse(cellValue, out PropertyType type))
        //                        property.type = type;
        //                    break;
        //                case "price_details.sale_price":
        //                    property.priceDetails ??= new PriceDetail();
        //                    cellValue = cellValue.Replace(",", "");
        //                    if (double.TryParse(cellValue, out double price))
        //                        property.priceDetails.salePrice = price;
        //                    break;


        //                case "owner.gender":
        //                    if (Enum.TryParse(cellValue, out Gender gender))
        //                    {
        //                        property.owner ??= new Owner();
        //                        property.owner.gender = gender;
        //                    }
        //                    break;
        //                case "owner.nationality":
        //                    property.owner ??= new Owner();
        //                    property.owner.nationality = cellValue;
        //                    break;
        //            }
        //        }

        //        properties.Add(property);
        //    }

        //    return properties;
        //}

        public async Task VerifyPropertyByIdAsync(string id)
        {
            var property = await _unitOfWork.Properties.GetByIdAsync(id);
            if (property == null)
            {
                throw new InvalidDataException("Không tìm thấy property");
            }
            property.isVerified = true;
            await _unitOfWork.Properties.UpdateAsync(id, property);
            await _unitOfWork.SaveAsync();
        }

        public async Task UnverifyPropertyByIdAsync(string id)
        {
            var property = await _unitOfWork.Properties.GetByIdAsync(id);
            if (property == null)
            {
                throw new InvalidDataException("Không tìm thấy property");
            }
            property.isVerified = false;
            await _unitOfWork.Properties.UpdateAsync(id, property);
            await _unitOfWork.SaveAsync();
        }

        public async Task FeaturePropertyByIdAsync(string id)
        {
            var property = await _unitOfWork.Properties.GetByIdAsync(id);
            if (property == null)
            {
                throw new InvalidDataException("Không tìm thấy property");
            }
            property.isFeatured = true;
            await _unitOfWork.Properties.UpdateAsync(id, property);
            await _unitOfWork.SaveAsync();
        }

        public async Task UnfeaturePropertyByIdAsync(string id)
        {
            var property = await _unitOfWork.Properties.GetByIdAsync(id);
            if (property == null)
            {
                throw new InvalidDataException("Không tìm thấy property");
            }
            property.isFeatured = false;
            await _unitOfWork.Properties.UpdateAsync(id, property);
            await _unitOfWork.SaveAsync();
        }


    }
}

