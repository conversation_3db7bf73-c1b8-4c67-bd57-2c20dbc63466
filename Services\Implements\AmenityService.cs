﻿using BusinessObjects.Models;
using Repositories.Interfaces;
using Services.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Services.Implements
{
    public class AmenityService : IAmenityService
    {
        private readonly IUnitOfWork _unitOfWork;
        public AmenityService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
        public async Task<IEnumerable<Amenity>> GetAllAmenitiesAsync()
        {
            return await _unitOfWork.Amenities.GetAllAsync();
        }

        public async Task<Amenity> GetAmenityByIdAsync(string id)
        {
            return await _unitOfWork.Amenities.GetByIdAsync(id);
        }

        public async Task<Amenity> AddAmenityAsync(Amenity amenity)
        {
            await _unitOfWork.Amenities.AddAsync(amenity);
            await _unitOfWork.SaveAsync();
            return amenity;
        }

        public async Task<Amenity> UpdateAmenityAsync(string id, Amenity amenity)
        {
            await _unitOfWork.Amenities.UpdateAsync(id, amenity);
            await _unitOfWork.SaveAsync();
            return amenity;
        }

        public async Task<bool> DeleteAmenityAsync(string id)
        {
            await _unitOfWork.Amenities.DeleteAsync(id);
            await _unitOfWork.SaveAsync();
            return true;
        }
    }
}

