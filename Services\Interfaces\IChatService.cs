﻿using BusinessObjects.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Services.Interfaces
{
    public interface IChatService
    {
        Task<ChatSession> StartChatSessionAsync(string userId);
        Task SendMessageAsync(string sessionId, string senderId, string content, SenderType senderType);
        Task<ChatSession> GetChatSessionAsync(string sessionId);
        Task<List<ChatSession>> GetUserChatSessionsAsync(string userId);
        Task<List<ChatSession>> GetAllChatSessionsAsync();
    }
}
