﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace BusinessObjects.Validations
{
    public class ValidNameAttribute: ValidationAttribute
    {
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value == null)
            {
                return ValidationResult.Success;
            }

            if (!(value is string name))
            {
                return new ValidationResult("Name is invalid");
            }

            name = name.Trim();

            if (string.IsNullOrWhiteSpace(name))
            {
                return new ValidationResult("Name must not be empty");
            }

            var words = name.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            if (words.Length < 2)
            {
                return new ValidationResult("Name must include at least 2 words");
            }


            if (!Regex.IsMatch(name, @"^[\p{L} ]+$"))
            {
                return new ValidationResult("Name must not contain any special characters");
            }

            return ValidationResult.Success;
        }

    }
}
