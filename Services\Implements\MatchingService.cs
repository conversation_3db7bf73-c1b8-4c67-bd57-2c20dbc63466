﻿using BusinessObjects.DTOs.MatchDTOs;
using BusinessObjects.Models;
using MongoDB.Driver;
using Repositories.Interfaces;
using Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Services.Implements
{
    public class MatchingService : IMatchingService
    {
        private readonly IUnitOfWork _unitOfWork;
        
        public MatchingService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
        
        public async Task<List<MatchResponse>> GetMatchesForUserAsync(string userId)
        {
            var matches = await _unitOfWork.Matchings.GetMatchesForUserAsync(userId);
            var results = new List<MatchResponse>();

            foreach (var match in matches)
            {
                var matchedUserId = match.userAId == userId ? match.userBId : match.userAId;
                var user = await _unitOfWork.Users.GetByIdAsync(matchedUserId);
                if (user != null)
                {
                    results.Add(new MatchResponse
                    {
                        matchedUserId = matchedUserId,
                        matchedAt = match.matchedAt,
                        fullName = user.fullName,
                        avatar = user.avatar ?? "",
                        introduction = user.introduction ?? ""
                    });
                }
            }

            return results;
        }
        
        public async Task<bool> UnmatchAsync(string matchId, string userId)
        {
            return await _unitOfWork.Matchings.DeactivateMatchAsync(matchId, userId);
        }



        public async Task<List<MatchResponse>> GetRoommateSuggestionsAsync(string userId, int page = 1, int pageSize = 20)
        {
            // Calculate skip value for pagination
            int skip = (page - 1) * pageSize;
            
            // Use the repository method that uses MongoDB Aggregation Pipeline
            var suggestedUsers = await _unitOfWork.Matchings.GetRoommateSuggestionsAsync(userId, pageSize, skip);
            
            // Convert to MatchResponse objects
            var results = suggestedUsers.Select(user => new MatchResponse
            {
                matchedUserId = user.id,
                matchedAt = DateTime.MinValue, // Not matched yet
                fullName = user.fullName,
                avatar = user.avatar ?? "",
                introduction = user.introduction ?? "",
                // We could add the compatibility score from the aggregation here if needed
            }).ToList();
            
            return results;
        }
        
        private bool IsCompatibleMatch(User user1, RoommatePreference pref1, User user2, RoommatePreference pref2)
        {
            // Check gender preferences
            bool genderMatch = true;
            
            if (pref1.preferredGender != null && user2.gender != null)
            {
                genderMatch = pref1.preferredGender.ToString() == "Any" || pref1.preferredGender.ToString() == user2.gender.ToString();
            }
            
            if (pref2.preferredGender != null && user1.gender != null)
            {
                genderMatch &= pref2.preferredGender.ToString() == "Any" || pref2.preferredGender.ToString() == user1.gender.ToString();
            }
            
            if (!genderMatch) return false;
            
            // Check age preferences
            int age1 = CalculateAge(user1.birthdate ?? DateTime.Today);
            int age2 = CalculateAge(user2.birthdate ?? DateTime.Today);
            
            bool ageMatch = 
                (pref1.ageMin == null || age2 >= pref1.ageMin) &&
                (pref1.ageMax == null || age2 <= pref1.ageMax) &&
                (pref2.ageMin == null || age1 >= pref2.ageMin) &&
                (pref2.ageMax == null || age1 <= pref2.ageMax);
            
            if (!ageMatch) return false;
            
            // Check smoking preferences
            bool smokingMatch = 
                (pref1.allowSmoking == null || pref1.allowSmoking == true) ||
                (pref2.allowSmoking != null && pref2.allowSmoking == false);
            
            if (!smokingMatch) return false;
            
            // Check pet preferences
            bool petMatch = 
                (pref1.allowPets == null || pref1.allowPets == true) ||
                (pref2.allowPets != null && pref2.allowPets == false);
            
            if (!petMatch) return false;
            
            // Check occupation match if both have preferences
            bool occupationMatch = true;
            if (pref1.occupation != null && pref1.occupation.Count > 0 && user2.occupation != null)
            {
                occupationMatch = pref1.occupation.Contains(user2.occupation.Value);
            }
            
            if (pref2.occupation != null && pref2.occupation.Count > 0 && user1.occupation != null)
            {
                occupationMatch &= pref2.occupation.Contains(user1.occupation.Value);
            }
            
            if (!occupationMatch) return false;
            
            // Check lifestyle match if both have preferences
            bool lifestyleMatch = true;
            if (pref1.preferredLifestyles != null && pref1.preferredLifestyles.Count > 0 && user2.lifestyles != null && user2.lifestyles.Count > 0)
            {
                lifestyleMatch = pref1.preferredLifestyles.Intersect(user2.lifestyles).Any();
            }
            
            if (pref2.preferredLifestyles != null && pref2.preferredLifestyles.Count > 0 && user1.lifestyles != null && user1.lifestyles.Count > 0)
            {
                lifestyleMatch &= pref2.preferredLifestyles.Intersect(user1.lifestyles).Any();
            }
            
            return lifestyleMatch;
        }
        
        private int CalculateAge(DateTime birthDate)
        {
            var today = DateTime.Today;
            var age = today.Year - birthDate.Year;
            if (birthDate.Date > today.AddYears(-age)) age--;
            return age;
        }
    }
}