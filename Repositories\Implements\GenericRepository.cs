﻿using BusinessObjects.DatabaseSettings;
using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using Repositories.Interfaces;
using System.Linq.Expressions;

namespace Repositories.Implements
{
    public class GenericRepository<T> : IGenericRepository<T> where T : class
    {
        private readonly IMongoCollection<T> _collection;

        public GenericRepository(RevoLandDbContext context, string collectionName)
        {
            _collection = context.GetCollection<T>(collectionName);
        }

        public virtual async Task<T> GetByIdAsync(string id)
        {
            if (!ObjectId.TryParse(id, out ObjectId objectId))
            {
                throw new ArgumentException("Invalid ObjectId format", nameof(id));
            }

            var filter = Builders<T>.Filter.Eq("_id", objectId);
            return await _collection.Find(filter).FirstOrDefaultAsync();
        }


        public virtual async Task<IEnumerable<T>> GetAllAsync()
        {
            return await _collection.Find(_ => true).ToListAsync();
        }

        public async Task<T> AddAsync(T entity)
        {
            await _collection.InsertOneAsync(entity);
            return entity;
        }

        public async Task<T> UpdateAsync(string id, T entity)
        {
            if (!ObjectId.TryParse(id, out ObjectId objectId))
            {
                throw new ArgumentException("Invalid ObjectId format", nameof(id));
            }

            var filter = Builders<T>.Filter.Eq("_id", objectId);

            var entityBson = entity.ToBsonDocument();
            entityBson.Remove("_id");

            var update = new BsonDocument("$set", entityBson);

            await _collection.UpdateOneAsync(filter, update);
            return await GetByIdAsync(id);
        }

        public async Task<UpdateResult> UpdateOneAsync(FilterDefinition<T> filter, UpdateDefinition<T> update)
        {
            try
            {
                var options = new UpdateOptions
                {
                    IsUpsert = false // Set true nếu muốn tạo mới khi không tìm thấy
                };

                var result = await _collection.UpdateOneAsync(filter, update, options);

                return result;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<UpdateResult> UpdateOneAsync(Expression<Func<T, bool>> filter, UpdateDefinition<T> update)
        {
            try
            {
                var filterDefinition = Builders<T>.Filter.Where(filter);
                return await UpdateOneAsync(filterDefinition, update);
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public virtual async Task<T?> FindOneAndUpdateAsync(FilterDefinition<T> filter, UpdateDefinition<T> update)
        {
            try
            {
                var options = new FindOneAndUpdateOptions<T>
                {
                    ReturnDocument = ReturnDocument.After // Return updated document
                };

                return await _collection.FindOneAndUpdateAsync(filter, update, options);
            }
            catch (Exception ex)
            {
                //_logger.LogError(ex, $"Error in FindOneAndUpdate for {_collectionName}");
                throw;
            }
        }

        public virtual async Task<T?> FindOneAndUpdateAsync(FilterDefinition<T> filter, UpdateDefinition<T> update, FindOneAndUpdateOptions<T> options)
        {
            try
            {
                return await _collection.FindOneAndUpdateAsync(filter, update, options);
            }
            catch (Exception ex)
            {
                //_logger.LogError(ex, $"Error in FindOneAndUpdate with options for {_collectionName}");
                throw;
            }
        }

        // ✅ FindOneAndUpdate with expression filters
        public virtual async Task<T?> FindOneAndUpdateAsync(Expression<Func<T, bool>> filterExpression, UpdateDefinition<T> update)
        {
            try
            {
                var filter = Builders<T>.Filter.Where(filterExpression);
                return await FindOneAndUpdateAsync(filter, update);
            }
            catch (Exception ex)
            {
                //_logger.LogError(ex, $"Error in FindOneAndUpdate with expression for {_collectionName}");
                throw;
            }
        }

        public virtual async Task<T?> FindOneAndUpdateAsync(Expression<Func<T, bool>> filterExpression, UpdateDefinition<T> update, FindOneAndUpdateOptions<T> options)
        {
            try
            {
                var filter = Builders<T>.Filter.Where(filterExpression);
                return await FindOneAndUpdateAsync(filter, update, options);
            }
            catch (Exception ex)
            {
                //_logger.LogError(ex, $"Error in FindOneAndUpdate with expression and options for {_collectionName}");
                throw;
            }
        }

        public async Task<ReplaceOneResult> ReplaceOneAsync(FilterDefinition<T> filter, T replacement)
        {
            try
            {
                var options = new ReplaceOptions
                {
                    IsUpsert = false
                };

                var result = await _collection.ReplaceOneAsync(filter, replacement, options);

                return result;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<ReplaceOneResult> ReplaceOneAsync(Expression<Func<T, bool>> filter, T replacement)
        {
            try
            {
                var filterDefinition = Builders<T>.Filter.Where(filter);
                return await ReplaceOneAsync(filterDefinition, replacement);
            }
            catch (Exception ex)
            {

                throw;
            }
        }


        public async Task<T> DeleteAsync(string id)
        {
            if (!ObjectId.TryParse(id, out ObjectId objectId))
            {
                throw new ArgumentException("Invalid ObjectId format", nameof(id));
            }
            var filter = Builders<T>.Filter.Eq("_id", new ObjectId(id));
            await _collection.DeleteOneAsync(filter);
            return await _collection.Find(filter).FirstOrDefaultAsync();
        }

        /// <summary>
        /// Checks whether an entity exists in the collection for the provided ObjectId.
        /// </summary>
        /// <param name="id">The ObjectId of the entity as a string.</param>
        /// <returns>True if the entity exists; otherwise, false.</returns>
        public async Task<bool> ExistsAsync(string id)
        {
            //parse string id to object id
            if (!ObjectId.TryParse(id, out ObjectId objectId))
            {
                return false;
            }
            var filter = Builders<T>.Filter.Eq("_id", objectId);
            var count = await _collection.CountDocumentsAsync(filter);
            return count > 0;
        }

        public async Task<long> CountAsync(Expression<Func<T, bool>> filter = null)
        {
            var filterDefinition = filter == null
        ? Builders<T>.Filter.Empty
        : Builders<T>.Filter.Where(filter);

            return await _collection.CountDocumentsAsync(filterDefinition);
        }

        public async Task<long> CountAsync(FilterDefinition<T> filter)
        {
            return await _collection.CountDocumentsAsync(filter);
        }

        public async Task<IEnumerable<T>> GetAllWithPagingAsync(
                                                                Expression<Func<T, bool>> filter = null,
                                                                Expression<Func<T, object>> orderBy = null,
                                                                bool isDescending = true,
                                                                int pageNumber = 1,
                                                                int pageSize = 10,
                                                                object include = null)
        {
            var query = filter == null
                ? _collection.AsQueryable()
                : _collection.AsQueryable().Where(filter);

            // Sorting
            if (orderBy != null)
            {
                query = isDescending
                    ? query.OrderByDescending(orderBy)
                    : query.OrderBy(orderBy);
            }

            // Pagination
            return await query
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        public IQueryable<T> GetQueryable()
        {
            return _collection.AsQueryable();
        }
    }
}
