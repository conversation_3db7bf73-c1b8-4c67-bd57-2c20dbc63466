﻿using BusinessObjects.DTOs.PropertyDTO;
using BusinessObjects.QueryObject;

namespace Services.Interface
{
    public interface IPropertyService
    {
        Task<PropertySearchResponse> GetAllPropertiesAsync(QueryProperty query);
        Task<PropertySearchResponse> GetPropertiesBySalerIdAsync(QueryProperty query, string salerId);
        Task<PropertySearchResponse> GetPropertiesByOwnerIdAsync(QueryProperty query, string ownerId);

        Task<PropertyCreateResponse> GetPropertyByIdAsync(string id, string userId, string role);
        Task<PropertyCreateResponse> AddPropertyAsync(string salerId, PropertyCreateRequest property);
        Task<PropertyCreateResponse> UpdatePropertyAsync(string id, string salerId, PropertyUpdateRequest property);
        Task<bool> DeletePropertyAsync(string id, string salerId);
        //Task<List<Property>> ImportExcelAsync(IFormFile file);
        //Task<PropertySearchResponse> GetFavoritePropertiesByUserAsync(QueryProperty query);
        Task VerifyPropertyByIdAsync(string id);
        Task UnverifyPropertyByIdAsync(string id);
        Task FeaturePropertyByIdAsync(string id);
        Task UnfeaturePropertyByIdAsync(string id);
    }
}
