﻿using Microsoft.Extensions.Hosting;

namespace Services.Implements
{
    public class FacebookWebhookHostedService : IHostedService
    {
        private readonly Interfaces.IFacebookMessengerService _fbService;

        public FacebookWebhookHostedService(Interfaces.IFacebookMessengerService fbService)
        {
            _fbService = fbService;
        }

        public async Task StartAsync(
            CancellationToken cancellationToken)
        {
            await _fbService.SubscribePageWebhookAsync();
        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            return Task.CompletedTask;
        }
    }
}
