﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.ChatMessageDTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Services.Interfaces;

namespace Controllers.Controllers
{
    [Route("api/chat")]
    [ApiController]
    public class ChatMessageController : ControllerBase
    {
        private readonly IChatMessageService _chatMessageService;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public ChatMessageController(IChatMessageService chatMessageService, IHttpContextAccessor httpContextAccessor)
        {
            _chatMessageService = chatMessageService;
            _httpContextAccessor = httpContextAccessor;
        }

        [HttpPost]
        [Authorize]
        public async Task<IActionResult> SendMessage(ChatMessageCreateRequest request)
        {
            try
            {
                var senderId = User.Claims.FirstOrDefault(c => c.Type == "UserID")?.Value;
                await _chatMessageService.CreateMessageAsync(senderId, request);
                return Ok("Ok");
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPost("send/zalo")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> SendZaloMessage([FromBody] SendZaloRequest request)
        {
            var userId = _httpContextAccessor.HttpContext?.User?.FindFirst("UserID")?.Value;

            try
            {
                if (string.IsNullOrWhiteSpace(request.text))
                    return BadRequest("Message cannot be empty.");

                await _chatMessageService.SendFromCrmToZaloAsync(
                customerProfileId: request.customerProfileId,
                text: request.text,
                conversationId: request.conversationId
            );

                return Ok(new { success = true });
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }

        }


        [HttpPost("send/anonymous")]
        public async Task<IActionResult> SendAnonymousMessage(ChatMessageCreateRequest request)
        {
            try
            {
                var (isNew, userId, conversationId) = await _chatMessageService.HandleIncomingSystemMessageAsync(request);
                return Ok(new
                {
                    conversationId,
                    userId,
                    isNew
                });
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPost("send/system")]
        [Authorize]
        public async Task<IActionResult> SendSystemMessage([FromBody] ChatMessageCreateRequest request)
        {
            var (isNew, userId, conversationId) = await _chatMessageService.HandleIncomingSystemMessageAsync(request);

            return Ok(new
            {
                success = true,
            });
        }
        [HttpDelete("{messageId}")]
        public async Task<IActionResult> DeleteMessage(string messageId)
        {
            try
            {
                await _chatMessageService.DeleteByIdAsync(messageId);
                return Ok(new { success = true, message = "Thành công" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        [HttpPost("pin")]
        public async Task<IActionResult> PinMessage([FromBody] PinMessageRequest request)
        {
            try
            {
                await _chatMessageService.PinMessageAsync(request.messageId, request.conversationId);
                return Ok(new { success = true, message = "Thành công" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        [HttpPost("unpin")]
        public async Task<IActionResult> UnpinMessage([FromBody] PinMessageRequest request)
        {
            try
            {
                await _chatMessageService.UnpinMessageAsync(request.messageId, request.conversationId);
                return Ok(new { success = true, message = "Message unpinned successfully." });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

    }

}
