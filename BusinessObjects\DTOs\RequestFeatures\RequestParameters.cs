﻿using System.ComponentModel.DataAnnotations;

namespace BusinessObjects.DTOs.RequestFeatures
{
    public class RequestParameters
    {
        [Range(1, int.MaxValue, ErrorMessage = "Page number must be greater than or equal to 1.")]
        public int pageNumber { get; set; } = 1;
        [Range(1, int.MaxValue, ErrorMessage = "Page size must be greater than or equal to 1.")]
        public int pageSize { get; set; } = 10;
    }
}
