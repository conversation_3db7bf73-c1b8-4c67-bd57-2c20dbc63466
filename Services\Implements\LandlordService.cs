﻿using BusinessObjects.DTOs.LandlordDTOs;
using BusinessObjects.Models;
using BusinessObjects.QueryObject;
using Repositories.Interfaces;
using Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Services.Implements
{
    public class LandlordService: ILandlordService
    {
        private readonly IUnitOfWork _unitOfWork;

        public LandlordService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
    
        public async Task<Landlord?> AddLandlordAsync(Landlord landlord)
        {
            var isExisted = await CheckExistedLandlordByEmailAsync(landlord.email);

            return isExisted ? throw new Exception("This landlord is existed") : await _unitOfWork.Landlords.AddAsync(landlord);
        }

        public async Task<Landlord?> GetLandlordByIdAsync(string id)
        {
            return await _unitOfWork.Landlords.GetByIdAsync(id);
        }

        public async Task<List<Landlord>> GetAllLandlordsAsync(QueryLandlord query)
        {
            var landlordQuery = _unitOfWork.Landlords.GetAll(); 

            if (!string.IsNullOrEmpty(query.searchTerm))
            {
                var search = query.searchTerm.ToLower();
                landlordQuery = landlordQuery.Where(l =>
                    l.name.ToLower().Contains(search) ||
                    l.phone.ToLower().Contains(search) ||
                    l.email.ToLower().Contains(search) ||
                    l.address.ToLower().Contains(search));
            }

            landlordQuery = query.isDescending
                ? landlordQuery.OrderByDescending(l => l.name)
                : landlordQuery.OrderBy(l => l.name);

            var paginatedLeads = landlordQuery
                .Skip((query.pageNumber - 1) * query.pageSize)
                .Take(query.pageSize)
                .ToList();

            return paginatedLeads;
        }

        public async Task<Landlord?> UpdateLandlordByIdAsync(string id, LandlordUpdateRequest updatedLandlord)
        {
            var foundLandlord = await GetLandlordByIdAsync(id);
            if (foundLandlord == null)
            {
                throw new Exception("Landlord not found");
            }
            foundLandlord.name = updatedLandlord.name ?? foundLandlord.name;
            foundLandlord.address = updatedLandlord.address ?? foundLandlord.address;
            foundLandlord.email = updatedLandlord.email ?? foundLandlord.email;
            foundLandlord.phone = updatedLandlord.phone ?? foundLandlord.phone;
            return await _unitOfWork.Landlords.UpdateAsync(id, foundLandlord);
        }
        
        public async Task<Landlord?> DeleteLandlordByIdAsync(string id)
        {
            var foundLandlord = await GetLandlordByIdAsync(id);
            if (foundLandlord == null)
            {
                throw new Exception("Landlord not found");
            }

            return await _unitOfWork.Landlords.DeleteAsync(id);
        }
        
        public async Task<Boolean> CheckExistedLandlordByEmailAsync(string email)
        {
            var landlord = await _unitOfWork.Landlords.GetLandlordByEmailAsync(email);
            return landlord != null;
        }
    }
}
