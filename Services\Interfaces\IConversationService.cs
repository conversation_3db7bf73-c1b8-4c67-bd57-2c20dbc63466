﻿using BusinessObjects.DTOs.ConversationDTOs;
using BusinessObjects.Models;
using BusinessObjects.QueryObject;

namespace Services.Interfaces
{
    public interface IConversationService
    {
        Task<List<GroupedConversationsResponse>> GetAllConversationsAsync(QueryConversation query);
        Task<ConversationResponse> GetConversationByUserIdAsync();
        Task<Conversation> GetConversationByIdAsync(string id);
        Task<Conversation> CreateConversationAsync(string userId, string sellerId, string content);
        Task<Conversation> GetConversationByUserIdAndSellerIdAsync(string userId, string sellerId);
        Task<List<ConversationResponse>> GetConversationByCurrentSellerAsync(string sellerId, QueryConversation query);
    }
}
