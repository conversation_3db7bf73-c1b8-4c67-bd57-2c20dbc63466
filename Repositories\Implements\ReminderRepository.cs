﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using Microsoft.EntityFrameworkCore;
using MongoDB.Driver;
using Repositories.Interfaces;
using System.Linq.Expressions;

namespace Repositories.Implements
{
    public class ReminderRepository : GenericRepository<Reminder>, IReminderRepository
    {
        public ReminderRepository(RevoLandDbContext context)
            : base(context, "Reminders")
        {
        }

        public async Task AddReminderAsync(Reminder reminder)
        {
            await AddAsync(reminder);
        }

        public async Task<List<Reminder>> GetRemindersAsync(Expression<Func<Reminder, bool>> predicate)
        {
            return (await GetAllWithPagingAsync(filter: predicate)).ToList();
        }

        public async Task UpdateReminderAsync(Reminder reminder)
        {
            await UpdateAsync(reminder.id.ToString(), reminder);
        }
    }
}
