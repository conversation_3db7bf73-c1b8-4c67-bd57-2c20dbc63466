﻿namespace BusinessObjects.DTOs.StatisticDTOs
{
    public abstract class BaseMetric
    {
        public string label { get; set; }
        public string description { get; set; }
        public DateTime updatedAt { get; set; } = DateTime.UtcNow;
    }

    public class NumberMetric : BaseMetric
    {
        public decimal value { get; set; }
        public decimal? previousValue { get; set; }
        public decimal? changePercentage { get; set; }
        public string trend { get; set; } // "up", "down", "stable"
        public string format { get; set; } = "number"; // "number", "currency", "percentage"
    }

    public class ChartMetric : BaseMetric
    {
        public decimal value { get; set; }
        public List<ChartDataPoint> chartData { get; set; } = new();
        public string chartType { get; set; } = "line"; // "line", "bar", "pie", "area"
    }

    public class ChartDataPoint
    {
        public string date { get; set; }
        public decimal value { get; set; }
        public string label { get; set; }
    }

    public class StatusMetric : BaseMetric
    {
        public string value { get; set; }
        public string status { get; set; } // "success", "warning", "error", "info"
        public string icon { get; set; }
    }

    public class ListMetric : BaseMetric
    {
        public int value { get; set; }
        public List<ListItem> items { get; set; } = new();
        public int? limit { get; set; }
    }

    public class ListItem
    {
        public string id { get; set; }
        public string name { get; set; }
        public decimal value { get; set; }
        public Dictionary<string, object> metadata { get; set; } = new();
    }
}
