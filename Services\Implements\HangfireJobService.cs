using Hangfire;
using Services.Interfaces;

namespace Services.Implements
{
    public class HangfireJobService : IHangfireJobService
    {
        public void ScheduleLeaseExpirationCheckJob()
        {
            RecurringJob.AddOrUpdate<IRentalContractReminderService>(
                "CheckExpiringContracts90Days",
                service => service.CheckExpiringContractsAsync(90),
                Cron.Daily(0, 0),
                TimeZoneInfo.Utc);

            RecurringJob.AddOrUpdate<IRentalContractReminderService>(
                "CheckExpiringContracts60Days",
                service => service.CheckExpiringContractsAsync(60),
                Cron.Daily(0, 0),
                TimeZoneInfo.Utc);

            RecurringJob.AddOrUpdate<IRentalContractReminderService>(
                "CheckExpiringContracts30Days",
                service => service.CheckExpiringContractsAsync(30),
                Cron.Daily(0, 0),
                TimeZoneInfo.Utc);
        }

        public void ScheduleNotificationProcessingJob()
        {
            RecurringJob.AddOrUpdate<IRentalContractReminderService>(
                "ProcessNotifications",
                service => service.GetRemindersToNotifyAsync(),
                Cron.Hourly(),
                TimeZoneInfo.Utc);
        }

        public void ScheduleRentDueReminderJob()
        {
            RecurringJob.AddOrUpdate<IRentalContractReminderService>(
                "CheckRentDueReminder",
                service => service.CheckRentDueRemindersAsync(7),
                Cron.Daily(0, 0),
                TimeZoneInfo.Utc);
        }

        public void ScheduleResetRentRemindersJob()
        {
            RecurringJob.AddOrUpdate<IRentalContractReminderService>(
                "ResetRentReminders",
                service => service.ResetRentRemindersAsync(),
                Cron.Daily(1, 0),
                TimeZoneInfo.Utc);
        }
    }
} 