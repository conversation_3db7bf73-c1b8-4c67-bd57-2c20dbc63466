﻿using BusinessObjects.Models;
using Repositories.Interfaces;
using Services.Interfaces;

namespace Services.Implements
{
    public class CustomerProfileService : ICustomerProfileService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IZaloApiService _zaloApiService;
        private readonly IFacebookMessengerService _facebookMessengerService;

        public CustomerProfileService(IUnitOfWork unitOfWork, IZaloApiService zaloApiService, IFacebookMessengerService facebookMessengerService)
        {
            _unitOfWork = unitOfWork;
            _zaloApiService = zaloApiService;
            _facebookMessengerService = facebookMessengerService;
        }

        public async Task<CustomerProfile> GetOrCreateZaloProfileAsync(string platformUserId)
        {
            var existing = await _unitOfWork.CustomerProfiles.GetByPlatformUserIdAsync("zalo", platformUserId);
            if (existing != null)
            {
                await _unitOfWork.CustomerProfiles.UpdateLastInteraction(existing.id);
                return existing;
            }

            string? displayName = null;
            string? avatarUrl = null;

            try
            {
                var profile = await _zaloApiService.GetUserProfileAsync(platformUserId);
                displayName = profile?.displayName;
                avatarUrl = profile?.avatar;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ZALO PROFILE ERROR] {ex.Message}");
            }

            var newProfile = new CustomerProfile
            {
                platform = "zalo",
                platformUserId = platformUserId,
                displayName = displayName,
                avatarUrl = avatarUrl,
                lastInteraction = DateTime.UtcNow
            };

            await _unitOfWork.CustomerProfiles.AddAsync(newProfile);
            return newProfile;
        }
        public async Task<CustomerProfile> GetOrCreateMessengerProfileAsync(string platformUserId)
        {
            var existing = await _unitOfWork.CustomerProfiles.GetByPlatformUserIdAsync("messenger", platformUserId);
            if (existing != null)
            {
                await _unitOfWork.CustomerProfiles.UpdateLastInteraction(existing.id);
                return existing;
            }

            string? displayName = null;
            string? avatarUrl = null;

            try
            {
                var profile = await _facebookMessengerService.GetUserProfileAsync(platformUserId);
                displayName = profile?.name;
                avatarUrl = profile?.profilePic;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[MESSENGER PROFILE ERROR] {ex.Message}");
            }

            var newProfile = new CustomerProfile
            {
                platform = "messenger",
                platformUserId = platformUserId,
                displayName = displayName,
                avatarUrl = avatarUrl,
                lastInteraction = DateTime.UtcNow
            };

            await _unitOfWork.CustomerProfiles.AddAsync(newProfile);
            return newProfile;
        }
    }
}
