using BusinessObjects.DatabaseSettings;
using BusinessObjects.Settings;
using Controllers.Extensions;
using FirebaseAdmin;
using Google.Apis.Auth.OAuth2;
using Hangfire;
using Hangfire.Mongo;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.SignalR;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Serializers;
using Repositories.Implements;
using Repositories.Interfaces;
using Repositories.Tools;
using Services.Helpers;
using Services.Implements;
using Services.Implements.HubService;
using Services.Interface;
using Services.Interfaces;
using Services.Tools;
using StackExchange.Redis;
using System.Reflection;
using System.Security.Claims;
using System.Text;
using System.Text.Json.Serialization;
using System.Transactions;
using VNPAY.NET;

namespace Controllers
{

    public class CustomUserIdProvider : IUserIdProvider
    {
        public string GetUserId(HubConnectionContext connection)
        {
            var userId = connection.User?.FindFirstValue("UserID");
            return userId;
        }
    }

    public class Program
    {
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);

            bool isDevelopment = builder.Environment.IsDevelopment();
            string pwd = Directory.GetCurrentDirectory();
            // Add services to the container.



            // Confugration
            var _configuration = builder.Configuration;

            // Getting environment variable
            var isdev = builder.Environment.IsDevelopment();

            // Applying connection string
            string conn;
            string _redis;


            if (isdev)
            {
                Console.WriteLine("/* Using development mode");
                // Add configuration based on environment
                builder.Configuration
                    .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
                    .AddEnvironmentVariables();
                var DBConnectionString = builder.Configuration["ConnectionStrings:MongoDb"];
                Console.WriteLine("/* Currently using connection string:" + DBConnectionString);
                builder.Services.Configure<DatabaseSettings>(builder.Configuration.GetSection("ConnectionStrings"));
                _redis = builder.Configuration["ConnectionStrings:Redis"];

            }
            else
            {
                Console.WriteLine("/* Using production mode");
                // Add configuration based on environment
                builder.Configuration
                    .AddJsonFile("appsettings.Production.json", optional: true, reloadOnChange: true)
                    .AddEnvironmentVariables();
                var DBConnectionString = builder.Configuration["ConnectionStrings:MongoDb"];
                Console.WriteLine("/* Currently using connection string:" + DBConnectionString);
                builder.Services.Configure<DatabaseSettings>(builder.Configuration.GetSection("ConnectionStrings"));
                _redis = builder.Configuration["ConnectionStrings:Redis"];
            }

            builder.Services.AddHttpContextAccessor();

            builder.Services.Configure<EmailSettings>(builder.Configuration.GetSection("EmailSettings"));
            builder.Services.Configure<KeySettings>(builder.Configuration.GetSection("KeySetting"));




            // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
            builder.Services.AddEndpointsApiExplorer();
            builder.Services.AddSwaggerGen();
            builder.Services.AddSingleton<RevoLandDbContext>();
            builder.Services.Configure<EmailSettings>(builder.Configuration.GetSection("EmailSettings"));
            builder.Services.Configure<KeySettings>(builder.Configuration.GetSection("KeySetting"));

            builder.Services.AddControllers().AddJsonOptions(options =>
            {
                //  options.JsonSerializerOptions.PropertyNamingPolicy = new HyphenNamingPolicy();
                options.JsonSerializerOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles;
                options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
            });

            // Cấu hình Hangfire
            builder.Services.AddHangfire(config =>
            {
                config.UseMongoStorage(builder.Configuration["ConnectionStrings:MongoDb"], "Hangfire", new MongoStorageOptions
                {
                    MigrationOptions = new MongoMigrationOptions
                    {
                        MigrationStrategy = new Hangfire.Mongo.Migration.Strategies.DropMongoMigrationStrategy()
                    },
                    CheckQueuedJobsStrategy = CheckQueuedJobsStrategy.TailNotificationsCollection
                });
            });
            builder.Services.AddHangfireServer();


            #region addScoped
            builder.Services.AddGoogleOAuthService();
            builder.Services.AddScoped<UserPasswordHasher>();
            builder.Services.AddScoped<CompanyPasswordHasher>();
            builder.Services.AddScoped<TokenTool>();
            builder.Services.AddScoped<TokenTools>();
            builder.Services.AddScoped<IUserService, UserService>();
            builder.Services.AddScoped<IGoogleCrendentialRepository, GoogleCrendentialRepository>();
            builder.Services.AddScoped<IUserRepository, UserRepository>();
            builder.Services.AddScoped<IRefreshTokenRepository, RefreshTokenRepository>();
            builder.Services.AddScoped<IRefreshTokenService, RefreshTokenService>();
            builder.Services.AddScoped<IUnitOfWork, UnitOfWork>();
            builder.Services.AddSingleton<FirebaseService>();
            builder.Services.AddScoped<IPasswordResetService, PasswordResetService>();
            builder.Services.AddScoped<IEmailService, EmailService>();
            builder.Services.AddScoped<ILocationRepository, LocationRepository>();
            builder.Services.AddScoped<IAmenityRepository, AmenityRepository>();
            builder.Services.AddScoped<IOwnerRepository, OwnerRepository>();
            builder.Services.AddScoped<IPriceDetailRepository, PriceDetailRepository>();
            builder.Services.AddScoped<ITransactionHistoryRepository, TransactionHistoryRepository>();
            builder.Services.AddScoped<IDealRepository, DealRepository>();
            builder.Services.AddScoped<IDealService, DealService>();
            builder.Services.AddScoped<IAmenityService, AmenityService>();
            builder.Services.AddScoped<ILocationService, LocationService>();
            builder.Services.AddScoped<IOwnerService, OwnerService>();
            builder.Services.AddScoped<IPriceDetailService, PriceDetailService>();
            builder.Services.AddScoped<IPropertyService, PropertyService>();
            builder.Services.AddScoped<IPropertyDetailService, PropertyDetailService>();
            builder.Services.AddScoped<ITenantRepository, TenantRepository>();
            builder.Services.AddScoped<ITenantService, TenantService>();
            builder.Services.AddScoped<ILeadService, LeadService>();
            builder.Services.AddScoped<IFavoritePropertyService, FavoritePropertyService>();
            builder.Services.AddScoped<ILeadRepository, LeadRepository>();
            builder.Services.AddScoped<ILandlordService, LandlordService>();
            builder.Services.AddScoped<ILandlordRepository, LandlordRepository>();
            builder.Services.AddScoped<ISearchService, SearchService>();
            builder.Services.AddScoped<ISearchRepository, SearchRepository>();
            builder.Services.AddScoped<IAppointmentService, AppointmentService>();
            builder.Services.AddScoped<IAppointmentRepository, AppointmentRepository>();
            builder.Services.AddSignalR();
            builder.Services.AddSingleton<IUserIdProvider, CustomUserIdProvider>();


            builder.Services.AddScoped<IChatMessageService, ChatMessageService>();
            builder.Services.AddScoped<IChatMessageRepository, ChatMessageRepository>();
            builder.Services.AddScoped<ICustomerProfileRepository, CustomerProfileRepository>();
            builder.Services.AddScoped<ICustomerProfileService, CustomerProfileService>();
            builder.Services.AddScoped<IConversationService, ConversationService>();
            builder.Services.AddScoped<IConversationRepository, ConversationRepository>();
            builder.Services.AddScoped<IZaloTokenRepository, ZaloTokenRepository>();
            builder.Services.AddHttpClient<IZaloTokenService, ZaloTokenService>();

            builder.Services.AddScoped<IRedisService, RedisService>();
            builder.Services.AddHttpClient<IZaloApiService, ZaloApiService>();
            builder.Services.AddScoped<IAuthService, AuthService>();
            builder.Services.AddScoped<IVnpay, Vnpay>();
            builder.Services.AddScoped<IVnpayService, VnpayService>();
            builder.Services.AddScoped<MomoService>();
            builder.Services.AddScoped<IAttachmentService, AttachmentService>();
            builder.Services.AddScoped<IAttachmentRepository, AttachmentRepository>();
            builder.Services.AddScoped<IPropertyViewHistoryRepository, PropertyViewHistoryRepository>();
            builder.Services.AddScoped<IPropertyViewHistoryService, PropertyViewHistoryService>();
            builder.Services.AddScoped<IDraftPropertyRepository, DraftPropertyRepository>();
            builder.Services.AddScoped<IDraftPropertyService, DraftPropertyService>();


            builder.Services.AddScoped<ITransactionHistoryService, TransactionHistoryService>();
            builder.Services.AddScoped<INotificationRepository, NotificationRepository>();
            builder.Services.AddScoped<IReminderRepository, ReminderRepository>();
            builder.Services.AddScoped<IReminderService, ReminderService>();
            builder.Services.AddScoped<IRentalContractReminderService, RentalContractReminderService>();
            builder.Services.AddScoped<IRentalContractNotificationService, RentalContractNotificationService>();
            builder.Services.AddScoped<INotificationService>(sp =>
                new NotificationService(
                    emailService: sp.GetRequiredService<IEmailService>(),
                    dbContext: sp.GetRequiredService<RevoLandDbContext>(),
                    twilioAccountSid: builder.Configuration["Twilio:AccountSid"],
                    twilioAuthToken: builder.Configuration["Twilio:AuthToken"],
                    twilioPhoneNumber: builder.Configuration["Twilio:PhoneNumber"],
                    firebaseCredentialsPath: builder.Configuration["Firebase:ServiceAccountKeyPath"],
                    hubContext: sp.GetRequiredService<IHubContext<NotificationHub>>()
                ));

            builder.Services.AddHostedService<ReminderBackgroundService>();

            builder.Services.AddScoped<IRentalContractReminderRepository, RentalContractReminderRepository>();
            builder.Services.AddScoped<IRentalContractNotificationRepository, RentalContractNotificationRepository>();
            builder.Services.AddScoped<ISurveyRepository, SurveyRepository>();
            builder.Services.AddScoped<ISurveyService, SurveyService>();

            #endregion
            builder.Services.AddLogging();

            builder.Services.AddSingleton<IConnectionMultiplexer>(sp =>
            {
                var configuration = ConfigurationOptions.Parse(_redis, true);
                return ConnectionMultiplexer.Connect(configuration);
            });

            #region jwt

            builder.Services.AddSwaggerGen(options =>
            {
                options.AddSecurityDefinition("oauth2", new OpenApiSecurityScheme
                {
                    Description = "Standard Authorization header using the Bearer scheme (\"{token}\")",
                    In = ParameterLocation.Header,
                    Name = "Authorization",
                    Type = SecuritySchemeType.Http,
                    Scheme = "bearer",
                    BearerFormat = "JWT"
                });
                options.OperationFilter<GlobalAuthOperationFilter>();
                options.SwaggerDoc("v1", new OpenApiInfo { Title = "RevoLandAPI", Version = "v1" });

                var apiXml = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
                var apiXmlPath = Path.Combine(AppContext.BaseDirectory, apiXml);
                options.IncludeXmlComments(apiXmlPath, includeControllerXmlComments: true);

                //**BusinessObjects** XML docs for QueryParams ...
                var businessXml = Path.Combine(AppContext.BaseDirectory, "BusinessObjects.xml");
                if (File.Exists(businessXml))
                {
                    options.IncludeXmlComments(businessXml);
                }
            });
            var secretKey = builder.Configuration["KeySetting:SecretKey"];
            var secretKeyBytes = Encoding.UTF8.GetBytes(secretKey);

            builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme).AddJwtBearer(opt =>
            {
                opt.TokenValidationParameters = new TokenValidationParameters
                {

                    ValidateIssuer = false,
                    ValidateAudience = false,
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(secretKeyBytes),
                    ClockSkew = TimeSpan.Zero
                };

                opt.Events = new JwtBearerEvents
                {
                    OnMessageReceived = context =>
                    {
                        var accessToken = context.Request.Query["access_token"];
                        var path = context.HttpContext.Request.Path;

                        // Nếu request đến SignalR hub và có token
                        if (!string.IsNullOrEmpty(accessToken) &&
                            (path.StartsWithSegments("/chatHub") || path.StartsWithSegments("/notificationHub")))
                        {
                            context.Token = accessToken;
                        }
                        return Task.CompletedTask;
                    }
                };
            });
            #endregion

            builder.Services.AddCors(options =>
                {
                    options.AddPolicy("AllowRevoland", policy =>
                    {
                        policy.WithOrigins("http://localhost:3000", "http://localhost:5173", "http://127.0.0.1:5500", "http://127.0.0.1:5501", "https://www.revoland.vn")
                            .AllowAnyHeader()
                            .AllowAnyMethod()
                            .AllowCredentials();
                    });
                });

            builder.Services.AddScoped<ILocationRepository, LocationRepository>();
            builder.Services.AddScoped<IAmenityRepository, AmenityRepository>();
            builder.Services.AddScoped<IOwnerRepository, OwnerRepository>();
            builder.Services.AddScoped<IPriceDetailRepository, PriceDetailRepository>();
            builder.Services.AddScoped<IPropertyRepository, PropertyRepository>();
            builder.Services.AddScoped<IPropertyDetailRepository, PropertyDetailRepository>();
            builder.Services.AddScoped<ITransactionHistoryRepository, TransactionHistoryRepository>();
            builder.Services.AddScoped<IContractRepository, ContractRepository>();
            builder.Services.AddScoped<ICompanyRepository, CompanyRepository>();
            builder.Services.AddScoped<ITeamRepository, TeamRepository>();
            builder.Services.AddScoped<IAgentRepository, AgentRepository>();
            builder.Services.AddScoped<IAgentInviteRepository, AgentInviteRepository>();
            builder.Services.AddScoped<IAgentCompanyLinkRepository, AgentCompanyLinkRepository>();
            builder.Services.AddScoped<IAgentActivityLogRepository, AgentActivityLogRepository>();
            builder.Services.AddScoped<ISwipeRepository,SwipeRepository >(); 
            builder.Services.AddScoped<IMatchingRepository, MatchingRepository >();
            builder.Services.AddScoped<IRoommatePreferenceRepository, RoommatePreferenceRepository>();
            builder.Services.AddScoped<ICollectionPropertyRepository, CollectionPropertyRepository>();


            builder.Services.AddScoped<IAuthorizationHelper, AuthorizationHelper>();
            builder.Services.AddScoped<ICurrentUserService, CurrentUserService>();
            builder.Services.AddScoped<ICustomCalendarService, CustomCalendarService>();
            builder.Services.AddScoped<IGoogleOAuthService, GoogleOAuthService>();
            builder.Services.AddScoped<IGoogleCredentialService, GoogleCredentialService>();
            builder.Services.AddScoped<ICalendarIntegrationService, CalendarIntegrationService>();



            builder.Services.AddScoped<IAmenityService, AmenityService>();
            builder.Services.AddScoped<ILocationService, LocationService>();
            builder.Services.AddScoped<IOwnerService, OwnerService>();
            builder.Services.AddScoped<IPriceDetailService, PriceDetailService>();
            builder.Services.AddScoped<IPropertyService, PropertyService>();
            builder.Services.AddScoped<IPropertyDetailService, PropertyDetailService>();
            builder.Services.AddSingleton<FirebaseService>();
            builder.Services.AddScoped<IFavoritePropertyService, FavoritePropertyService>();
            builder.Services.AddScoped<ITransactionHistoryService, TransactionHistoryService>();
            builder.Services.AddScoped<IContractService, ContractService>();
            builder.Services.AddScoped<IUnitOfWork, UnitOfWork>();
            builder.Services.AddSingleton<IFacebookMessengerService, FacebookMessengerService>();
            builder.Services.AddScoped<IChatSessionRepository, ChatSessionRepository>();
            builder.Services.AddScoped<IChatService, ChatService>();
            builder.Services.AddScoped<IStatisticsService, StatisticsService>();
            builder.Services.AddScoped<ICompanyService, CompanyService>();
            builder.Services.AddScoped<ITeamService, TeamService>();
            builder.Services.AddScoped<IAgentService, AgentService>();
            builder.Services.AddScoped<IAgentActivitylogService, AgentActivitylogService>();
            builder.Services.AddScoped<IBuyerService, BuyerService>();               
            builder.Services.AddScoped<IRoommatePreferenceService, RoommatePreferenceService>();
            builder.Services.AddScoped<ISwipeService, SwipeService>();
            builder.Services.AddScoped<IMatchingService, MatchingService>();
            builder.Services.AddScoped<ICollectionPropertyService, CollectionPropertyService>();



            builder.Services.AddHostedService<FacebookWebhookHostedService>();

            builder.Services.Configure<ApplicationSettings>(options =>
            {
                options.WebAppUrl = "https://localhost:7155";
                options.ApiUrl = "https://localhost:7155";
                options.CompanyName = "QuindLand";
                options.SupportEmail = "<EMAIL>";
            });

            BsonSerializer.RegisterSerializer(new EnumSerializer<TransactionStatus>(BsonType.String));
            var firebaseConfig = builder.Configuration.GetSection("Firebase");

            FirebaseApp.Create(new AppOptions
            {
                Credential = GoogleCredential.FromFile(firebaseConfig["ServiceAccountKeyPath"])
            });

            // Đăng ký cấu hình
            builder.Services.Configure<ESmsSettings>(builder.Configuration.GetSection("ESmsSettings"));

            // Thêm HttpClient cho ESmsService
            builder.Services.AddHttpClient<ESmsService>();

            // Đăng ký services
            builder.Services.AddScoped<ISmsService, ESmsService>();

            builder.Services.AddScoped<IHangfireJobService, HangfireJobService>();
            builder.WebHost.ConfigureKestrel(options =>
            {
                options.Limits.MaxRequestBodySize = *********;
            });

            var app = builder.Build();

            using (var scope = app.Services.CreateScope())
            {
                try
                {
                    var propertyRepo = scope.ServiceProvider.GetRequiredService<IPropertyRepository>();
                    propertyRepo.InitializeIndexesAsync();
                    Console.WriteLine("Property indexes initialized successfully");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Failed to initialize indexes: {ex.Message}");
                }
            }

            // Configure the HTTP request pipeline.
            if (app.Environment.IsDevelopment())
            {
                app.UseSwagger();
                app.UseSwaggerUI();

                // Thêm Hangfire Dashboard chỉ trong môi trường Development
                app.UseHangfireDashboard();
            }
            else
            {
                app.UseSwagger();
                app.UseSwaggerUI();
                // Thêm Hangfire Dashboard chỉ trong môi trường Development
                app.UseHangfireDashboard();
            }

            app.UseHttpsRedirection();
            app.UseRouting();
            app.UseCors("AllowRevoland");
            app.UseAuthentication();
            app.UseAuthorization();


            app.MapGet("/", () => Results.Content(
                """
                <!DOCTYPE html>
                <html lang="en">
                <head>
                    <meta charset="UTF-8">
                    <title>Zalo Verify</title>
                <meta name="zalo-platform-site-verification" content="PVxl8wECVIyYklSAuf1c4cIMzoo8u25nCJSn" />
                </head>
                <body>
                    Hello World!
                </body>
                </html>
                """, "text/html"
            ));

            app.MapHub<ChatMessageHub>("/chatHub");
            app.MapHub<NotificationHub>("/notificationHub");
            app.MapControllers();

            // Khởi tạo Hangfire job khi ứng dụng khởi động
            using (var scope = app.Services.CreateScope())
            {
                var hangfireJobService = scope.ServiceProvider.GetRequiredService<IHangfireJobService>();
                hangfireJobService.ScheduleLeaseExpirationCheckJob();
                hangfireJobService.ScheduleNotificationProcessingJob();
                hangfireJobService.ScheduleRentDueReminderJob();
                hangfireJobService.ScheduleResetRentRemindersJob();
            }

            app.Run();
        }
    }
}