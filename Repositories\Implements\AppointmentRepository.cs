﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.DTOs.AppoinmentDTOs;
using BusinessObjects.Models;
using MongoDB.Bson;
using MongoDB.Driver;
using Repositories.Interfaces;
using static BusinessObjects.DTOs.ResultDTOs.ResultDTO;

namespace Repositories.Implements
{
    public class AppointmentRepository : GenericRepository<Appointment>, IAppointmentRepository
    {
        private readonly IMongoCollection<Appointment> _appointments;

        public AppointmentRepository(RevoLandDbContext context) : base(context, "Appointments")
        {
            _appointments = context.GetCollection<Appointment>("Appointments");
        }

        public async Task<Appointment> GetByLeadIdAndPropertyIdAsync(string leadId, string propertyId)
        {
            //Exclude both Cancelled AND Completed
            var filter = Builders<Appointment>.Filter.And(
                Builders<Appointment>.Filter.Eq(a => a.leadId, leadId),
                Builders<Appointment>.Filter.Eq(a => a.propertyId, propertyId),
                GetActiveAppointmentFilter()
            );

            return await _appointments.Find(filter).FirstOrDefaultAsync();
        }

        //Get active appointment statuses (exclude Completed & Cancelled)
        private FilterDefinition<Appointment> GetActiveAppointmentFilter()
        {
            return Builders<Appointment>.Filter.And(
                Builders<Appointment>.Filter.Ne(a => a.status, AppointmentStatus.Cancelled),
                Builders<Appointment>.Filter.Ne(a => a.status, AppointmentStatus.Completed)
            );
        }

        public async Task<List<Appointment>> GetByLeadIdAsync(string leadId, string status = null)
        {
            var builder = Builders<Appointment>.Filter;
            FilterDefinition<Appointment> filter;

            if (string.IsNullOrEmpty(status))
            {
                // Nếu status là null hoặc rỗng, chỉ lọc theo leadId (chấp nhận mọi status)
                filter = builder.Eq("leadId", leadId);
            }
            else
            {
                // Nếu status có giá trị, lọc theo cả leadId và status
                filter = builder.And(
                    builder.Eq("leadId", leadId),
                    builder.Eq("status", status)
                );
            }

            return await _appointments.Find(filter).ToListAsync();
        }


        public async Task<List<Appointment>> GetBySalerIdAsync(string salerId, string status)
        {
            var builder = Builders<Appointment>.Filter;



            var filter = builder.And(
                builder.Eq("salerId", salerId)
            );
            return await _appointments.Find(filter).ToListAsync();
        }

        //Check for time conflicts for a specific user
        public async Task<List<Appointment>> GetUserAppointmentsByDateRangeAsync(string userId, DateTime startDate,
            DateTime endDate)
        {
            var filter = Builders<Appointment>.Filter.And(
                Builders<Appointment>.Filter.Eq(a => a.userId, userId),
                Builders<Appointment>.Filter.Gte(a => a.date, startDate),
                Builders<Appointment>.Filter.Lte(a => a.date, endDate),
                GetActiveAppointmentFilter()
            );

            return await _appointments.Find(filter).ToListAsync();
        }

        //Check for specific time conflict
        //with excludeAppointmentId
        public async Task<bool> HasTimeConflictAsync(string userId, DateTime appointmentDate, int durationMinutes = 60,
            string? excludeAppointmentId = null)
        {
            // Calculate time range (default 1 hour buffer)
            var startTime = appointmentDate.AddMinutes(-durationMinutes);
            var endTime = appointmentDate.AddMinutes(durationMinutes);

            var filterBuilder = Builders<Appointment>.Filter;
            var filter = filterBuilder.And(
                filterBuilder.Eq(a => a.userId, userId),
                filterBuilder.Gte(a => a.date, startTime),
                filterBuilder.Lte(a => a.date, endTime),
                GetActiveAppointmentFilter()
            );

            // ✅ EXCLUDE current appointment if provided (for reschedule scenario)
            if (!string.IsNullOrEmpty(excludeAppointmentId))
            {
                filter = filterBuilder.And(filter, filterBuilder.Ne(a => a.id, excludeAppointmentId));
            }

            var conflictingAppointment = await _appointments.Find(filter).FirstOrDefaultAsync();
            return conflictingAppointment != null;
        }

        // ✅ ENHANCED: GetConflictingAppointmentsAsync with excludeAppointmentId
        public async Task<List<AppointmentConflictInfo>> GetConflictingAppointmentsAsync(string userId,
            DateTime appointmentDate, int durationMinutes = 60, string? excludeAppointmentId = null)
        {
            var startTime = appointmentDate.AddMinutes(-durationMinutes);
            var endTime = appointmentDate.AddMinutes(durationMinutes);

            var matchStage = new BsonDocument("$match", new BsonDocument
            {
                { "userId", userId },
                {
                    "date", new BsonDocument
                    {
                        { "$gte", startTime },
                        { "$lte", endTime }
                    }
                },
                {
                    "status", new BsonDocument("$nin", new BsonArray
                    {
                        AppointmentStatus.Cancelled.ToString(),
                        AppointmentStatus.Completed.ToString()
                    })
                }
            });

            //ADD EXCLUDE CONDITION if provided
            if (!string.IsNullOrEmpty(excludeAppointmentId))
            {
                matchStage["$match"].AsBsonDocument
                    .Add("_id", new BsonDocument("$ne", ObjectId.Parse(excludeAppointmentId)));
            }

            var pipeline = new BsonDocument[]
            {
                matchStage,
                new BsonDocument("$lookup", new BsonDocument
                {
                    { "from", "Properties" },
                    { "localField", "propertyId" },
                    { "foreignField", "_id" },
                    { "as", "property" }
                }),
                new BsonDocument("$unwind", "$property"),
                new BsonDocument("$project", new BsonDocument
                {
                    { "appointmentId", "$_id" },
                    { "date", 1 },
                    { "status", 1 },
                    { "propertyAddress", "$property.location.address" },
                    { "propertyTitle", "$property.title" }
                })
            };

            var result = await _appointments.Aggregate<AppointmentConflictInfo>(pipeline).ToListAsync();
            return result;
        }

        //Check conflict for both user and saler
        public async Task<bool> HasTimeConflictForBothPartiesAsync(string userId, string salerId,
            DateTime appointmentDate, int durationMinutes = 60, string? excludeAppointmentId = null)
        {
            var startTime = appointmentDate.AddMinutes(-durationMinutes);
            var endTime = appointmentDate.AddMinutes(durationMinutes);

            var filterBuilder = Builders<Appointment>.Filter;

            // Build filter for either userId OR salerId
            var userOrSalerFilter = filterBuilder.Or(
                filterBuilder.Eq(a => a.userId, userId),
                filterBuilder.Eq(a => a.salerId, salerId)
            );

            var filter = filterBuilder.And(
                userOrSalerFilter,
                filterBuilder.Gte(a => a.date, startTime),
                filterBuilder.Lte(a => a.date, endTime),
                GetActiveAppointmentFilter()
            );

            //EXCLUDE current appointment if provided
            if (!string.IsNullOrEmpty(excludeAppointmentId))
            {
                filter = filterBuilder.And(filter, filterBuilder.Ne(a => a.id, excludeAppointmentId));
            }

            var conflictingAppointment = await _appointments.Find(filter).FirstOrDefaultAsync();
            return conflictingAppointment != null;
        }

        //Get detailed conflicts for both parties
        public async Task<ConflictCheckResult> GetConflictingAppointmentsForBothPartiesAsync(string userId,
            string salerId, DateTime appointmentDate, int durationMinutes = 60, string? excludeAppointmentId = null)
        {
            var result = new ConflictCheckResult();

            // ✅ Check USER conflicts
            result.userConflicts =
                await GetConflictingAppointmentsAsync(userId, appointmentDate, durationMinutes, excludeAppointmentId);
            result.hasUserConflict = result.userConflicts.Any();

            // ✅ Check SALER conflicts (if salerId is provided)
            if (!string.IsNullOrEmpty(salerId))
            {
                result.salerConflicts = await GetConflictingAppointmentsAsync(salerId, appointmentDate, durationMinutes,
                    excludeAppointmentId);
                result.hasSalerConflict = result.salerConflicts.Any();
            }

            result.hasAnyConflict = result.hasUserConflict || result.hasSalerConflict;

            return result;
        }

        //Get statuses that should be considered for conflicts
        private List<AppointmentStatus> GetActiveStatuses()
        {
            return new List<AppointmentStatus>
            {
                AppointmentStatus.Open,
                AppointmentStatus.Confirmed,
                AppointmentStatus.Rescheduled
            };
        }
    }
}