using BusinessObjects.Settings;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Services.Implements
{
    public class ESmsService : ISmsService
    {
        private readonly HttpClient _httpClient;
        private readonly ESmsSettings _settings;
        private readonly ILogger<ESmsService> _logger;

        public ESmsService(
            HttpClient httpClient,
            IOptions<ESmsSettings> settings,
            ILogger<ESmsService> logger)
        {
            _httpClient = httpClient;
            _settings = settings.Value;
            _logger = logger;
        }

        public async Task<bool> SendSmsAsync(string phoneNumber, string message)
        {
            try
            {
                // Chuẩn hóa số điện thoại: loại bỏ dấu +84 và thay bằng 0 ở đầu nếu cần
                phoneNumber = NormalizePhoneNumber(phoneNumber);

                var payload = new ESmsRequest
                {
                    ApiKey = _settings.ApiKey,
                    SecretKey = _settings.SecretKey,
                    Phone = phoneNumber,
                    Content = message,
                    SmsType = _settings.UseFixedCostSms ? 3 : _settings.SmsType, // Nếu UseFixedCostSms = true, dùng SmsType = 3 (tin cước thấp)
                    Sandbox = _settings.UseSandbox ? 1 : 0
                };

                // Chỉ thêm brandname khi sử dụng tin chăm sóc khách hàng (SmsType = 2)
                if (!_settings.UseFixedCostSms && _settings.SmsType == 2)
                {
                    payload.Brandname = _settings.Brandname;
                }

                // Gửi tin nhắn theo định dạng POST TEXT
                var content = new StringContent(
                    JsonSerializer.Serialize(payload),
                    Encoding.UTF8,
                    "application/json");

                var response = await _httpClient.PostAsync(_settings.SendSmsEndpoint, content);
                
                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ESmsResponse>();
                    if (result.CodeResult == "100")
                    {
                        _logger.LogInformation($"Gửi SMS thành công đến {phoneNumber}, SMSID: {result.SMSID}");
                        return true;
                    }
                    else
                    {
                        _logger.LogWarning($"Gửi SMS thất bại: {result.ErrorMessage}, Mã lỗi: {result.CodeResult}");
                        return false;
                    }
                }
                
                _logger.LogError($"Lỗi kết nối với eSMS API: {response.StatusCode}");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Lỗi khi gửi SMS: {ex.Message}");
                return false;
            }
        }

        private string NormalizePhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return phoneNumber;

            // Loại bỏ +84 và thay bằng 0
            if (phoneNumber.StartsWith("+84"))
                return "0" + phoneNumber.Substring(3);

            // Nếu bắt đầu bằng 84 không có dấu +
            if (phoneNumber.StartsWith("84") && phoneNumber.Length > 2)
                return "0" + phoneNumber.Substring(2);

            return phoneNumber;
        }
    }

    public class ESmsRequest
    {
        [JsonPropertyName("ApiKey")]
        public string ApiKey { get; set; }

        [JsonPropertyName("SecretKey")]
        public string SecretKey { get; set; }

        [JsonPropertyName("Phone")]
        public string Phone { get; set; }

        [JsonPropertyName("Content")]
        public string Content { get; set; }

        [JsonPropertyName("SmsType")]
        public int SmsType { get; set; } = 2; // 2: tin chăm sóc khách hàng

        [JsonPropertyName("Brandname")]
        public string Brandname { get; set; }

        [JsonPropertyName("Sandbox")]
        public int Sandbox { get; set; } = 0;
    }

    public class ESmsResponse
    {
        [JsonPropertyName("CodeResult")]
        public string CodeResult { get; set; }

        [JsonPropertyName("CountRegenerate")]
        public int CountRegenerate { get; set; }

        [JsonPropertyName("SMSID")]
        public string SMSID { get; set; }

        [JsonPropertyName("ErrorMessage")]
        public string ErrorMessage { get; set; }
    }
} 