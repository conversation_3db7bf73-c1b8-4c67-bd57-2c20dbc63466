﻿using BusinessObjects.DTOs.TransactionHistoryDTO;
using BusinessObjects.Models;
using Microsoft.AspNetCore.Mvc;
using MongoDB.Bson;
using Org.BouncyCastle.Asn1.Ocsp;
using Repositories.Interfaces;
using Services.Interface;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Services.Implements
{
    public class TransactionHistoryService : ITransactionHistoryService
    {
        private readonly IUnitOfWork _unitOfWork;

        public TransactionHistoryService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<TransactionHistoryCreateResponse>> GetAllTransactionHistoriesAsync()
        {
            var transactions = await _unitOfWork.TransactionHistories.GetAllAsync();
            var responses = new List<TransactionHistoryCreateResponse>();
            
            foreach (var trans in transactions)
            {
                responses.Add(await ToTransResponse(trans));
            }
            
            return responses;
        }

        public async Task<TransactionHistoryCreateResponse> GetTransactionHistoryByIdAsync(string id)
        {
            var tran = await _unitOfWork.TransactionHistories.GetByIdAsync(id);
            if (tran == null)
            {
                throw new InvalidDataException("Transaction's not found");
            }
            return await ToTransResponse(tran);
        }

        public async Task<TransactionHistoryCreateResponse> AddTransactionHistoryAsync(TransactionHistoryCreateRequest transactionHistory)
        {
            var createdTrans = await CreateToTrans(transactionHistory);
            await _unitOfWork.TransactionHistories.AddAsync(createdTrans);
            await _unitOfWork.SaveAsync();
            return await ToTransResponse(createdTrans);
        }


        private async Task<TransactionHistoryCreateResponse> ToTransResponse(TransactionHistory trans)
        {
            var response = new TransactionHistoryCreateResponse
            {
                id = trans.transactionId.ToString(),
                price = trans.price,
                transactionDate = trans.transactionDate,
                transactionType = trans.transactionType.ToString()
            };

            if (trans.buyerId != null)
            {
                var buyer = await _unitOfWork.Users.GetByIdAsync(trans.buyerId.ToString());
                if (buyer != null)
                {
                    response.buyer = new TransactionUserInfo
                    {
                        id = buyer.id,
                        fullName = buyer.fullName,
                        email = buyer.email,
                        phoneNumber = buyer.phoneNumber,
                    };
                }
            }

            if (trans.sellerId != null)
            {
                var seller = await _unitOfWork.Users.GetByIdAsync(trans.sellerId.ToString());
                if (seller != null)
                {
                    response.seller = new TransactionUserInfo
                    {
                        id = seller.id,
                        fullName = seller.fullName,
                        email = seller.email,
                        phoneNumber = seller.phoneNumber,
                    };
                }
            }

            return response;
        }

        private async Task<TransactionHistory> CreateToTrans(TransactionHistoryCreateRequest request)
        {
            if(request.Date.Year < 1800 || request.Date.Year > DateTime.Now.Year)
            {
                throw new InvalidOperationException("Date is invalid");
            }

            if(request.price <= 0)
            {
                throw new InvalidOperationException("Price is invalid");
            }

            if (string.IsNullOrEmpty(request.buyerId))
                throw new InvalidOperationException("Buyer ID is required");

            if (string.IsNullOrEmpty(request.sellerId))
                throw new InvalidOperationException("Seller ID is required");

            var rs = new TransactionHistory
            {
                price = request.price,
                transactionDate = request.Date,
                transactionType = request.transactionType,
            };
            if(request.buyerId != null)
            {
                rs.buyerId = ObjectId.Parse(request.buyerId);
            }

            if (request.sellerId != null)
            {
                rs.sellerId = ObjectId.Parse(request.sellerId);
            }
            return rs;
        }

        

        public async Task<TransactionHistoryUpdateResponse> UpdateTransactionHistoryAsync(string id, TransactionHistoryUpdateRequest request)
        {
            var existing = await _unitOfWork.TransactionHistories.GetByIdAsync(id);
            if (existing == null)
            {
                throw new InvalidDataException("Transaction's not found");
            }

            existing.transactionType = request.transactionType;
            if (request.price > 0)
            {
                existing.price = request.price;
            }
            if (request.transactionDate != default)
            {
                existing.transactionDate = request.transactionDate;
            }

            await _unitOfWork.TransactionHistories.UpdateAsync(id, existing);
            await _unitOfWork.SaveAsync();
            return new TransactionHistoryUpdateResponse
            {
                id = existing.transactionId.ToString(),
                price = existing.price,
                transactionDate = existing.transactionDate,
                transactionType = existing.transactionType.ToString()
            };
        }

        public async Task<bool> DeleteTransactionHistoryAsync(string id)
        {
            var tran = await _unitOfWork.TransactionHistories.GetByIdAsync(id);
            if (tran == null)
            {
                throw new InvalidDataException("Transaction's not found");
            }
            await _unitOfWork.TransactionHistories.DeleteAsync(id);
            await _unitOfWork.SaveAsync();
            return true;
        }
    }
}
