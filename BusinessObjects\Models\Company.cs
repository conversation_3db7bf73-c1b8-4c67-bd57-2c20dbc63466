﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System.ComponentModel.DataAnnotations;

namespace BusinessObjects.Models
{
    public class Company
    {
        [BsonId]
        [BsonElement("companyId")]
        [BsonRepresentation(BsonType.ObjectId)]
        public string id { get; set; } = ObjectId.GenerateNewId().ToString();

        [Required]
        public string name { get; set; }

        [Required]
        [EmailAddress]
        public string? email { get; set; }
        [Required]
        public string password { get; set; }

        [Phone]
        [Required]
        [BsonElement("phone")]
        public string? phone { get; set; }
        public string? logoUrl { get; set; }
        public string ownerId { get; set; }
        public string? address { get; set; }

        [BsonRepresentation(BsonType.String)]
        public Industry? industry { get; set; }
        public string? taxId { get; set; }
        public string? licenseNumber { get; set; }
        public string? website { get; set; }
        public bool isVerified { get; set; } = false;
        public bool isActive { get; set; } = true;        
    }

    public enum Industry
    {
        RealEstate,
        Rental
    }
}
