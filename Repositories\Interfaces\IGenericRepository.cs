﻿using MongoDB.Driver;
using System.Linq.Expressions;

namespace Repositories.Interfaces
{
    public interface IGenericRepository<T> where T : class
    {
        Task<T> GetByIdAsync(string id);
        Task<IEnumerable<T>> GetAllAsync();
        Task<T> AddAsync(T entity);
        Task<T> UpdateAsync(string id, T entity);

        Task<UpdateResult> UpdateOneAsync(FilterDefinition<T> filter, UpdateDefinition<T> update);
        Task<UpdateResult> UpdateOneAsync(Expression<Func<T, bool>> filter, UpdateDefinition<T> update);

        Task<T?> FindOneAndUpdateAsync(FilterDefinition<T> filter, UpdateDefinition<T> update);

        Task<T?> FindOneAndUpdateAsync(FilterDefinition<T> filter, UpdateDefinition<T> update, FindOneAndUpdateOptions<T> options);

        Task<T?> FindOneAndUpdateAsync(Expression<Func<T, bool>> filterExpression, UpdateDefinition<T> update);
        Task<T?> FindOneAndUpdateAsync(Expression<Func<T, bool>> filterExpression, UpdateDefinition<T> update, FindOneAndUpdateOptions<T> options);

        Task<ReplaceOneResult> ReplaceOneAsync(FilterDefinition<T> filter, T replacement);
        Task<ReplaceOneResult> ReplaceOneAsync(Expression<Func<T, bool>> filter, T replacement);

        Task<T> DeleteAsync(string id);
        Task<bool> ExistsAsync(string id);

        Task<long> CountAsync(Expression<Func<T, bool>> filter = null);

        Task<long> CountAsync(FilterDefinition<T> filter);

        Task<IEnumerable<T>> GetAllWithPagingAsync(
                                                   Expression<Func<T, bool>> filter = null,
                                                   Expression<Func<T, object>> orderBy = null,
                                                   bool isDescending = true,
                                                   int pageNumber = 1,
                                                   int pageSize = 10,
                                                   object include = null);

        IQueryable<T> GetQueryable();

    }
}
