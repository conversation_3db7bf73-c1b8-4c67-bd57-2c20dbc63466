﻿using BusinessObjects.Models;
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.DTOs.OwnerDTOs
{
    public class OwnerCreateResponse
    {


        public string name { get; set; }


        public string phone { get; set; }


        public string email { get; set; }


        public string address { get; set; }


        public string? gender { get; set; }

        public string? userId { get; set; }

        public string nationality { get; set; }
        public string idVerification { get; set; }

        public DateTime createdAt { get; set; }

        public DateTime updatedAt { get; set; } 
    }
}
