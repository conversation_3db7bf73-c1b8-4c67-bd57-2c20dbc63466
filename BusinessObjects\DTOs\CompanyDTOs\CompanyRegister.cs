﻿using BusinessObjects.Models;
using BusinessObjects.Validations;
using Microsoft.AspNetCore.Http;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.DTOs.CompanyDTOs
{
	public class CompanyRegister
	{
		[Required(ErrorMessage = "Name is required")]
		public string name { get; set; }
		[Required(ErrorMessage = "Email or Phone number is required")]
		[EmailOrPhoneVN(ErrorMessage = "Must be a valid email address or Vietnamese phone number")]
		public string keyRegister { get; set; }
		[Required(ErrorMessage = "Password is required")]
		[RegularExpression(@"^(?=.*[A-Z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,}$",
			ErrorMessage = "Password must have at least 8 characters, one capital letter and one digit")]
		public string password { get; set; }
		public string? address { get; set; }
		[BsonRepresentation(BsonType.String)]
		public Industry? industry { get; set; }
		public string? taxId { get; set; }
		public string? licenseNumber { get; set; }
		public string? website { get; set; }
	}
}
