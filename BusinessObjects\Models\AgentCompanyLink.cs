﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.Models
{
public class AgentCompanyLink
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string id { get; set; } = ObjectId.GenerateNewId().ToString();

    [BsonRepresentation(BsonType.ObjectId)]
    public string agentId { get; set; }

    [BsonRepresentation(BsonType.ObjectId)]
    public string companyId { get; set; }

    public DateTime createdAt { get; set; } = DateTime.UtcNow;

    public string? billingStatus { get; set; }

    public string? plan { get; set; }

    [BsonRepresentation(BsonType.String)]
    public AgentCompanyRole role { get; set; }

    public string token { get; set; }
	public DateTime acceptAt { get; set; }
	}

public enum AgentCompanyRole
{
		Official, //ch<PERSON>h thức
		Collaborator //cộng tác viên
	}
}
