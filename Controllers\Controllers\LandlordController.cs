﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.LandlordDTOs;
using BusinessObjects.Models;
using BusinessObjects.QueryObject;
using Microsoft.AspNetCore.Mvc;
using Services.Implements;
using Services.Interfaces;

namespace Controllers.Controllers
{
    [Route("api/landlords")]
    [ApiController]
    public class LandlordController : ControllerBase
    {
        private readonly ILandlordService _landlordService;

        public LandlordController(ILandlordService landlordService)
        {
            _landlordService = landlordService ?? throw new ArgumentNullException(nameof(landlordService)); 
        }

        private ApiResponse CreateResponse(int code, bool status, string message, object data = null)
        {
            return new ApiResponse { code = code, status = status, message = message, data = data };
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateLandlordById([FromBody] LandlordUpdateRequest updatedRequest, string id)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                       .SelectMany(v => v.Errors)
                       .Select(e => e.ErrorMessage)
                       .ToList().ToString();
                    return BadRequest(CreateResponse(400, false, errors));
                }
                var landlord = await _landlordService.UpdateLandlordByIdAsync(id, updatedRequest);
                var response = new LandlordResponse
                {
                    id = landlord.id,
                    name = landlord.name,
                    email = landlord.email,
                    phone = landlord.phone,
                    address = landlord.address,
                };
                return Ok(CreateResponse(200, true, "Update landlord successfully", response));

            }
            catch (Exception ex)
            {
                return Ok(CreateResponse(500, false, ex.Message));
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteLandlordById(string id)
        {
            try
            {
                await _landlordService.DeleteLandlordByIdAsync(id);
                return Ok(CreateResponse(200, true, "Delete landlord successfully"));
            }
            catch (Exception ex)
            {
                return Ok(CreateResponse(500, false, ex.Message));
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetLandlordById(string id)
        {
            try
            {
                var landlord = await _landlordService.GetLandlordByIdAsync(id);
                if (landlord == null)
                {
                    return NotFound(CreateResponse(404, false, "Landlord not found"));
                }

                var response = new LandlordResponse
                {
                    id = landlord.id,
                    name = landlord.name,
                    email = landlord.email,
                    address = landlord.address,
                    phone = landlord.phone
                };
                return Ok(CreateResponse(200, true, "Get landlord successfully", response));
            }
            catch (Exception ex)
            {
                return Ok(CreateResponse(500, false, ex.Message));
            }
        }


        [HttpGet]
        public async Task<IActionResult> GetAllLandlords([FromQuery] QueryLandlord query)
        {
            try
            {
                var landlords = await _landlordService.GetAllLandlordsAsync(query);
                return Ok(CreateResponse(200, true, "Get all landlords successfully", landlords));
            }
            catch (Exception ex)
            {
                return Ok(CreateResponse(500, false, ex.Message));
            }
        }

        [HttpPost]
        public async Task<IActionResult> AddLandlord([FromBody] LandlordCreateRequest landlord)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                       .SelectMany(v => v.Errors)
                       .Select(e => e.ErrorMessage)
                       .ToList().ToString();
                    return BadRequest(CreateResponse(400, false, errors));
                }

                var newLandlord = new Landlord()
                {
                    name = landlord.name,
                    address = landlord.address,
                    email = landlord.email,
                    phone = landlord.phone,
                    createdAt = DateTime.Now,
                    updatedAt = DateTime.Now
                };
                await _landlordService.AddLandlordAsync(newLandlord);
                return Ok(CreateResponse(201, true, "Create landlord successfully", newLandlord));
            }
            catch (Exception ex)
            {
                return Ok(CreateResponse(500, false, ex.Message));
            }
        }
    }
}
