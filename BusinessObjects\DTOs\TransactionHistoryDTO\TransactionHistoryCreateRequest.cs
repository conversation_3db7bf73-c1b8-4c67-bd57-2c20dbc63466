﻿using BusinessObjects.Models;
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;

namespace BusinessObjects.DTOs.TransactionHistoryDTO
{
    public class TransactionHistoryCreateRequest
    {

        public TransactionType transactionType { get; set; }
        public DateTime Date { get; set; }
        [Range(0.01, double.MaxValue, ErrorMessage = "Price must be greater than 0.")]
        public double price { get; set; }

        public string? buyerId { get; set; }
        public string? sellerId { get; set; }

    }
}
