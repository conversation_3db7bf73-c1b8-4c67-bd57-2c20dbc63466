﻿using Google.Apis.Auth;
using Google.Apis.Auth.OAuth2.Responses;
using static BusinessObjects.DTOs.GoogleDTOs.GoogleUserDTOs;

namespace Services.Interfaces
{
    public interface IGoogleOAuthService
    {
        Task<GoogleJsonWebSignature.Payload> VerifyIdTokenAsync(string idToken);
        string GenerateCalendarAuthUrl(string state, string userId);
        Task<TokenResponse> GetTokensFromCodeAsync(string code);
        Task<TokenResponse> RefreshAccessTokenAsync(string refreshToken);
        //Task<List<CalendarInfo>> GetUserCalendarsAsync(string userId);
        Task<bool> ValidateTokenAsync(string accessToken);

        Task<GoogleUserInfo?> GetUserInfoWithGoogleApiAsync(string accessToken);

    }
}
