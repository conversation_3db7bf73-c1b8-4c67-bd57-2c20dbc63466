﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.LeadDTOs;
using BusinessObjects.Models;
using BusinessObjects.QueryObject;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Services.Interfaces;
namespace Controllers.Controllers
{
    [Route("api/leads")]
    [ApiController]
    [Authorize]
    public class LeadController : ControllerBase
    {
        private readonly ILeadService _leadService;

        public LeadController(ILeadService leadService)
        {
            _leadService = leadService ?? throw new ArgumentNullException(nameof(leadService));
        }

        private ApiResponse CreateResponse(int code, bool status, string message, object data = null)
        {
            return new ApiResponse { code = code, status = status, message = message, data = data };
        }

        /// <summary>
        /// Get all leads for current account
        /// </summary>
        [HttpGet]
        [Authorize(Roles = "Admin,Saler")]
        public async Task<IActionResult> GetAllLeads([FromQuery] QueryLead query)
        {
            try
            {
                var leads = await _leadService.GetAllLeadsByCurrentUserAsync(query);
                return Ok(CreateResponse(200, true, "Thành công", leads));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(500, false, ex.Message));
            }
        }



        /// <summary>
        /// Get lead by id
        /// </summary>
        [HttpGet("{id}")]
        [Authorize(Roles = "Admin,Saler")]
        public async Task<IActionResult> GetLeadById(string id)
        {
            try
            {
                var lead = await _leadService.GetLeadByIdAsync(id);
                if (lead == null)
                {
                    return NotFound(CreateResponse(404, false, "Không tìm thấy khách hàng tiềm năng"));
                }

                return Ok(CreateResponse(200, true, "Thành công", lead));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(500, false, ex.Message));
            }
        }

        /// <summary>
        /// Update lead by id
        /// </summary>
        [HttpPut("{id}")]
        [Authorize(Roles = "Admin,Saler")]
        public async Task<IActionResult> UpdateLeadById([FromBody] LeadUpdateRequest request, string id)
        {
            try
            {
                await _leadService.UpdateLeadAsync(id, request);

                return Ok(CreateResponse(200, true, "Thành công"));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(500, false, ex.Message));
            }
        }

        /// <summary>
        /// Delete lead by id
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteLeadById(string id)
        {
            try
            {
                var lead = await _leadService.GetLeadByIdAsync(id);
                if (lead == null)
                {
                    return NotFound(CreateResponse(404, false, "Không tìm thấy khách hàng tiềm năng"));

                }
                await _leadService.DeleteLeadAsync(id);
                return Ok(CreateResponse(200, true, "Thành công"));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(500, false, ex.Message));
            }
        }

        /// <summary>
        /// Create a new lead
        /// </summary>
        [HttpPost("")]
        [Authorize(Roles = "Saler,Admin")]
        public async Task<IActionResult> AddLead([FromBody] LeadCreateRequest request)
        {
            try
            {
                await _leadService.AddLeadAsync(request);
                return Ok(CreateResponse(201, true, "Thành công"));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(500, false, ex.Message));
            }
        }

        /// <summary>
        /// Add a property to lead's favorite list
        /// </summary>
        [HttpPost("favorites")]
        [Authorize(Roles = "User")]
        public async Task<IActionResult> AddFavoriteProperty([FromBody] AddFavoritePropertyRequest request)
        {
            try
            {
                await _leadService.AddFavoritePropertyAsync(request);
                return Ok(CreateResponse(200, true, "Thành công"));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(500, false, ex.Message));
            }
        }

        /// <summary>
        /// Remove a property from lead's favorite list
        /// </summary>
        [HttpDelete("favorites")]
        public async Task<IActionResult> RemoveFavoriteProperty(string propertyId)
        {
            try
            {
                await _leadService.RemoveFavoritePropertyAsync(propertyId);
                return Ok(CreateResponse(200, true, "Thành công"));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(500, false, ex.Message));
            }
        }

        /// <summary>
        /// Assign lead to a seller
        /// </summary>
        [Authorize(Roles = "Admin")]
        [HttpPatch("assign")]
        public async Task<IActionResult> AssignLeadToSeller([FromQuery] string leadId, [FromQuery] string sellerId)
        {
            try
            {
                await _leadService.AssignLeadToSellerAsync(leadId, sellerId);
                return Ok(CreateResponse(200, true, "Thành công"));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(500, false, ex.Message));
            }
        }

        /// <summary>
        /// Unassign lead from a seller
        /// </summary>
        [Authorize(Roles = "Admin")]
        [HttpPatch("unassign")]
        public async Task<IActionResult> UnassignLeadToSeller([FromQuery] string leadId, [FromQuery] string sellerId)
        {
            try
            {
                await _leadService.UnassignLeadToSellerAsync(leadId, sellerId);
                return Ok(CreateResponse(200, true, "Thành công"));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(500, false, ex.Message));
            }
        }

        /// <summary>
        /// Update lead score by lead id
        /// </summary>
        [Authorize(Roles = "Admin,Saler")]
        [HttpPatch("score")]
        public async Task<IActionResult> UpdateScoreByLeadId([FromQuery] string leadId, [FromQuery] LeadScore score)
        {
            try
            {
                await _leadService.UpdateScoreByLeadId(leadId, score);
                return Ok(CreateResponse(200, true, "Thành công"));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(500, false, ex.Message));
            }
        }
    }
}
