﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.LeadDTOs;
using BusinessObjects.Models;
using BusinessObjects.QueryObject;

namespace Services.Interfaces
{
    public interface ILeadService
    {
        Task AddLeadAsync(LeadCreateRequest request);
        Task<LeadResponse?> GetLeadByIdAsync(string id);
        Task<Lead?> GetLeadByEmailAsync(string email);
        Task<Lead?> GetLeadByPhoneAsync(string phone);
        Task<PagedResult<LeadResponse>> GetAllLeadsAsync(QueryLead queryLead);
        Task<PagedResult<LeadResponse>> GetAllLeadsByCurrentUserAsync(QueryLead queryLead);
        Task<PagedResult<LeadResponse>> GetLeadsBySalerIdAsync(string userId, QueryLead queryLead);
        Task UpdateLeadAsync(string id, LeadUpdateRequest lead);
        Task<List<Lead>> GetLeadsByScoreAsync(LeadScore score);
        Task<Lead?> DeleteLeadAsync(string id);
        Task AddFavoritePropertyAsync(AddFavoritePropertyRequest request);
        Task RemoveFavoritePropertyAsync(string propertyId);
        Task AssignLeadToSellerAsync(string leadId, string sellerId);

        Task UnassignLeadToSellerAsync(string leadId, string sellerId);

        Task UpdateScoreByLeadId(string leadId, LeadScore score);
    }
}
