using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;

namespace BusinessObjects.Models
{
    public class Contract
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public ObjectId id { get; set; }

        [BsonElement("property_id")]
        public string propertyId { get; set; }

        [BsonElement("owner_id")]
        public string ownerId { get; set; }
        
        
        [BsonElement("buyer_id")]
        public string? buyerId { get; set; }
        
       
        [BsonElement("tenant_id")]
        public string? tenantId { get; set; }

        [BsonElement("saler_id")]
        public string salerId { get; set; }

        [BsonElement("contract_type")]
        [BsonRepresentation(BsonType.String)]
        public ContractType contractType { get; set; }

        [BsonElement("contract_start_date")]
        public DateTime? contractStartDate { get; set; }

        [BsonElement("contract_end_date")]
        public DateTime? contractEndDate { get; set; }

        [BsonElement("payment_terms")]
        [BsonRepresentation(BsonType.String)]
        public PaymentTerms? paymentTerms { get; set; }

        [BsonElement("status")]
        [BsonRepresentation(BsonType.String)]
        public ContractStatus status { get; set; }

        [BsonElement("recurring_rent")]
        public double? recurringRent { get; set; }

        [BsonElement("rent_due_date")]
        public DateTime? rentDueDate { get; set; }

        [BsonElement("deposit_amount")]
        public double? depositAmount { get; set; }

        [BsonElement("pdf_contract_urls")]
        public List<string> pdfContractUrls { get; set; } = new List<string>();

        [BsonElement("payment_schedule")]
        public List<PaymentScheduleItem>? paymentSchedule { get; set; }


        [BsonElement("renewal_logs")]
        public List<RenewalLog>? renewalLogs { get; set; } = new List<RenewalLog>();

        [BsonElement("created_at")]
        public DateTime createdAt { get; set; } = DateTime.UtcNow;

        [BsonElement("updated_at")]
        public DateTime updatedAt { get; set; } = DateTime.UtcNow;
        
        [BsonElement("is_deleted")]
        public bool isDeleted { get; set; } = false;
        
        [BsonElement("deleted_at")]
        public DateTime? deletedAt { get; set; } = null;

        [BsonElement("sent_noti_90_days")]
        public bool sentNoti90Days { get; set; } = false;

        [BsonElement("sent_noti_60_days")]
        public bool sentNoti60Days { get; set; } = false;

        [BsonElement("sent_noti_30_days")]
        public bool sentNoti30Days { get; set; } = false;

        [BsonElement("sent_rent_reminder")]
        public bool sentRentReminder { get; set; } = false;

        [BsonElement("is_rent_paid")]
        public bool isRentPaid { get; set; } = false;

        [BsonElement("content")]
        public string? content { get; set; }
    }

    public class RenewalLog
    {
        [BsonElement("previous_end_date")]
        public DateTime previousEndDate { get; set; }

        [BsonElement("new_end_date")]
        public DateTime newEndDate { get; set; }

        [BsonElement("renewal_date")]
        public DateTime renewalDate { get; set; } = DateTime.UtcNow;

        [BsonElement("renewal_note")]
        public string renewalNote { get; set; }
    }

    public enum PaymentTerms
    {
        [BsonElement("monthly")] Monthly,
        [BsonElement("quarterly")] Quarterly,
        [BsonElement("biannually")] Biannually,
        [BsonElement("annually")] Annually
    }

    public enum ContractStatus
    {
        [BsonElement("active")] Active,
        [BsonElement("expired")] Expired,
        [BsonElement("terminated")] Terminated,
        [BsonElement("pending")] Pending
    }
    public enum ContractType
    {
        [BsonElement("rental")] Rental,
        [BsonElement("sale")] Sale
    }
    public class PaymentScheduleItem
    {
        [BsonElement("payment_date")]
        public DateTime paymentDate { get; set; }
        [BsonElement("amount")]
        public double amount { get; set; }
        [BsonElement("note")]
        public string? note { get; set; }
    }
} 