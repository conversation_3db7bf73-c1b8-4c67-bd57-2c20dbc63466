﻿using BusinessObjects.Models;
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson;
using System;

namespace BusinessObjects.DTOs.TransactionHistoryDTO
{
    public class TransactionHistoryCreateResponse
    {
        public string id { get; set; }
        public DateTime transactionDate { get; set; }
        public string transactionType { get; set; }
        public double price { get; set; }
        public TransactionUserInfo? buyer { get; set; }
        public TransactionUserInfo? seller { get; set; }
    }

    public class TransactionUserInfo
    {
        public string id { get; set; }
        public string fullName { get; set; }
        public string email { get; set; }
        public string phoneNumber { get; set; }
    }

    public class TransactionHistoryUpdateResponse
    {
        public string id { get; set; }
        public DateTime transactionDate { get; set; }
        public string transactionType { get; set; }
        public double price { get; set; }
    }
}
