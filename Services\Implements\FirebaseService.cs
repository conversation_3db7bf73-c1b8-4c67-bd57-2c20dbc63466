﻿using Google.Apis.Auth.OAuth2;
using Google.Cloud.Storage.V1;
using Microsoft.Extensions.Configuration;

namespace Services.Implements
{
    public class FirebaseService
    {
        private readonly StorageClient _storageClient;
        private const string BucketName = "convoshub.appspot.com";

        public FirebaseService(IConfiguration configuration)
        {
            var serviceAccountKeyPath = configuration["Firebase:ServiceAccountKeyPath"];

            if (string.IsNullOrWhiteSpace(serviceAccountKeyPath) || !File.Exists(serviceAccountKeyPath))
            {
                throw new FileNotFoundException("Firebase service account key file not found.", serviceAccountKeyPath);
            }

            var credential = GoogleCredential.FromFile(serviceAccountKeyPath);
            _storageClient = StorageClient.Create(credential);
        }

        public async Task<string> UploadAvatarAsync(Stream imageStream, string fileName)
        {
            var uniqueFileName = $"{Guid.NewGuid()}_{fileName}";
            string contentType = GetContentType(fileName);

            try
            {
                var storageObject = await _storageClient.UploadObjectAsync(
                    BucketName,
                    $"avatars/QuindLand/{uniqueFileName}",
                    contentType,
                    imageStream
                );


                storageObject.Acl = new[] { new Google.Apis.Storage.v1.Data.ObjectAccessControl
                    {
                        Entity = "allUsers",
                        Role = "READER"
                    }
                };

                await _storageClient.UpdateObjectAsync(storageObject);


                return $"https://storage.googleapis.com/{BucketName}/avatars/QuindLand/{uniqueFileName}";
            }
            catch (Exception ex)
            {

                throw new InvalidOperationException("Error uploading file to Firebase Storage.", ex);
            }
        }
        public async Task<string> UploadIdVerificationAsync(Stream imageStream, string fileName)
        {
            var uniqueFileName = $"{Guid.NewGuid()}_{fileName}";
            string contentType = GetContentType(fileName);

            try
            {
                var storageObject = await _storageClient.UploadObjectAsync(
                    BucketName,
                    $"IdVerification/QuindLand/{uniqueFileName}",
                    contentType,
                    imageStream
                );


                storageObject.Acl = new[] { new Google.Apis.Storage.v1.Data.ObjectAccessControl
                    {
                        Entity = "allUsers",
                        Role = "READER"
                    }
                };

                await _storageClient.UpdateObjectAsync(storageObject);


                return $"https://storage.googleapis.com/{BucketName}/IdVerification/QuindLand/{uniqueFileName}";
            }
            catch (Exception ex)
            {

                throw new InvalidOperationException("Error uploading file to Firebase Storage.", ex);
            }
        }

        public async Task<string> UploadFileAsync(Stream fileStream, string fileName, string folder = "uploads")
        {
            if (fileStream == null || fileStream.Length == 0)
                throw new ArgumentException("Invalid file stream.");


            var allowedExtensions = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
    {
        ".txt", ".docx", ".xlsx", ".png", ".jpg"
    };

            string fileExtension = Path.GetExtension(fileName);

            if (!allowedExtensions.Contains(fileExtension))
                throw new NotSupportedException("Unsupported file type.");

            var uniqueFileName = $"{Guid.NewGuid()}_{fileName}";
            string filePath = $"QuinLandProp/{folder}/{uniqueFileName}";

            string contentType = GetContentType(fileName);

            try
            {
                var storageObject = await _storageClient.UploadObjectAsync(
                    BucketName,
                    filePath,
                    contentType,
                    fileStream
                );

                storageObject.Acl = new[] { new Google.Apis.Storage.v1.Data.ObjectAccessControl
            {
                Entity = "allUsers",
                Role = "READER"
            }
        };

                await _storageClient.UpdateObjectAsync(storageObject);

                return $"https://storage.googleapis.com/{BucketName}/{filePath}";
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Error uploading file to Firebase Storage.", ex);
            }
        }


        public async Task<string> UploadPropImagesAsync(Stream imageStream, string fileName)
        {
            var uniqueFileName = $"{Guid.NewGuid()}_{fileName}";
            string contentType = GetContentType(fileName);

            try
            {
                var storageObject = await _storageClient.UploadObjectAsync(
                    BucketName,
                    $"avatars/PropImages/{uniqueFileName}",
                    contentType,
                    imageStream
                );


                storageObject.Acl = new[] { new Google.Apis.Storage.v1.Data.ObjectAccessControl
                    {
                        Entity = "allUsers",
                        Role = "READER"
                    }
                };

                await _storageClient.UpdateObjectAsync(storageObject);


                return $"https://storage.googleapis.com/{BucketName}/avatars/PropImages/{uniqueFileName}";
            }
            catch (Exception ex)
            {

                throw new InvalidOperationException("Error uploading file to Firebase Storage.", ex);
            }
        }

        public async Task<string> UploadContractPdfAsync(Stream pdfStream, string fileName)
        {
            var uniqueFileName = $"{Guid.NewGuid()}_{fileName}";
            string contentType = "application/pdf";

            try
            {
                var storageObject = await _storageClient.UploadObjectAsync(
                    BucketName,
                    $"contracts/RentalContracts/{uniqueFileName}",
                    contentType,
                    pdfStream
                );

                storageObject.Acl = new[] { new Google.Apis.Storage.v1.Data.ObjectAccessControl
                    {
                        Entity = "allUsers",
                        Role = "READER"
                    }
                };

                await _storageClient.UpdateObjectAsync(storageObject);

                return $"https://storage.googleapis.com/{BucketName}/contracts/RentalContracts/{uniqueFileName}";
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Error uploading file to Firebase Storage.", ex);
            }
        }

        public async Task<string> UploadAttachmentMessageAsync(Stream attachmentStream, string fileName)
        {
            if (attachmentStream == null || attachmentStream.Length == 0)
                throw new ArgumentException("Invalid attachment stream.");

            var allowedExtensions = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
    {
        ".txt", ".docx", ".xlsx", ".png", ".jpg", ".jpeg", ".gif", ".bmp", ".tiff", ".pdf"
    };
            string fileExtension = Path.GetExtension(fileName);

            if (!allowedExtensions.Contains(fileExtension))
                throw new NotSupportedException("Unsupported file type.");

            var uniqueFileName = $"{Guid.NewGuid()}_{fileName}";
            string filePath = $"QuindLand/messages/{uniqueFileName}";
            string contentType = GetContentType(fileName);

            try
            {
                if (attachmentStream.CanSeek)
                {
                    attachmentStream.Seek(0, SeekOrigin.Begin);
                }

                var storageObject = await _storageClient.UploadObjectAsync(
                    BucketName,
                    filePath,
                    contentType,
                    attachmentStream
                );

                storageObject.Acl = new[] { new Google.Apis.Storage.v1.Data.ObjectAccessControl
        {
            Entity = "allUsers",
            Role = "READER"
        }};

                await _storageClient.UpdateObjectAsync(storageObject);

                return $"https://storage.googleapis.com/{BucketName}/{filePath}";
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Error uploading file to Firebase Storage.", ex);
            }
        }

        public async Task<string> UploadFloorPlanAsync(Stream imageStream, string fileName)
        {
            var uniqueFileName = $"{Guid.NewGuid()}_{fileName}";
            string contentType = GetContentType(fileName);
            try
            {
                var storageObject = await _storageClient.UploadObjectAsync(
                    BucketName,
                    $"QuindLand/FloorPlans/{uniqueFileName}",
                    contentType,
                    imageStream
                );
                storageObject.Acl = new[] { new Google.Apis.Storage.v1.Data.ObjectAccessControl
                    {
                        Entity = "allUsers",
                        Role = "READER"
                    }
                };
                await _storageClient.UpdateObjectAsync(storageObject);
                return $"https://storage.googleapis.com/{BucketName}/QuindLand/FloorPlans/{uniqueFileName}";
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Error uploading file to Firebase Storage.", ex);
            }
        }
        private string GetContentType(string fileName)
        {
            return Path.GetExtension(fileName).ToLowerInvariant() switch
            {
                ".jpg" => "image/jpg",
                ".jpeg" => "image/jpeg",
                ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                ".txt" => "text/plain",
                ".xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                ".png" => "image/png",
                ".gif" => "image/gif",
                ".bmp" => "image/bmp",
                ".tiff" => "image/tiff",
                ".pdf" => "application/pdf",
                _ => "application/octet-stream",
            };
        }

        public async Task<string> UploadPropertyVideoAsync(Stream videoStream, string fileName)
        {
            if (videoStream == null || videoStream.Length == 0)
                throw new ArgumentException("Invalid video stream.");
            var uniqueFileName = $"{Guid.NewGuid()}_{fileName}";
            string contentType = GetContentType(fileName);
            try
            {
                var storageObject = await _storageClient.UploadObjectAsync(
                    BucketName,
                    $"QuindLand/PropertyVideos/{uniqueFileName}",
                    contentType,
                    videoStream
                );
                storageObject.Acl = new[] { new Google.Apis.Storage.v1.Data.ObjectAccessControl
                    {
                        Entity = "allUsers",
                        Role = "READER"
                    }
                };
                await _storageClient.UpdateObjectAsync(storageObject);
                return $"https://storage.googleapis.com/{BucketName}/QuindLand/PropertyVideos/{uniqueFileName}";
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Error uploading video to Firebase Storage.", ex);
            }
        }

        public async Task<string> UploadPropertyImagesAsync(Stream imageStream, string fileName)
        {
            if (imageStream == null || imageStream.Length == 0)
                throw new ArgumentException("Invalid image stream.");
            var uniqueFileName = $"{Guid.NewGuid()}_{fileName}";
            string contentType = GetContentType(fileName);
            try
            {
                var storageObject = await _storageClient.UploadObjectAsync(
                    BucketName,
                    $"QuindLand/PropertyImages/{uniqueFileName}",
                    contentType,
                    imageStream
                );
                storageObject.Acl = new[] { new Google.Apis.Storage.v1.Data.ObjectAccessControl
                    {
                        Entity = "allUsers",
                        Role = "READER"
                    }
                };
                await _storageClient.UpdateObjectAsync(storageObject);
                return $"https://storage.googleapis.com/{BucketName}/QuindLand/PropertyImages/{uniqueFileName}";
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Error uploading file to Firebase Storage.", ex);
            }
        }

        public async Task<string> UploadPropertyLegalDocumentAsync(Stream documentStream, string fileName)
        {
            if (documentStream == null || documentStream.Length == 0)
                throw new ArgumentException("Invalid document stream.");
            var uniqueFileName = $"{Guid.NewGuid()}_{fileName}";
            string contentType = GetContentType(fileName);
            try
            {
                var storageObject = await _storageClient.UploadObjectAsync(
                    BucketName,
                    $"QuindLand/PropertyLegalDocuments/{uniqueFileName}",
                    contentType,
                    documentStream
                );
                storageObject.Acl = new[] { new Google.Apis.Storage.v1.Data.ObjectAccessControl
                    {
                        Entity = "allUsers",
                        Role = "READER"
                    }
                };
                await _storageClient.UpdateObjectAsync(storageObject);
                return $"https://storage.googleapis.com/{BucketName}/QuindLand/PropertyLegalDocuments/{uniqueFileName}";
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Error uploading legal document to Firebase Storage.", ex);
            }
        }
    }
}
