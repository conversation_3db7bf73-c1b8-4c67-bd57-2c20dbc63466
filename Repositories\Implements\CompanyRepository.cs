﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using MongoDB.Driver;
using Repositories.Interfaces;

namespace Repositories.Implements
{
	public class CompanyRepository : GenericRepository<Company>, ICompanyRepository
	{
		private readonly IMongoCollection<Company> _companies;
		public CompanyRepository(RevoLandDbContext context) : base(context, "Companies")
		{
			_companies = context.Companies;
		}
		public async Task<Company> GetCompanyByNameAsync(string name)
		{
			var filter = Builders<Company>.Filter.Eq(u => u.name, name);
			return await _companies.Find(filter).FirstOrDefaultAsync();
		}
		public async Task<Company> GetCompanyByEmailAsync(string email)
		{
			var filter = Builders<Company>.Filter.Eq(u => u.email, email);
			return await _companies.Find(filter).FirstOrDefaultAsync();
		}
		public async Task<Company> GetCompanyByPhonenumberAsync(string phonenumber)
		{
			var filter = Builders<Company>.Filter.Eq(u => u.phone, phonenumber);
			return await _companies.Find(filter).FirstOrDefaultAsync();
		}
		public async Task<List<Company>> GetByIdsAsync(List<string> companyIds)
		{
			var filter = Builders<Company>.Filter.In(u => u.id, companyIds);
			return await _companies.Find(filter).ToListAsync();
		}
    }
}
