﻿using BusinessObjects.Models;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.DTOs.AgentDTOs
{
	public class AddAgentToTeamRequest
	{
		[Required(ErrorMessage = "TeamId is required")]
		public string teamId { get; set; }
		[Required(ErrorMessage = "AgentId is required")]
		public string agentId { get; set; }
		[Required(ErrorMessage = "Role is required")]
		public AgentTeamRole role { get; set; }
	}
}
