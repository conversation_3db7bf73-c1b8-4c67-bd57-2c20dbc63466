﻿using BusinessObjects.Models;

namespace BusinessObjects.DTOs.ConversationDTOs
{
    public class ConversationResponse
    {
        public string id { get; set; }
        public PlatformUser platformUser { get; set; }
        public string platform { get; set; }
        public string lastMessage { get; set; }
        public DateTime lastUpdated { get; set; }
        public List<ChatMessage> pinMessages { get; set; } = new List<ChatMessage>();
    }

    public class PlatformUser
    {
        public string id { get; set; }
        public string name { get; set; }
        public string avatarUrl { get; set; }
    }
}
