﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.AgentDTOs;
using BusinessObjects.DTOs.CompanyDTOs;
using BusinessObjects.DTOs.UserDTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Services.Implements;
using Services.Interfaces;

namespace Controllers.Controllers
{
    [Route("api/company")]
    [ApiController]
    public class CompanyController : ControllerBase
    {
        private readonly ICompanyService _companyService;

        public CompanyController(ICompanyService companyService, FirebaseService firebaseService)
        {
            _companyService = companyService;
        }

        private ApiResponse CreateResponse(int code, bool status, string message, object data = null)
        {
            return new ApiResponse { code = code, status = status, message = message, data = data };
        }

        /// <summary>
        /// Login by company account
        /// </summary>
        [HttpPost("company-login")]
        public async Task<IActionResult> CompanyLogin(CompanyLogin model)
        {
            try
            {
                var token = await _companyService.LoginAsync(model);

                if (token == null)
                {
                    return BadRequest(CreateResponse(401, false, "Tài khoản này chưa được xác thực"));
                }

                return Ok(CreateResponse(200, true, "Đăng nhập thành công", token));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(400, false, "Đăng nhập thất bại: " + ex.Message));
            }
        }

        /// <summary>
        /// Register new company
        /// </summary>        
        [HttpPost("company-register")]
        [Authorize]
        public async Task<IActionResult> CompanyRegister([FromForm] CompanyRegister model, IFormFile logoUrl)
        {
            var companyOwnerId = User.Claims.FirstOrDefault(c => c.Type == "UserID")?.Value;
            if (string.IsNullOrEmpty(companyOwnerId))
            {
                return BadRequest(CreateResponse(401, false, "User ID not found in token"));
            }
            try
            {

                await _companyService.CompanyRegisterAsync(model, companyOwnerId, logoUrl);
            }
            catch (Exception e)
            {
                return BadRequest(CreateResponse(400, false, "Đăng kí thất bại: " + e.Message));
            }

            return Ok(CreateResponse(200, true, "Vui lòng nhập mã OTP trong email hoặc phone để xác thực tài khoản"));

        }

        /// <summary>
        /// Verify company OTP
        /// </summary>      
        [HttpPost("verify-company-otp")]
        public async Task<IActionResult> VerifyCompanyOTP([FromBody] VerifyOTPRequest request)
        {
            try
            {
                await _companyService.VerifyCompanyOTPAsync(request.otp, request.verifyKey);
                return Ok(CreateResponse(200, true, "Xác thực OTP thành công"));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(400, false, "Xác thực OTP thất bại: " + ex.Message));
            }
        }

        /// <summary>   
        /// Request new OTP to verify company account
        /// </summary>
        [HttpPost("request-company-otp")]
        public async Task<IActionResult> RequestCompanyOTP([FromBody] OTPRequest request)
        {
            try
            {
                await _companyService.RequestCompanyOTPAsync(request.verifyKey);
                return Ok(CreateResponse(200, true, "Đã gửi mã xác thực OTP"));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(400, false, "Gửi mã xác thực OTP thất bại: " + ex.Message));
            }
        }

        /// <summary>
        /// Get Company by Id
        /// </summary>   
        [HttpGet("{id}")]
        public async Task<IActionResult> GetCompanyById(string id)
        {
            try
            {
                var company = await _companyService.FindCompanyByIdAsync(id);
                if (company == null)
                {
                    return NotFound(CreateResponse(404, false, "Company not found"));
                }

                return Ok(CreateResponse(200, true, "Get Company by Id Successfully", company));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(500, false, ex.Message));
            }
        }

        /// <summary>
        /// Update Company by Id
        /// </summary>
        [HttpPut("update-company")]
        [Authorize]
        public async Task<IActionResult> UpdateCompany([FromForm] CompanyUpdateRequest request, IFormFile logoUrl)
        {
            try
            {
                var companyId = User.Claims.FirstOrDefault(c => c.Type == "CompanyID")?.Value;
                if (string.IsNullOrEmpty(companyId))
                {
                    return BadRequest(CreateResponse(400, false, "CompanyID not found in token."));
                }

                await _companyService.UpdateCompanyAsync(companyId, request, logoUrl);
                return Ok(CreateResponse(200, true, "Update company successfully"));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(500, false, ex.Message));
            }
        }

        /// <summary>
        /// Update status company
        /// </summary>
        [HttpPut("update-company-status")]
        [Authorize]
        public async Task<IActionResult> UpdateCompanyStatus(bool status)
        {
            try
            {
                var companyOwnerId = User.Claims.FirstOrDefault(c => c.Type == "CompanyID")?.Value;
                if (string.IsNullOrEmpty(companyOwnerId))
                {
                    return BadRequest(CreateResponse(400, false, "CompanyID not found in token."));
                }

                await _companyService.UpdateCompanyStatusAsync(companyOwnerId, status);
                return Ok(CreateResponse(200, true, "Update company status successfully"));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(500, false, ex.Message));
            }
        }

        [HttpGet]
        [Authorize]
        public async Task<IActionResult> GetAllCompany()
        {
            try
            {
                var companies = await _companyService.GetAllCompanyAsync();
                return Ok(CreateResponse(200, true, "Get all companies successfully", companies));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(500, false, ex.Message));
            }
        }

        /// <summary>
        /// Invite agent to Company via Email
        /// </summary>
        [HttpPost("invite-agent")]
        public async Task<IActionResult> InviteAgent([FromForm] InviteAgentRequest request)
        {
            var companyId = User.Claims.FirstOrDefault(c => c.Type == "CompanyID")?.Value;
            if (string.IsNullOrEmpty(companyId))
            {
                return BadRequest(CreateResponse(400, false, "CompanyID not found in token."));
            }
            try
            {
                await _companyService.InviteAgentAsync(request.email, companyId, request.validHours, request.role);
                return Ok(CreateResponse(200, true, "Invitation sent successfully"));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(400, false, ex.Message));
            }
        }
        /// <summary>
        /// ReInvite agent via Email
        /// </summary>

		[HttpPost("re-invite-agent")]
		public async Task<IActionResult> ReInviteAgent([FromForm] InviteAgentRequest request)
		{
			var companyId = User.Claims.FirstOrDefault(c => c.Type == "CompanyID")?.Value;
			if (string.IsNullOrEmpty(companyId))
			{
				return BadRequest(CreateResponse(400, false, "CompanyID not found in token."));
			}
			try
			{
				await _companyService.ReInviteAgentAsync(request.email, companyId);
				return Ok(CreateResponse(200, true, "Invitation sent successfully"));
			}
			catch (Exception ex)
			{
				return BadRequest(CreateResponse(400, false, ex.Message));
			}
		}
        /// <summary>
        /// Agent accept invite via Email
        /// </summary>

		[HttpGet("accept-invite")]
        public async Task<IActionResult> AcceptInvite(string token)
        {
            try
            {
                await _companyService.AcceptInviteAsync(token);
                return Ok(CreateResponse(200, true, "Invitation accepted successfully"));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(400, false, "Accepting invitation failed: " + ex.Message));
            }
        }
    }
}
