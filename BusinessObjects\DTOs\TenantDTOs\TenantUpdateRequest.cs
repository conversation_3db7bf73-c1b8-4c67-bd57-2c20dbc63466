﻿using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.DTOs.TenantDTOs
{
    public class TenantUpdateRequest
    {
        [Required]
        public string id { get; set; } 
 
        public string? name { get; set; }

        [BsonElement("phone")]
        public string? phone { get; set; }
        public string? email { get; set; }
        
        public string? address { get; set; }
        
        public List<string>? idVerification { get; set; }

        
        public DateTime? leaseStartDate { get; set; }

        
        public DateTime? leaseEndDate { get; set; }
        
    }
}
