﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System.ComponentModel.DataAnnotations;
using System.Runtime.Serialization;

namespace BusinessObjects.Models
{
	public class User
	{
		[BsonId]
		[BsonRepresentation(BsonType.ObjectId)]
		public string id { get; set; } = ObjectId.GenerateNewId().ToString();
		[Required]
		[BsonElement("userName")]
		public string userName { get; set; }
		[Required]
		public string fullName { get; set; }
		[EmailAddress]
		[Required]
		[BsonElement("email")]
		public string email { get; set; }
		[Required]
		public string password { get; set; }
		[Phone]
		[Required]
		[BsonElement("phoneNumber")]
		public string phoneNumber { get; set; }
		public string? avatar { get; set; }
		[Required]
		[BsonRepresentation(BsonType.String)]
		public Status? status { get; set; }
		[Required]
		[BsonRepresentation(BsonType.String)]
		public UserRole role { get; set; }
		public string? about { get; set; }

		[BsonRepresentation(BsonType.String)]
		public Gender gender { get; set; }
		public DateTime? birthdate { get; set; }
		[Required]
		public DateTime joinedAt { get; set; }

		public Boolean isVerified { get; set; } = false;

		// Profile matching
		public bool isRoommateActive { get; set; } = false;
		public string? introduction { get; set; }
		[BsonRepresentation(BsonType.String)]
		public OccupationType? occupation { get; set; }
		public decimal? budgetMax { get; set; }
		public bool? hasPets { get; set; }
		public bool? isSmoking { get; set; }
		public DateTime? updatedAtRoommateInfo { get; set; }
		[BsonRepresentation(BsonType.String)]
		public SleepHabit? sleepingHabit { get; set; }
		[BsonRepresentation(BsonType.String)]
		public List<Lifestyle>? lifestyles { get; set; }
		public List<InterestType>? interests { get; set; }
		public RoleMatching? roleMatching { get; set; }
		public Location? currentLocation { get; set; }
		public Location? preferredLocation { get; set; }
		public List<string>? locationPictures { get; set; }
		public List<string>? selfPictures { get; set; }

	}
	public enum Gender
	{
		[BsonElement("male")] Male,
		[BsonElement("female")] Female,
		[BsonElement("other")] Other
	}

	public enum Status
	{
		Online,
		Idle,
		DoNotDisturb,
		Invisible
	}
	public enum UserRole
	{
		Admin,
		Saler,
		User,
		Owner
	}

	public enum SleepHabit
	{
		[EnumMember(Value = "Ngủ sớm")]
		Soon,

		[EnumMember(Value = "Thức khuya")]
		Late,

		[EnumMember(Value = "Không xác định")]
		Uncertain
	}

	public enum Lifestyle
	{
		// Vệ sinh / ngăn nắp
		[EnumMember(Value = "Gọn gàng, sạch sẽ")]
		Clean,
		[EnumMember(Value = "Cực kỳ ngăn nắp, cầu toàn")]
		NeatFreak,
		[EnumMember(Value = "Dễ tính, không quan trọng sự bừa bộn")]
		ToleratesMess,

		// Tính cách & giao tiếp
		[EnumMember(Value = "Thân thiện, dễ nói chuyện")]
		Friendly,
		[EnumMember(Value = "Thích độc lập, ít giao tiếp")]
		Independent,
		[EnumMember(Value = "Cần không gian riêng tư")]
		NeedsPrivacy,

		// Hoạt động xã hội / giải trí
		[EnumMember(Value = "Thích tiệc tùng")]
		PartyFriendly,
		[EnumMember(Value = "Thích yên tĩnh")]
		Quiet,
		[EnumMember(Value = "Nói nhiều, hướng ngoại")]
		TalksALot,
		[EnumMember(Value = "Ít nói, trầm tính")]
		SilentType,

		// Ăn uống & sinh hoạt
		[EnumMember(Value = "Thường nấu ăn")]
		LikesCooking,
		[EnumMember(Value = "Hay ăn ngoài")]
		EatsOutOften,
		[EnumMember(Value = "Chia sẻ đồ ăn")]
		SharesFood,
		[EnumMember(Value = "Giữ đồ ăn riêng")]
		KeepsFoodSeparate,

		// Vệ sinh cá nhân / chia sẻ không gian
		[EnumMember(Value = "Thích nhà vệ sinh riêng")]
		PrivateBathroomPreferred,
		[EnumMember(Value = "Dùng WC chung cũng được")]
		OkayWithSharedBathroom,

		// Mối quan hệ cá nhân
		[EnumMember(Value = "Thỉnh thoảng có người yêu tới")]
		HasPartnerVisit,
		[EnumMember(Value = "Không cho người ngoài vào")]
		NoPartnerVisit,

		// Giải trí & vận động
		[EnumMember(Value = "Tập gym thường xuyên")]
		GymGoer,
		[EnumMember(Value = "Chơi game thường xuyên")]
		Gamer,

		// Âm thanh
		[EnumMember(Value = "Hay bật nhạc")]
		PlaysMusicOften,
		[EnumMember(Value = "Ưa thích sự yên tĩnh")]
		PrefersSilence,
		[EnumMember(Value = "Nhạy cảm với tiếng ồn")]
		NoiseSensitive,

		// Tài chính / chia tiền
		[EnumMember(Value = "Luôn trả tiền đúng hạn")]
		PaysOnTime,
		[EnumMember(Value = "Rạch ròi về chi phí")]
		BudgetStrict,
		[EnumMember(Value = "Linh hoạt trong chia chi phí")]
		FlexibleOnCostShare,

		// Lịch làm việc / giờ giấc
		[EnumMember(Value = "Làm ca đêm")]
		NightWorker,
		[EnumMember(Value = "Làm hành chính")]
		DayWorker,

		// Lối sống
		[EnumMember(Value = "Quan tâm đến môi trường")]
		EnvironmentallyFriendly,
		[EnumMember(Value = "Sống tối giản")]
		Minimalist
	}

	public enum RoleMatching
	{
		[EnumMember(Value = "Tìm người ở ghép")]
		ShareRoom,

		[EnumMember(Value = "Tìm phòng để ở")]
		FindRoom
	}

	public enum OccupationType
	{
		[EnumMember(Value = "Sinh viên")]
		Student,

		[EnumMember(Value = "Nhân viên văn phòng")]
		OfficeWorker,

		[EnumMember(Value = "Làm tự do")]
		Freelancer,

		[EnumMember(Value = "Làm việc từ xa")]
		RemoteWorker,

		[EnumMember(Value = "Làm ca")]
		ShiftWorker,

		[EnumMember(Value = "Thất nghiệp")]
		Unemployed,

		[EnumMember(Value = "Chủ doanh nghiệp")]
		BusinessOwner,

		[EnumMember(Value = "Công chức")]
		GovernmentEmployee,

		[EnumMember(Value = "Khác")]
		Other
	}

	public enum InterestType
	{
		[EnumMember(Value = "Đọc sách")]
		Reading,
		[EnumMember(Value = "Nấu ăn")]
		Cooking,
		[EnumMember(Value = "Chơi game")]
		Gaming,
		[EnumMember(Value = "Xem phim")]
		WatchingMovies,
		[EnumMember(Value = "Du lịch")]
		Traveling,
		[EnumMember(Value = "Thể dục thể thao")]
		Fitness,
		[EnumMember(Value = "Thiền định")]
		Meditation,
		[EnumMember(Value = "Nghe nhạc")]
		Music,
		[EnumMember(Value = "Chạy bộ")]
		Running,
		[EnumMember(Value = "Nhiếp ảnh")]
		Photography,
		[EnumMember(Value = "Lập trình")]
		Coding,
		[EnumMember(Value = "Yêu động vật")]
		PetLover,
		[EnumMember(Value = "Trồng cây")]
		Gardening,
		[EnumMember(Value = "Uống cà phê")]
		CoffeeEnthusiast,
		[EnumMember(Value = "Board game")]
		BoardGames,
		[EnumMember(Value = "Giao lưu xã hội")]
		Socializing,
		[EnumMember(Value = "Hoạt động tình nguyện")]
		Volunteering,
		[EnumMember(Value = "Trang trí nhà cửa")]
		HomeDecoration
	}

}
