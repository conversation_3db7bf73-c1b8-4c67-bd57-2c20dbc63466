﻿using System.Runtime.Serialization;
using System.Reflection;

namespace Services.Helpers
{
	public static class EnumHelper
	{
		public static string GetEnumMemberValue<T>(T enumValue) where T : Enum
		{
			var field = typeof(T).GetField(enumValue.ToString());
			var attribute = field?.GetCustomAttribute<EnumMemberAttribute>();
			return attribute?.Value ?? enumValue.ToString();
		}

		public static List<object> GetEnumList<T>() where T : Enum
		{
			return Enum.GetValues(typeof(T))
				.Cast<T>()
				.Select(e => new
				{
					Name = e.ToString(),
					DisplayName = GetEnumMemberValue(e)
				})
				.ToList<object>();
		}
	}
}
