﻿using BusinessObjects.Models;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.DTOs.TeamDTOs
{
	public class TeamCreateRequest
	{
		[Required(ErrorMessage = "Name is required")]
		public string name { get; set; }
		public string? description { get; set; }
	}
}
