﻿using BusinessObjects.DTOs.OwnerDTOs;
using BusinessObjects.Models;
using MongoDB.Bson;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Services.Interface
{
    public interface IOwnerService
    {
        Task<IEnumerable<OwnerCreateResponse>> GetAllOwnersAsync();
        Task<OwnerCreateResponse> GetOwnerByIdAsync(string id);
        Task<OwnerCreateResponse> AddOwnerAsync(OwnerCreatedRequest owner);
        Task<OwnerCreateResponse> UpdateOwnerAsync(string id, OwnerUpdateRequest owner);
        Task<bool> DeleteOwnerAsync(string id);
        Task<OwnerCreateResponse> GetOwnerByPhoneAsync(string phone);
        
    }
}
