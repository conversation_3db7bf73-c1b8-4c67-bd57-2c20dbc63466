﻿namespace BusinessObjects.DTOs.DealDTOs
{
    public class DealNoteResponse
    {
        public required string id { get; set; }
        public required string? title { get; set; }
        public required string content { get; set; }
        public DateTime createdAt { get; set; }
        public DateTime updatedAt { get; set; }

        public string? createdBy { get; set; }
        // Additional properties can be added as needed
        // For example, you might want to include the author of the note, etc.

    }
}
