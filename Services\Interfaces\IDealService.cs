﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.DealDTOs;
using BusinessObjects.QueryObject;

namespace Services.Interfaces
{
    public interface IDealService
    {
        Task<IEnumerable<DealResponse>> TakeAllDealsAsync();

        Task<DealResponse> AddDealAsync(DealCreateRequest deal);
        Task<DealResponse> AddDealByCurrentSalerAsync(DealCreateRequest request, string currentUserId);
        Task<DealResponse> AddDealByCurrentUserAsync(DealCreateRequest request);

        Task<PagedResult<DealResponse>> GetAllDealsAsyncs(QueryDeal query);
        Task<PagedResult<DealResponse>> GetAllDealsBySaleRepIdAsync(string saleRepsId, QueryDeal query);
        Task<PagedResult<DealResponse>> GetAllDealsByCurrentUserAsync(QueryDeal query);

        Task<DealResponse> GetDealByIdAsync(string id);
        Task<DealResponse> GetDealByIdByCurrentUserAsync(string id);

        Task<DealResponse> UpdateDealAsync(string id, DealUpdateRequest deal, string userId, bool isAdminUpdate);
        Task<DealResponse> UpdateDealByCurrentUserAsync(string id, DealUpdateRequest deal);
        Task<bool> DeleteDealAsync(string id, string adminId);
        Task<bool> DeleteDealByCurrentUserAsync(string id);
        Task<bool> DeleteDealByCurrentSalerAsync(string id, string currentUserId);

        Task<PagedResult<DealsLogResponse>> GetDealsLogByDealIdAsync(string id, QueryDealsLog query);

        Task<DealNoteResponse> UpdateDealNoteAsync(string dealId, DealNoteRequest request, string dealNoteId);

        Task<bool> AddDealPropertyAsync(string dealId, string propertyId);
        Task<bool> DeleteDealPropertyAsync(string dealId, string propertyId);

        Task<bool> UpdateDealPropertyAsync(string dealId, string propertyId, DealPropertyUpdateRequest request);

        //STATISTIC
        Task<int> GetDealsInProgressCountAsync(string salerId);
        Task<int> GetDealsClosedThisMonthCountAsync(string salerId);
        Task<int> GetDealsWonThisMonthCountAsync(string salerId);
        Task<int> GetDealsLostThisMonthCountAsync(string salerId);
        Task<Dictionary<string, int>> GetDealsByStatusCountAsync(string salerId);
        Task<decimal> GetConversionRateAsync(string salerId, int totalLeads);

        Task<List<DealValueByPropertyTypeDto>> GetDealValueByPropertyTypeAsync(
        string salesRepId = null,
        DateTime? fromDate = null,
        DateTime? toDate = null);

    }
}
