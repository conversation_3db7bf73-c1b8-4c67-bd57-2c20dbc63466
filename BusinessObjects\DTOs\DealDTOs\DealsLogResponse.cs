﻿using BusinessObjects.Models;

namespace BusinessObjects.DTOs.DealDTOs
{
    public class DealsLogResponse
    {
        public string? id { get; set; }
        public required string dealId { get; set; }
        public Deal.DealStatus fromStatus { get; set; }
        public Deal.DealStatus toStatus { get; set; }
        public bool isDeleteAction { get; set; }
        public DateTime timestamp { get; set; }
        public required UserResponse triggeredBy { get; set; }
    }
}

