﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.UserDTOs;
using BusinessObjects.Models;
using BusinessObjects.Settings;
using BusinessObjects.Validations;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Repositories.Interfaces;
using Services.Interfaces;
using Services.Tools;
using System.Text.RegularExpressions;

namespace Services.Implements
{
    public class AuthService : IAuthService
    {
        private readonly IRedisService _redisService;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IUserService _userService;
        private readonly UserPasswordHasher _userPasswordHasher;
        private readonly TokenTools _token;
        private readonly FirebaseService _firebaseService;
        private readonly IZaloApiService _zaloApiService;
        private readonly IEmailService _emailService;

        public AuthService(IRedisService redisService, IEmailService emailService, IZaloApiService zaloApiService, TokenTools token, FirebaseService firebaseService, UserPasswordHasher userPasswordHasher, IHttpContextAccessor httpContextAccessor, IUnitOfWork unitOfWork, IUserService userService)
        {
            _redisService = redisService;
            _httpContextAccessor = httpContextAccessor;
            _unitOfWork = unitOfWork;
            _userService = userService;
            _userPasswordHasher = userPasswordHasher;
            _token = token;
            _firebaseService = firebaseService;
            _zaloApiService = zaloApiService;
            _emailService = emailService;
        }

        public async Task VerifyOTPAsync(string otp, string verifyKey)
        {
            string phonePattern = @"^\+?\d{10,15}$";
            string type = "email";
            string redisKey;
            if (Regex.IsMatch(verifyKey, phonePattern))
            {
                type = "phone";
                string formattedPhone = "84" + verifyKey.Substring(1);
                redisKey = $"OTP:{formattedPhone}";
            }
            else
            {
                redisKey = $"OTP:{verifyKey}";
                if (verifyKey.Length < 6 || !verifyKey.Contains("@"))
                {
                    throw new Exception("Email không đúng định dạng.");
                }
            }

            string? cachedOtp = await _redisService.GetStringAsync(redisKey);

            if (cachedOtp == null)
            {
                throw new Exception("Mã OTP này đã hết hạn hoặc không tồn tại, vui lòng gửi yêu cầu cấp lại mã OTP mới.");
            }

            if (cachedOtp != otp)
            {
                throw new Exception("Mã OTP không hợp lệ.");
            }

            await _redisService.DeleteKeyAsync(redisKey);
            User user;
            if (type == "email")
            {
                user = await _userService.FindUserByEmailAsync(verifyKey);
            }
            else
            {
                user = await _userService.FindUserByPhonenumberAsync(verifyKey);
            }

            if (user == null)
            {
                throw new Exception("Tài khoản này không tồn tại");
            }

            user.isVerified = true;
            await _unitOfWork.Users.UpdateAsync(user.id, user);
        }


        public async Task<TokenSetting> LoginAsync(UserLoginRequest request)
        {
            string emailPattern = @"^[^@\s]+@[^@\s]+\.[^@\s]+$";
            string phonePattern = @"^\+?\d{10,15}$";
            if (string.IsNullOrEmpty(request.keyLogin))
            {
                throw new Exception("Tài khoản không đúng định dạng email hoặc số điện thoại.");
            }

            User user = new User();
            if (Regex.IsMatch(request.keyLogin, emailPattern))
            {
                user = await _userService.FindUserByEmailAsync(request.keyLogin);
            }

            if (Regex.IsMatch(request.keyLogin, phonePattern))
            {
                user = await _userService.FindUserByPhonenumberAsync(request.keyLogin);
            }

            var userconvert = new UserDTO
            {
                userName = user.userName,
                about = user.about,
                phoneNumber = user.phoneNumber,
                birthdate = user.birthdate,
                fullName = user.fullName,
                email = user.email,
                password = user.password,
            };

            if (user != null)
            {
                var verifypassword = _userPasswordHasher.VerifyHashedPassword(userconvert, user.password, request.password);
                if (verifypassword == PasswordVerificationResult.Failed)
                {
                    throw new Exception("Sai tài khoản hoặc mật khẩu");
                }
            }
            else
            {
                throw new Exception("Sai tài khoản hoặc mật khẩu");
            }

            if (user.isVerified == false)
            {
                if (Regex.IsMatch(request.keyLogin, emailPattern))
                {
                    await SendEmailOTP(user.email, user.fullName);
                    throw new Exception("Tài khoản chưa được xác thực. Vui lòng kiểm tra email và nhập OTP để xác thực tài khoản của bạn.");
                }
                else if (Regex.IsMatch(request.keyLogin, phonePattern))
                {
                    await SendPhoneOTP(user.phoneNumber, user.fullName);
                    throw new Exception("Tài khoản chưa được xác thực. Vui lòng kiểm tra tin nhắn Zalo hoặc Sms và nhập OTP để xác thực tài khoản của bạn.");
                }
            }

            var usermodel = new UserDTO
            {
                Id = user.id,
                userName = user.userName,
                about = user.about,
                phoneNumber = user.phoneNumber,
                avatar = user.avatar,
                birthdate = user.birthdate,
                fullName = user.fullName,
                email = user.email,
                joinedAt = user.joinedAt,
                role = user.role,
                status = user.status,
                password = user.password,
            };


            var token = await _token.GenerateToken(usermodel);
            return token;
        }

        public async Task RegisterAsync(UserRegister model)
        {
            bool isEmail = ValidationHelper.IsEmail(model.keyRegister);
            bool isPhone = ValidationHelper.IsPhoneNumber(model.keyRegister) && ValidationHelper.IsVietnamesePhone(model.keyRegister);

            if (!isEmail && !isPhone)
            {
                throw new Exception("Tài khoản không đúng định dạng email hoặc số điện thoại");
            }

            string? email = null;
            string? phoneNumber = null;

            if (isEmail)
            {
                email = model.keyRegister;
                var existingEmailUser = await _userService.FindUserByEmailAsync(email);

                if (existingEmailUser != null)
                {
                    if (!existingEmailUser.isVerified)
                    {
                        await SendEmailOTP(email, model.fullName);
                        return;
                    }
                    throw new Exception("Email này đã được đăng kí");
                }

                await SendEmailOTP(email, model.fullName);
            }
            else if (isPhone)
            {
                phoneNumber = ValidationHelper.CleanPhoneForDB(model.keyRegister);
                var existingPhoneUser = await _userService.FindUserByPhonenumberAsync(phoneNumber);
                if (existingPhoneUser != null)
                {
                    if (!existingPhoneUser.isVerified)
                    {
                        await SendPhoneOTP(phoneNumber, model.fullName);
                        return;
                    }
                    throw new Exception("Số điện thoại này đã được đăng kí");

                }
                await SendPhoneOTP(phoneNumber, model.fullName);
            }
            else
            {
                throw new Exception("Không đúng định dạng email hoặc số điện thoại");
            }

            var hashedPassword = _userPasswordHasher.HashPassword(new UserDTO(), model.password);

            var newUser = new User()
            {
                userName = model.userName,
                fullName = model.fullName,
                email = email,
                password = hashedPassword,
                phoneNumber = phoneNumber,
                avatar = model.avatarUrl,
                status = Status.Online,
                about = model.about,
                birthdate = model.birthDate,
                joinedAt = DateTime.UtcNow,
                role = model.role
            };

            await _userService.SaveUserAsync(newUser);
        }

        private async Task SendEmailOTP(string email, string fullName)
        {
            var random = new Random();
            var otpCode = random.Next(100000, 999999).ToString();
            var redisKey = $"OTP:{email}"; // Redis key: OTP:<EMAIL>

            string? cachedOtp = await _redisService.GetStringAsync(redisKey);
            if (cachedOtp != null)
            {
                throw new Exception("Mã OTP đã được gửi. Vui lòng kiểm tra email.");
            }

            await _redisService.SetStringAsync(redisKey, otpCode, TimeSpan.FromMinutes(2));
            var emailContent = EmailTemplate.GetOtpEmailContent(fullName, otpCode, 2);
            _emailService.SendEmailAsync(email, "QuindLand - OTP Verification", emailContent);
        }

        private async Task SendPhoneOTP(string phoneNumber, string fullName)
        {
            try
            {
                await _zaloApiService.SendOTPAsync(phoneNumber);
            }
            catch (Exception e)
            {
                throw new Exception($"Lỗi: {e.Message}");
            }
        }

        public async Task RequestOTPAsync(string verifyKey)
        {
            string phonePattern = @"^\+?\d{10,15}$";
            string emailPattern = @"^[^@\s]+@[^@\s]+\.[^@\s]+$";
            if (Regex.IsMatch(verifyKey, phonePattern))
            {
                string formattedPhone = "84" + verifyKey.Substring(1);
                var random = new Random();
                var otpCode = random.Next(100000, 999999).ToString();
                var redisKey = $"OTP:{formattedPhone}";
                string? cachedOtp = await _redisService.GetStringAsync(redisKey);
                if (cachedOtp != null)
                {
                    throw new Exception("Mã OTP đã được gửi. Vui lòng chờ trong giây lát trước khi gửi yêu cầu mới.");
                }

                var user = await _userService.FindUserByPhonenumberAsync(verifyKey);

                if (user == null)
                {
                    throw new Exception("User not found");
                }
                await _redisService.SetStringAsync(redisKey, otpCode, TimeSpan.FromMinutes(1));
                _zaloApiService.SendOTPAsync(formattedPhone);
            }
            else if (Regex.IsMatch(verifyKey, emailPattern))
            {
                var user = await _userService.FindUserByEmailAsync(verifyKey) ?? throw new Exception("Không tìm thấy người dùng");
                if (user.isVerified)
                {
                    throw new Exception("Email đã được xác thực. Vui lòng đăng nhập.");
                }
                await SendEmailOTP(verifyKey, user.fullName);
            }
            else
            {
                throw new Exception("Tài khoản không đúng định dạng email hoặc số điện thoại");
            }
        }
    }
}
