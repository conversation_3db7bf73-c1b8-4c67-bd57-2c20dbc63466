using BusinessObjects.DTOs.RoommatePreferenceDTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Services.Interfaces
{
    public interface IRoommatePreferenceService
    {
        Task<RoommatePreferenceResponse> GetRoommatePreferenceByUserIdAsync(string userId);
        Task<RoommatePreferenceResponse> UpdateRoommatePreferenceAsync(string userId, RoommatePreferenceUpdateRequest request);
    }
} 