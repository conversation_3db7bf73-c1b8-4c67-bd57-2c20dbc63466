﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace BusinessObjects.Models
{
    public class Amenity
    {
        [BsonElement("parking")]
        public bool parking { get; set; }

        [BsonElement("elevator")]
        public bool? elevator { get; set; }

        [BsonElement("swimmingPool")]
        public bool? swimmingPool { get; set; }

        [BsonElement("gym")]
        public bool? gym { get; set; }

        [BsonElement("securitySystem")]
        public bool? securitySystem { get; set; }

        [BsonElement("airConditioning")]
        public bool? airConditioning { get; set; }

        [BsonElement("balcony")]
        public bool? balcony { get; set; }

        [BsonElement("garden")]
        public bool? garden { get; set; }

        [BsonElement("playground")]
        public bool? playground { get; set; }

        [BsonElement("backupGenerator")]
        public bool? backupGenerator { get; set; }

        [BsonElement("concierge")]
        public bool? concierge { get; set; }

        [BsonElement("reception24h")]
        public bool? reception24h { get; set; }

        [BsonElement("cctv")]
        public bool? cctv { get; set; }

        [BsonElement("accessCard")]
        public bool? accessCard { get; set; }

        [BsonElement("intercom")]
        public bool? intercom { get; set; }

        [BsonElement("mailbox")]
        public bool? mailbox { get; set; }

        [BsonElement("laundryRoom")]
        public bool? laundryRoom { get; set; }

        [BsonElement("storageRoom")]
        public bool? storageRoom { get; set; }

        [BsonElement("bikeStorage")]
        public bool? bikeStorage { get; set; }

        [BsonElement("rooftopTerrace")]
        public bool? rooftopTerrace { get; set; }

        [BsonElement("bbqArea")]
        public bool? bbqArea { get; set; }

        [BsonElement("communityRoom")]
        public bool? communityRoom { get; set; }

        [BsonElement("businessCenter")]
        public bool? businessCenter { get; set; }

        [BsonElement("conferenceRoom")]
        public bool? conferenceRoom { get; set; }

        [BsonElement("kidsPlayArea")]
        public bool? kidsPlayArea { get; set; }

        [BsonElement("petFriendly")]
        public bool? petFriendly { get; set; }

        [BsonElement("petWashStation")]
        public bool? petWashStation { get; set; }

        [BsonElement("furnished")]
        public bool? furnished { get; set; }

        [BsonElement("semifurnished")]
        public bool? semifurnished { get; set; }

        [BsonElement("kitchenAppliances")]
        public bool? kitchenAppliances { get; set; }

        [BsonElement("washingMachine")]
        public bool? washingMachine { get; set; }

        [BsonElement("dryer")]
        public bool? dryer { get; set; }

        [BsonElement("dishwasher")]
        public bool? dishwasher { get; set; }

        [BsonElement("microwave")]
        public bool? microwave { get; set; }

        [BsonElement("refrigerator")]
        public bool? refrigerator { get; set; }

        [BsonElement("oven")]
        public bool? oven { get; set; }

        [BsonElement("waterHeater")]
        public bool? waterHeater { get; set; }

        [BsonElement("fireplace")]
        public bool? fireplace { get; set; }

        [BsonElement("hardwoodFloor")]
        public bool? hardwoodFloor { get; set; }

        [BsonElement("tileFloor")]
        public bool? tileFloor { get; set; }

        [BsonElement("carpetFloor")]
        public bool? carpetFloor { get; set; }

        [BsonElement("walkinCloset")]
        public bool? walkinCloset { get; set; }

        [BsonElement("builtinWardrobe")]
        public bool? builtinWardrobe { get; set; }

        [BsonElement("ensuiteBathroom")]
        public bool? ensuiteBathroom { get; set; }

        [BsonElement("bathtub")]
        public bool? bathtub { get; set; }

        [BsonElement("showerCabin")]
        public bool? showerCabin { get; set; }

        [BsonElement("wifi")]
        public bool? wifi { get; set; }

        [BsonElement("cableTV")]
        public bool? cableTV { get; set; }

        [BsonElement("smartHome")]
        public bool? smartHome { get; set; }

        [BsonElement("smartLock")]
        public bool? smartLock { get; set; }

        [BsonElement("videoIntercom")]
        public bool? videoIntercom { get; set; }

        [BsonElement("nearPublicTransport")]
        public bool? nearPublicTransport { get; set; }

        [BsonElement("nearShopping")]
        public bool? nearShopping { get; set; }

        [BsonElement("nearSchool")]
        public bool? nearSchool { get; set; }

        [BsonElement("nearHospital")]
        public bool? nearHospital { get; set; }

        [BsonElement("nearPark")]
        public bool? nearPark { get; set; }

        [BsonElement("wheelchairAccessible")]
        public bool? wheelchairAccessible { get; set; }

        [BsonElement("waterIncluded")]
        public bool? waterIncluded { get; set; }

        [BsonElement("electricityIncluded")]
        public bool? electricityIncluded { get; set; }

        [BsonElement("gasIncluded")]
        public bool? gasIncluded { get; set; }

        [BsonElement("internetIncluded")]
        public bool? internetIncluded { get; set; }

        [BsonElement("cableIncluded")]
        public bool? cableIncluded { get; set; }

        [BsonElement("cleaningService")]
        public bool? cleaningService { get; set; }

        [BsonElement("maintenanceService")]
        public bool? maintenanceService { get; set; }

        [BsonElement("cityView")]
        public bool? cityView { get; set; }

        [BsonElement("riverView")]
        public bool? riverView { get; set; }

        [BsonElement("mountainView")]
        public bool? mountainView { get; set; }

        [BsonElement("gardenView")]
        public bool? gardenView { get; set; }

        [BsonElement("poolView")]
        public bool? poolView { get; set; }

        [BsonElement("terrace")]
        public bool? terrace { get; set; }

        [BsonElement("patio")]
        public bool? patio { get; set; }

        [BsonElement("privateGarden")]
        public bool? privateGarden { get; set; }
    }
}
