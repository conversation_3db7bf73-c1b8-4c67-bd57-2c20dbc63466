﻿using BusinessObjects.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Repositories.Interfaces
{
    public interface IChatSessionRepository : IGenericRepository<ChatSession>
    {
        Task AddMessageAsync(string sessionId, Message message);
        Task<List<ChatSession>> GetByUserIdAsync(string userId);

        Task<List<ChatSession>> GetAllChatSessionsAsync();
        Task<ChatSession> GetByAnonymousSessionIdAsync(string anonymousSessionId);
    }
}
