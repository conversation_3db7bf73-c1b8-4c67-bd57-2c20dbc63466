﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.Models
{
	public class AgentInvite
	{
		[BsonId]
		[BsonRepresentation(BsonType.ObjectId)]
		public string id { get; set; }

		[BsonElement("email")]
		public string email { get; set; }

		[BsonElement("token")]
		public string token { get; set; }

		[BsonElement("companyId")]
		[BsonRepresentation(BsonType.ObjectId)]
		public string companyId { get; set; }

		[BsonElement("createdAt")]
		public DateTime createdAt { get; set; }

		[BsonElement("expiredAt")]
		public DateTime expiredAt { get; set; }
		[BsonElement("acceptAt")]
		public DateTime? acceptAt { get; set; }

		[BsonElement("isUsed")]
		public bool isUsed { get; set; } = false;

		[BsonRepresentation(BsonType.String)]
		public AgentCompanyRole role { get; set; }
	}
}
