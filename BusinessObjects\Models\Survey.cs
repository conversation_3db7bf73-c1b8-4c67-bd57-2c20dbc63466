using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson;
using System;

namespace BusinessObjects.Models
{
    public class Survey
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string id { get; set; } = ObjectId.GenerateNewId().ToString();

        [BsonElement("full_name")]
        public string fullName { get; set; }

        [BsonElement("email")]
        public string email { get; set; }

        [BsonElement("phone_number")]
        public string phoneNumber { get; set; }

        [BsonElement("property_type_interest")]
        public string propertyTypeInterest { get; set; }

        [BsonElement("budget_category")]
        public BudgetCategory? budgetCategory { get; set; }

        [BsonElement("location_preference")]
        public string locationPreference { get; set; }

        [BsonElement("additional_comments")]
        public string additionalComments { get; set; }

        [BsonElement("created_at")]
        public DateTime createdAt { get; set; } = DateTime.UtcNow;

        [BsonElement("is_converted_to_lead")]
        public bool isConvertedToLead { get; set; } = false;
    }

    public enum BudgetCategory
    {
        [BsonElement("under_1_billion")] Under1Billion,
        [BsonElement("1_to_3_billion")] From1To3Billion,
        [BsonElement("3_to_5_billion")] From3To5Billion,
        [BsonElement("5_to_10_billion")] From5To10Billion,
        [BsonElement("over_10_billion")] Over10Billion
    }
} 