﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace BusinessObjects.Models
{
    public class ChatMessage
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string id { get; set; }
        public string senderId { get; set; }
        public string recipientId { get; set; }
        public string platformUserId { get; set; }
        public string content { get; set; }
        public string messageType { get; set; } = "text";
        public string direction { get; set; }
        public string platform { get; set; }
        public string conversationId { get; set; }
        public string replyToMessageId { get; set; }
        public DateTime createdAt { get; set; } = DateTime.UtcNow;
        public DateTime? updatedAt { get; set; }
        [BsonIgnoreIfNull]
        public List<Attachment> attachments { get; set; } = new List<Attachment>();
        public Boolean isDeleted { get; set; } = false;
        [BsonIgnoreIfNull]
        public BsonDocument? payload { get; set; }

        [BsonIgnoreIfNull]
        public string? groupId { get; set; }
    }

}
