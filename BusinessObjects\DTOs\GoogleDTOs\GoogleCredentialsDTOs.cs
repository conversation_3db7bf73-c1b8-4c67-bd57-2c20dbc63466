﻿using System.ComponentModel.DataAnnotations;

namespace BusinessObjects.DTOs.GoogleDTOs
{
    public class GoogleCredentialsDTOs
    {
        public class SaveGoogleCredentialsRequest
        {
            [Required]
            [StringLength(2048, ErrorMessage = "Access token không hợp lệ")]
            public string accessToken { get; set; }

            [StringLength(512, ErrorMessage = "Refresh token không hợp lệ")]
            public string? refreshToken { get; set; }

            [StringLength(255, ErrorMessage = "Calendar ID không hợp lệ")]
            public string calendarId { get; set; } = "primary";

            [Required]
            public DateTime? tokenExpiry { get; set; }

            [StringLength(500, ErrorMessage = "Scope không hợp lệ")]
            public string? scope { get; set; }

            public string tokenType { get; set; } = "Bearer";
        }

        public class GoogleCredentialResponse
        {
            public string id { get; set; }
            public string userId { get; set; }
            public string calendarId { get; set; }
            public DateTime? tokenExpiry { get; set; }
            public string? scope { get; set; }
            public string tokenType { get; set; }
            public bool isActive { get; set; }
            public DateTime createdAt { get; set; }
            public DateTime updatedAt { get; set; }
            public DateTime? lastUsed { get; set; }
            public bool isTokenValid { get; set; }
        }

        public class UpdateGoogleCredentialsRequest
        {
            [StringLength(255, ErrorMessage = "Calendar ID không hợp lệ")]
            public string? calendarId { get; set; }

            [StringLength(500, ErrorMessage = "Scope không hợp lệ")]
            public string? scope { get; set; }
        }

        public class RefreshTokenRequest
        {
            [Required]
            public string refreshToken { get; set; }
        }
    }
}
