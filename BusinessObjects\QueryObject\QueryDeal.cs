﻿using BusinessObjects.DTOs.RequestFeatures;

namespace BusinessObjects.QueryObject
{
    public class QueryDeal : RequestParameters
    {
        public string? searchTerm { get; set; }

        public string? leadId { get; set; }
        public string? salesRepId { get; set; }
        public DateTime? createdAt { get; set; }
        public DateTime? updatedAt { get; set; }
        public int? createdYear { get; set; }
        public int? createdMonth { get; set; }
        public int? createdDay { get; set; }
        public int? updatedYear { get; set; }
        public int? updatedMonth { get; set; }
        public int? updatedDay { get; set; }
        /// <summary>
        /// The status value, represented as a string (e.g., "New", "Contacted", etc.)
        /// </summary>
        public string? status { get; set; }

        /// <summary>
        /// Filter by priority , e.g. "Low", "Medium", "High"
        /// </summary>
        public string? priority { get; set; }

        public DealEnumSortBy sortBy { get; set; } = DealEnumSortBy.createdAt;

        public bool? isAscending { get; set; }

        public enum DealEnumSortBy
        {
            createdAt,
            updatedAt
        }





    }

}
