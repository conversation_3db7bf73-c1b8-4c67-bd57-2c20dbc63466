﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using DnsClient.Internal;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using Repositories.Interfaces;

namespace Repositories.Implements
{
    public class GoogleCrendentialRepository : GenericRepository<GoogleCredentialModel>, IGoogleCrendentialRepository
    {
        private readonly IMongoCollection<GoogleCredentialModel> _googleCredentials;
        private readonly ILogger<GoogleCrendentialRepository> _logger;

        public GoogleCrendentialRepository(RevoLandDbContext context, ILogger<GoogleCrendentialRepository> logger) : base(context, "GoogleCredentials")
        {
            // Lấy collection từ context
            _googleCredentials = context.GetCollection<GoogleCredentialModel>("GoogleCredentials");
            _logger = logger;
            // Tạo indexes
            CreateIndexes();
        }

        private void CreateIndexes()
        {
            try
            {
                // Index cho userId
                var userIdIndex = Builders<GoogleCredentialModel>.IndexKeys.Ascending(x => x.userId);
                _googleCredentials.Indexes.CreateOne(new CreateIndexModel<GoogleCredentialModel>(userIdIndex));

                // Compound index cho userId và isActive
                var userActiveIndex = Builders<GoogleCredentialModel>.IndexKeys
                    .Ascending(x => x.userId)
                    .Ascending(x => x.isActive);
                _googleCredentials.Indexes.CreateOne(new CreateIndexModel<GoogleCredentialModel>(userActiveIndex));

                // Index cho tokenExpiry
                var expiryIndex = Builders<GoogleCredentialModel>.IndexKeys.Ascending(x => x.tokenExpiry);
                _googleCredentials.Indexes.CreateOne(new CreateIndexModel<GoogleCredentialModel>(expiryIndex));

                // TTL Index cho expired tokens (tự động xóa sau 30 ngày)
                var ttlIndex = Builders<GoogleCredentialModel>.IndexKeys.Ascending(x => x.updatedAt);
                var ttlOptions = new CreateIndexOptions { ExpireAfter = TimeSpan.FromDays(30) };
                _googleCredentials.Indexes.CreateOne(new CreateIndexModel<GoogleCredentialModel>(ttlIndex, ttlOptions));
            }
            catch (Exception ex)
            {
                // Log error but don't throw - indexes are not critical for basic functionality
                Console.WriteLine($"Error creating indexes: {ex.Message}");
            }
        }

        public async Task<GoogleCredentialModel?> GetByUserIdAsync(string userId)
        {
            var filter = Builders<GoogleCredentialModel>.Filter.Eq(x => x.userId, userId);
            return await _googleCredentials.Find(filter).FirstOrDefaultAsync();
        }

        public async Task<GoogleCredentialModel?> GetActiveByUserIdAsync(string userId)
        {
            var filter = Builders<GoogleCredentialModel>.Filter.And(
                Builders<GoogleCredentialModel>.Filter.Eq(x => x.userId, userId),
                Builders<GoogleCredentialModel>.Filter.Eq(x => x.isActive, true)
            );
            return await _googleCredentials.Find(filter).FirstOrDefaultAsync();
        }




        public async Task<bool> DeactivateByUserIdAsync(string userId)
        {
            var filter = Builders<GoogleCredentialModel>.Filter.Eq(x => x.userId, userId);
            var update = Builders<GoogleCredentialModel>.Update
                .Set(x => x.isActive, false)
                .Set(x => x.updatedAt, DateTime.UtcNow);

            var result = await _googleCredentials.UpdateManyAsync(filter, update);
            return result.ModifiedCount > 0;
        }

        public async Task<bool> UpdateTokenAsync(string userId, string accessToken, DateTime? expiry)
        {
            var filter = Builders<GoogleCredentialModel>.Filter.And(
                Builders<GoogleCredentialModel>.Filter.Eq(x => x.userId, userId),
                Builders<GoogleCredentialModel>.Filter.Eq(x => x.isActive, true)
            );

            var update = Builders<GoogleCredentialModel>.Update
                .Set(x => x.accessToken, accessToken)
                .Set(x => x.tokenExpiry, expiry)
                .Set(x => x.updatedAt, DateTime.UtcNow)
                .Set(x => x.lastUsed, DateTime.UtcNow);

            var result = await _googleCredentials.UpdateOneAsync(filter, update);
            return result.ModifiedCount > 0;
        }

        public async Task<List<GoogleCredentialModel>> GetExpiredTokensAsync()
        {
            var now = DateTime.UtcNow;
            var filter = Builders<GoogleCredentialModel>.Filter.And(
                Builders<GoogleCredentialModel>.Filter.Eq(x => x.isActive, true),
                Builders<GoogleCredentialModel>.Filter.Lte(x => x.tokenExpiry, now)
            );

            return await _googleCredentials.Find(filter).ToListAsync();
        }

        public async Task<bool> UpdateLastUsedAsync(string userId)
        {
            var filter = Builders<GoogleCredentialModel>.Filter.And(
                Builders<GoogleCredentialModel>.Filter.Eq(x => x.userId, userId),
                Builders<GoogleCredentialModel>.Filter.Eq(x => x.isActive, true)
            );

            var update = Builders<GoogleCredentialModel>.Update
                .Set(x => x.lastUsed, DateTime.UtcNow)
                .Set(x => x.updatedAt, DateTime.UtcNow);

            var result = await _googleCredentials.UpdateOneAsync(filter, update);
            return result.ModifiedCount > 0;
        }

        public async Task<bool> IsTokenValidAsync(string userId)
        {
            try
            {
                var credential = await GetActiveByUserIdAsync(userId);
                if (credential == null)
                {
                    _logger.LogWarning($"❌ No credentials found for user {userId}");
                    return false;
                }

                // ✅ CHỈ check validity 
                if (credential.tokenExpiry.HasValue)
                {
                    var isValid = credential.tokenExpiry.Value > DateTime.UtcNow.AddMinutes(5);
                    _logger.LogInformation($"Token validity for user {userId}: {isValid} (expires: {credential.tokenExpiry.Value})");
                    return isValid;
                }

                _logger.LogInformation($"✅ Token has no expiry for user {userId} - considered valid");
                return true; // Nếu không có expiry thì coi như valid
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"❌ Error checking token validity for user {userId}");
                return false;
            }
        }


        public async Task<GoogleCredentialModel?> RefreshTokenAsync(string userId, string newAccessToken, DateTime? newExpiry)
        {
            var filter = Builders<GoogleCredentialModel>.Filter.And(
                Builders<GoogleCredentialModel>.Filter.Eq(x => x.userId, userId),
                Builders<GoogleCredentialModel>.Filter.Eq(x => x.isActive, true)
            );

            var update = Builders<GoogleCredentialModel>.Update
                .Set(x => x.accessToken, newAccessToken)
                .Set(x => x.tokenExpiry, newExpiry)
                .Set(x => x.updatedAt, DateTime.UtcNow)
                .Set(x => x.lastUsed, DateTime.UtcNow);

            var options = new FindOneAndUpdateOptions<GoogleCredentialModel>
            {
                ReturnDocument = ReturnDocument.After
            };

            return await _googleCredentials.FindOneAndUpdateAsync(filter, update, options);
        }
    }

}
