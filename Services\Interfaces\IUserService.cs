using BusinessObjects.DTOs.UserDTOs;
using BusinessObjects.Models;
using Microsoft.AspNetCore.Http;
using BusinessObjects.QueryObject;

namespace Services.Interfaces
{
    public interface IUserService
    {
        Task<User> FindUserByEmailAsync(string email);
        Task<User> FindUserByUsernameAsync(string username);
        Task SaveUserAsync(User user);
        Task<User> FindUserByPhonenumberAsync(string phonenumber);
        Task<User> FindUserByIdAsync(string id);
        Task UpdateUserAsync(string id, User user);
        Task<UserActionResponse> UpdateUserProfileAsync(string userId, UserUpdateRequest request, IFormFile? avatarFile);
        Task<UserProfileResponse> GetUserProfileAsync(string id);
        Task<object> GetSellersAsync(QuerySeller query);
        Task<UserRoomateInfoResponse> GetUserRoomateInfoAsync(string userId);
        Task<User> UpdateUserRoomateInfoAsync(string userId, UserUpdateRoomateInfoRequest request);
        Task<UserActionResponse> ToggleRoommateAsync(string userId, bool isActive);
	}
}
