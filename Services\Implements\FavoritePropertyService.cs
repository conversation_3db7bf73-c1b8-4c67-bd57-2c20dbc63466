﻿using BusinessObjects.DTOs.FavoritePropertyDTOs;
using BusinessObjects.DTOs.LeadDTOs;
using BusinessObjects.DTOs.PropertyDTO;
using BusinessObjects.Models;
using BusinessObjects.QueryObject;
using Microsoft.AspNetCore.Http;
using Repositories.Interfaces;
using Services.Interface;
using Services.Interfaces;
using System.Security.Claims;

namespace Services.Implements
{
    public class FavoritePropertyService : IFavoritePropertyService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly FirebaseService _firebaseService;
        private readonly IUserService _userService;
        private readonly ITransactionHistoryService _transactionHistoryService;
        private readonly ISearchService _searchService;
        private readonly IHttpContextAccessor _httpContextAccessor;
        public FavoritePropertyService(IUnitOfWork unitOfWork, FirebaseService firebaseService, IUserService userService, ITransactionHistoryService transactionHistoryService, ISearchService searchService, IHttpContextAccessor httpContextAccessor)
        {
            _unitOfWork = unitOfWork;
            _firebaseService = firebaseService;
            _userService = userService;
            _transactionHistoryService = transactionHistoryService;
            _searchService = searchService;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task<FavoritePropertyCreateRequest> AddFavoritePropertyAsync(FavoritePropertyCreateRequest request)
        {
            var userId = _httpContextAccessor.HttpContext?.User?.FindFirstValue("UserID");
            if (userId == null)
            {
                throw new UnauthorizedAccessException("Không tìm thấy người dùng");
            }

            var lead = await _unitOfWork.Leads.GetLeadByUserIdAsync(userId);

            if (lead == null)
            {
                var user = await _userService.FindUserByIdAsync(userId);
                lead = new Lead
                {
                    userId = userId,
                    createdAt = DateTime.UtcNow,
                    updatedAt = DateTime.UtcNow,
                    email = user.email,
                    name = user.fullName,
                    phone = user.phoneNumber,
                    score = LeadScore.Hot,
                    source = LeadSource.Web,
                    favoriteProperties = new List<AddFavoritePropertyRequest>{
                        new AddFavoritePropertyRequest
                        {
                            propertyId = request.propertyId,
                            savedAt = DateTime.UtcNow
                        }
                    }
                };
                await _unitOfWork.Leads.AddAsync(lead);
            }
            else
            {
                lead.favoriteProperties.Add(new AddFavoritePropertyRequest
                {
                    propertyId = request.propertyId,
                    savedAt = DateTime.UtcNow
                });
                lead.updatedAt = DateTime.UtcNow;
                lead.score = LeadScore.Hot;
                await _unitOfWork.Leads.UpdateAsync(lead.id, lead);
            }

            // Check if the property exists
            var property = await _unitOfWork.Properties.GetByIdAsync(request.propertyId);
            if (property == null)
            {
                throw new InvalidDataException("Không tìm thấy property");
            }

            // Check if the property is already favorited by the user
            var existingFavorite = await _unitOfWork.FavoriteProperties.GetByUserIdAndPropertyIdAsync(userId, request.propertyId);
            if (existingFavorite != null)
            {
                throw new InvalidDataException("Property này đã tồn tại trong danh sách yêu thích của bạn");
            }

            // Add the favorite property
            var favoriteProperty = new FavoriteProperty
            {
                propertyId = request.propertyId,
                userId = userId,
                savedAt = DateTime.UtcNow
            };

            await _unitOfWork.FavoriteProperties.AddAsync(favoriteProperty);
            await _unitOfWork.SaveAsync();

            return new FavoritePropertyCreateRequest
            {
                propertyId = favoriteProperty.propertyId,
                savedAt = favoriteProperty.savedAt
            };
        }

        public async Task DeleteFavoritePropertyAsync(string propertyId)
        {
            var userId = _httpContextAccessor.HttpContext?.User?.FindFirstValue("UserID");
            if (userId == null)
            {
                throw new UnauthorizedAccessException("Không tìm thấy người dùng");
            }
            // Check if the favorite property exists
            var favoriteProperty = await _unitOfWork.FavoriteProperties.GetByUserIdAndPropertyIdAsync(userId, propertyId);
            if (favoriteProperty == null)
            {
                throw new InvalidDataException("Không tìm thấy property này trong danh sách yêu thích của bạn");
            }
            // Remove the favorite property
            await _unitOfWork.FavoriteProperties.DeleteAsync(favoriteProperty.id.ToString());
            await _unitOfWork.SaveAsync();
        }
        public async Task<List<PropertySearchResponse>> GetFavoritePropertyByUserIdAsync(QueryProperty query, string userId)
        {
            if (query.searchTerm == null)
            {
                query.searchTerm = "";
            }
            else
            {
                _searchService.AddSearchAsync(query);
            }
            // Add debug logging
            var favoriteProperties = await _unitOfWork.FavoriteProperties.GetByUserIdAsync(userId);
            if (favoriteProperties == null || !favoriteProperties.Any())
            {
                return new List<PropertySearchResponse>();
            }

            // Get the property IDs from favorite properties
            var propertyIds = favoriteProperties.Select(fp => fp.propertyId).ToList();

            // Get the properties
            var properties = await _unitOfWork.Properties.GetByIdsAsync(propertyIds);
            if (properties == null || !properties.Any())
            {
                return new List<PropertySearchResponse>();
            }

            // Apply filters and return the result
            var result = await ApplyPropertyFilters(query, properties.ToList());
            return new List<PropertySearchResponse> { result };
        }
        private async Task<PropertySearchResponse> ApplyPropertyFilters(QueryProperty query, List<Property> result)
        {
            var propertyList = new List<PropertyCreateResponse>();

            if (query.type == null && query.status == null)
            {
                foreach (var p in result)
                {
                    propertyList.Add(await ToPropertyResponse(p));
                }
                return new PropertySearchResponse
                {
                    properties = propertyList,
                    page = query.pageNumber,
                    limit = query.pageSize,
                    totalPages = (int)Math.Ceiling(propertyList.Count / (double)query.pageSize),
                    count = propertyList.Count()
                };
            }

            if (result == null || !result.Any())
            {
                return new PropertySearchResponse();
            }

            result = result.Where(p => p.name.Contains(query.searchTerm, StringComparison.OrdinalIgnoreCase) ||
                                       p.contactName.Contains(query.searchTerm, StringComparison.OrdinalIgnoreCase) ||
                                       p.description.Contains(query.searchTerm, StringComparison.OrdinalIgnoreCase) ||
                                       p.location.address.Contains(query.searchTerm, StringComparison.OrdinalIgnoreCase)).ToList();

            if (query.isFeatured != null)
            {
                result = result.Where(p => p.isFeatured == query.isFeatured).ToList();
            }

            //if (query.status != null && query.status.Any())
            //{
            //    var statusList = query.status
            //        .Where(id => Enum.IsDefined(typeof(PropertyStatus), id))
            //        .Select(id => (PropertyStatus)id)
            //        .ToList();

            //    result = result.Where(p => statusList.Contains(p.status)).ToList();
            //}

            //if (query.transactionType != null && query.transactionType.Any())
            //{
            //    var transactionTypeList = query.transactionType
            //        .Where(id => Enum.IsDefined(typeof(PropertyTransactionType), id))
            //        .Select(id => (PropertyTransactionType)id)
            //        .ToList();

            //    result = result.Where(p => transactionTypeList.Contains(p.transactionType)).ToList();
            //}

            //if (query.type != null && query.type.Any())
            //{
            //    var typeList = query.type
            //        .Where(id => Enum.IsDefined(typeof(PropertyType), id))
            //        .Select(id => (PropertyType)id)
            //        .ToList();

            //    result = result.Where(p => typeList.Contains(p.type)).ToList();
            //}

            if (query.amenityFilters != null)
            {
                foreach (var filter in query.amenityFilters)
                {
                    switch (filter)
                    {
                        case AmenityFilter.Parking:
                            result = result.Where(p => p.amenities.parking == true).ToList();
                            break;
                        case AmenityFilter.Elevator:
                            result = result.Where(p => p.amenities.elevator == true).ToList();
                            break;
                        case AmenityFilter.SwimmingPool:
                            result = result.Where(p => p.amenities.swimmingPool == true).ToList();
                            break;
                        case AmenityFilter.Gym:
                            result = result.Where(p => p.amenities.gym == true).ToList();
                            break;
                        case AmenityFilter.SecuritySystem:
                            result = result.Where(p => p.amenities.securitySystem == true).ToList();
                            break;
                        case AmenityFilter.AirConditioning:
                            result = result.Where(p => p.amenities.airConditioning == true).ToList();
                            break;
                        case AmenityFilter.Balcony:
                            result = result.Where(p => p.amenities.balcony == true).ToList();
                            break;
                        case AmenityFilter.Garden:
                            result = result.Where(p => p.amenities.garden == true).ToList();
                            break;
                        case AmenityFilter.Playground:
                            result = result.Where(p => p.amenities.playground == true).ToList();
                            break;
                        case AmenityFilter.BackupGenerator:
                            result = result.Where(p => p.amenities.backupGenerator == true).ToList();
                            break;
                    }
                }
            }

            if (query.propertyDetailFilters != null && query.propertyDetailFilters.Any())
            {
                foreach (var filter in query.propertyDetailFilters)
                {
                    if (filter == PropDetailBoolFilter.hasBasement)
                    {
                        result = result.Where(p => p.propertyDetails.hasBasement == true).ToList();
                    }
                    else if (filter == PropDetailBoolFilter.furnished)
                    {
                        result = result.Where(p => p.propertyDetails.furnished == true).ToList();
                    }
                }
            }

            if (query.minBedrooms.HasValue)
            {
                result = result.Where(p => p.propertyDetails.bedrooms >= query.minBedrooms.Value).ToList();
            }
            if (query.maxBedrooms.HasValue)
            {
                result = result.Where(p => p.propertyDetails.bedrooms <= query.maxBedrooms.Value).ToList();
            }

            if (query.bathrooms.HasValue)
            {
                result = result.Where(p => p.propertyDetails.bathrooms == query.bathrooms.Value).ToList();
            }


            if (query.minLivingRooms.HasValue)
            {
                result = result.Where(p => p.propertyDetails.landArea >= query.minLivingRooms.Value).ToList();
            }

            if (query.maxLivingRooms.HasValue)
            {
                result = result.Where(p => p.propertyDetails.landArea <= query.maxLivingRooms.Value).ToList();
            }

            if (query.kitchens.HasValue)
            {
                result = result.Where(p => p.propertyDetails.kitchens == query.kitchens.Value).ToList();
            }


            if (query.minLandArea.HasValue)
            {
                result = result.Where(p => p.propertyDetails.landArea >= query.minLandArea.Value).ToList();
            }

            if (query.maxLandArea.HasValue)
            {
                result = result.Where(p => p.propertyDetails.landArea <= query.maxLandArea.Value).ToList();
            }

            if (query.landWidth.HasValue)
            {
                result = result.Where(p => p.propertyDetails.landWidth == query.landWidth.Value).ToList();
            }

            if (query.landLength.HasValue)
            {
                result = result.Where(p => p.propertyDetails.landLength == query.landLength.Value).ToList();
            }

            if (query.minBuildingArea.HasValue)
            {
                result = result.Where(p => p.propertyDetails.buildingArea >= query.minBuildingArea.Value).ToList();
            }

            if (query.maxBuildingArea.HasValue)
            {
                result = result.Where(p => p.propertyDetails.buildingArea <= query.maxBuildingArea.Value).ToList();
            }

            if (query.numberOfFloors.HasValue)
            {
                result = result.Where(p => p.propertyDetails.numberOfFloors == query.numberOfFloors.Value).ToList();
            }

            if (query.floorNumber.HasValue)
            {
                result = result.Where(p => p.propertyDetails.floorNumber == query.floorNumber.Value).ToList();
            }

            //if (!string.IsNullOrEmpty(query.apartmentOrientation.ToString()) &&
            //Enum.TryParse<ApartmentOrientation>(query.apartmentOrientation.ToString(), out var orientation))
            //{
            //    result = result.Where(p => p.propertyDetails.apartmentOrientation == orientation.ToString()).ToList();
            //}

            if (query.type != null && query.type.Any(t => t.ToString().Contains("rent")))
            {
                switch (query.sortBy)
                {
                    case PropertyEnumSortBy.name:
                        result = query.isDescending ? result.OrderByDescending(p => p.name).ToList() : result.OrderBy(p => p.name).ToList();
                        break;
                    case PropertyEnumSortBy.price:
                        result = query.isDescending ? result.OrderByDescending(p => p.priceDetails?.rentalPrice).ToList() : result.OrderBy(p => p.priceDetails?.rentalPrice).ToList();
                        break;
                    default:
                        result = result.OrderBy(p => p.name).ToList();
                        break;
                }
            }
            else
            {
                switch (query.sortBy)
                {
                    case PropertyEnumSortBy.name:
                        result = query.isDescending ? result.OrderByDescending(p => p.name).ToList() : result.OrderBy(p => p.name).ToList();
                        break;
                    case PropertyEnumSortBy.price:
                        result = query.isDescending ? result.OrderByDescending(p => p.priceDetails?.salePrice).ToList() : result.OrderBy(p => p.priceDetails?.salePrice).ToList();
                        break;
                    default:
                        result = result.OrderBy(p => p.name).ToList();
                        break;
                }
            }

            int totalRecords = result.Count();
            int totalPages = (int)Math.Ceiling(totalRecords / (double)query.pageSize);
            int currentPage = query.pageNumber;

            var paginatedProperties = result
                .Skip((currentPage - 1) * query.pageSize)
                .Take(query.pageSize)
                .ToList();

            foreach (var property in paginatedProperties)
            {
                propertyList.Add(await ToPropertyResponse(property));
            }

            return new PropertySearchResponse
            {
                properties = propertyList,
                page = currentPage,
                limit = query.pageSize,
                totalPages = totalPages,
                count = result.Count()
            };
        }

        private async Task<PropertyCreateResponse> ToPropertyResponse(Property property)
        {
            User user = null;
            if (property.ownerId != null)
            {
                user = await _unitOfWork.Users.GetByIdAsync(property.ownerId);
            }
            else if (property.salerId != null)
            {
                user = await _unitOfWork.Users.GetByIdAsync(property.salerId);
            }

            var userResponse = new UserResponse
            {
                avatar = user.avatar,
                email = user.email,
                fullName = user.fullName,
                id = user.id,
                phoneNumber = user.phoneNumber,
                gender = user.gender.ToString(),
            };
            return new PropertyCreateResponse
            {
                id = property.id.ToString(),
                saler = property.salerId != null ? userResponse : null,
                title = property.title,
                name = property.name,
                description = property.description,
                type = property.type.ToString(),
                status = property.status.ToString(),
                adminNote = property.adminNote,
                code = property.code,
                owner = property.ownerId != null ? userResponse : null,
                location = property.location == null ? null : new LocationResponse
                {
                    address = property.location.address,
                    city = property.location.city,
                    district = property.location.district,
                    ward = property.location.ward,
                    latitude = property.location.latitude,
                    longitude = property.location.longitude
                },
                propertyDetails = property.propertyDetails,
                priceDetails = property.priceDetails,
                amenities = property.amenities,
                imageUrls = property.images,
                floorPlanUrls = property.floorPlans,
                yearBuilt = property.yearBuilt,
                legalDocumentUrls = property.legalDocuments,
                video = property.video,
                transactionHistory = property.transactionHistory?.Select(th => new TransactionHistory
                {
                    transactionDate = th.transactionDate,
                    transactionType = th.transactionType,
                    price = th.price,
                    buyerId = th.buyerId,
                    sellerId = th.sellerId
                }).ToList(),
                contactName = property.contactName,
                contactPhone = property.contactPhone,
                contactEmail = property.contactEmail,
                createdAt = property.createdAt,
                updatedAt = property.updatedAt,
                isFeatured = property.isFeatured,
                transactionType = property.transactionType.ToString(),
                isVerified = property.isVerified,
                isFavorite = true
            };
        }
    }
}