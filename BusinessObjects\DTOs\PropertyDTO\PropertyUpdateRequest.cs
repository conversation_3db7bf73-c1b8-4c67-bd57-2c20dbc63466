﻿using BusinessObjects.Models;
using System.ComponentModel.DataAnnotations;

namespace BusinessObjects.DTOs.PropertyDTO
{
    public class PropertyUpdateRequest
    {
        [StringLength(255, ErrorMessage = "Property title cannot exceed 255 characters.")]
        public string? title { get; set; }

        [StringLength(255, ErrorMessage = "Property name cannot exceed 255 characters.")]
        public string? name { get; set; }

        [StringLength(1000, ErrorMessage = "Description cannot exceed 1000 characters.")]
        public string? description { get; set; }
        public PropertyTransactionType? transactionType { get; set; }
        public PropertyType? type { get; set; }
        public PropertyStatus? status { get; set; }

        public string? adminNote { get; set; }

        [StringLength(50, ErrorMessage = "Code cannot exceed 50 characters.")]
        public string? code { get; set; }

        public string? ownerId { get; set; }

        public LocationUpdateRequest? location { get; set; }

        public PropertyDetail? propertyDetails { get; set; }

        public PriceDetailCreateRequest? priceDetails { get; set; }

        public Amenity? amenities { get; set; }

        public List<string>? images { get; set; }

        public int? yearBuilt { get; set; }

        public List<string>? legalDocuments { get; set; }

        public List<string>? floorPlans { get; set; }
        public PropertyVideo? video { get; set; }

        public string? transactionHistoryId { get; set; }

        public bool? isFeatured { get; set; }

        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            if (!IsValidYearBuilt(yearBuilt))
            {
                yield return new ValidationResult(
                    $"Year built must be between 1800 and {DateTime.Now.Year}.",
                    new[] { nameof(yearBuilt) }
                );
            }
        }

        private bool IsValidYearBuilt(int? year)
        {
            int currentYear = DateTime.Now.Year;
            return year.HasValue && year.Value >= 1800 && year.Value <= currentYear;
        }
    }

}
