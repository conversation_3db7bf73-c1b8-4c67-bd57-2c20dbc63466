﻿using BusinessObjects.Models;
using Microsoft.Extensions.Configuration;
using Repositories.Interfaces;
using Services.Interfaces;
using System.Text.Json;

namespace Services.Implements
{
    public class ZaloTokenService : IZaloTokenService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _config;

        public ZaloTokenService(IUnitOfWork unitOfWork, HttpClient httpClient, IConfiguration config)
        {
            _unitOfWork = unitOfWork;
            _httpClient = httpClient;
            _config = config;
        }

        public async Task<string> GetValidAccessTokenAsync()
        {
            try
            {
                var tokenInfo = (await _unitOfWork.ZaloTokens.GetAllAsync()).FirstOrDefault();

                if (tokenInfo == null)
                {
                    tokenInfo = new ZaloToken
                    {
                        accessToken = string.Empty,
                        refreshToken = string.Empty,
                        accessTokenExpiresAt = DateTime.UtcNow,
                        refreshTokenExpiresAt = DateTime.UtcNow.AddDays(90),
                        createdAt = DateTime.UtcNow,
                        updatedAt = DateTime.UtcNow
                    };

                    await _unitOfWork.ZaloTokens.AddAsync(tokenInfo);
                }

                if (tokenInfo.accessTokenExpiresAt <= DateTime.UtcNow.AddMinutes(5))
                {
                    await RefreshAccessTokenAsync(tokenInfo);
                }

                return tokenInfo.accessToken;
            }
            catch (Exception ex)
            {
                Console.WriteLine("🔥 Exception in GetValidAccessTokenAsync: " + ex.Message);
                throw;
            }
        }

        private async Task RefreshAccessTokenAsync(ZaloToken tokenInfo)
        {

            try
            {
                var appId = _config["Zalo:AppId"];
                var appSecret = _config["Zalo:AppSecret"];

                if (string.IsNullOrWhiteSpace(appId) || string.IsNullOrWhiteSpace(appSecret))
                {
                    throw new Exception("Thiếu AppId hay AppSecret trong cấu hình.");
                }

                var request = new HttpRequestMessage(HttpMethod.Post, "https://oauth.zaloapp.com/v4/oa/access_token")
                {
                    Content = new FormUrlEncodedContent(new[]
                    {
                    new KeyValuePair<string, string>("app_id", appId),
                    new KeyValuePair<string, string>("grant_type", "refresh_token"),
                    new KeyValuePair<string, string>("refresh_token", tokenInfo.refreshToken)
                })
                };
                request.Headers.Add("secret_key", appSecret);

                var response = await _httpClient.SendAsync(request);

                var content = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    throw new Exception($"Thất bại khi refresh token zalo {content}");
                }

                var json = JsonDocument.Parse(content).RootElement;
                int expiresIn = int.Parse(json.GetProperty("expires_in").GetString());

                tokenInfo.accessToken = json.GetProperty("access_token").GetString();
                tokenInfo.accessTokenExpiresAt = DateTime.UtcNow.AddSeconds(expiresIn);
                tokenInfo.refreshToken = json.GetProperty("refresh_token").GetString();
                tokenInfo.refreshTokenExpiresAt = DateTime.UtcNow.AddDays(90);
                tokenInfo.updatedAt = DateTime.UtcNow;

                await _unitOfWork.ZaloTokens.UpdateAsync(tokenInfo.id.ToString(), tokenInfo);
            }
            catch (Exception ex)
            {
                Console.WriteLine("🔥 Exception in RefreshAccessTokenAsync: " + ex.Message);
                throw;

            }
        }

        public async Task SaveOAuthTokenAsync(string accessToken, string refreshToken, int expiresIn)
        {
            var tokenInfo = (await _unitOfWork.ZaloTokens.GetAllAsync()).FirstOrDefault();

            if (tokenInfo == null)
            {
                tokenInfo = new ZaloToken
                {
                    createdAt = DateTime.UtcNow
                };
            }

            tokenInfo.accessToken = accessToken;
            tokenInfo.refreshToken = refreshToken;
            tokenInfo.accessTokenExpiresAt = DateTime.UtcNow.AddSeconds(expiresIn);
            tokenInfo.refreshTokenExpiresAt = DateTime.UtcNow.AddDays(90); // reset
            tokenInfo.updatedAt = DateTime.UtcNow;

            if (string.IsNullOrEmpty(tokenInfo.id))
            {
                await _unitOfWork.ZaloTokens.AddAsync(tokenInfo);
            }
            else
            {
                await _unitOfWork.ZaloTokens.UpdateAsync(tokenInfo.id.ToString(), tokenInfo);
            }
        }
    }
}
