﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.TenantDTOs;
using BusinessObjects.QueryObject;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Services.Implements;
using Services.Interfaces;

namespace Controllers.Controllers
{
    [Route("api/tenants")]
    [ApiController]
    public class TenantController : ControllerBase
    {
        private readonly ITenantService _tenantService;
        private readonly FirebaseService _firebaseService;

        public TenantController(ITenantService tenantService, FirebaseService firebaseService)
        {
            _tenantService = tenantService;
            _firebaseService = firebaseService;
        }

        private ApiResponse CreateResponse(int code, bool status, string message, object data = null)
        {
            return new ApiResponse { code = code, status = status, message = message, data = data };
        }
        [Authorize]
        [HttpGet]
        public async Task<IActionResult> GetAllTenants([FromQuery] QueryTenant query)
        {
            try
            {
                if (!ModelState.IsValid)
                    return BadRequest(CreateResponse(400, false, "Invalid model", ModelState));

                var tenants = await _tenantService.GetAllTenantsAsync(query);
                return Ok(CreateResponse(200, true, "List of tenants retrieved successfully", tenants));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateResponse(500, false, "Internal server error: " + ex.Message));
            }
        }
        [Authorize]
        [HttpGet("{id}")]
        public async Task<IActionResult> GetTenantById(string id)
        {
            try
            {
                var tenant = await _tenantService.GetTenantByIdAsync(id);
                if (tenant == null)
                    return NotFound(CreateResponse(404, false, "Tenant not found"));

                return Ok(CreateResponse(200, true, "Tenant retrieved successfully", tenant));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateResponse(500, false, "Internal server error: " + ex.Message));
            }
        }
        [Authorize]
        [HttpGet("phone")]
        public async Task<IActionResult> GetTenantByPhone(string phone)
        {
            try
            {
                var tenant = await _tenantService.GetTenantByPhoneAsync(phone);
                if (tenant == null)
                    return NotFound(CreateResponse(404, false, "Tenant not found"));

                return Ok(CreateResponse(200, true, "Tenant retrieved successfully", tenant));
            }

            catch (Exception ex)
            {
                return StatusCode(500, CreateResponse(500, false, "Internal server error: " + ex.Message));
            }
        }
        [Authorize]
        [HttpPost]
        public async Task<IActionResult> AddTenant([FromBody] TenantCreateRequest tenant)
        {
            try
            {
             //   var idVerificationUrl = await _tenantService.UploadIdVerificationAsync(tenant.idVerification);
                var createdTenant = await _tenantService.AddTenantAsync(tenant, tenant.idVerification);
                await _tenantService.CreateUserFromTenantAsync(tenant);

				return CreatedAtAction(nameof(GetTenantById), new { id = createdTenant.id }, new ApiResponse { code = 201, status = true, message = "Tenant added successfully", data = createdTenant });
			}
            catch (ArgumentException argEx)
            {
                return BadRequest(CreateResponse(400, false, argEx.Message));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateResponse(500, false, "Internal server error: " + ex.Message));
            }
        }
        [Authorize]
        [HttpPut]
        public async Task<IActionResult> UpdateTenant([FromForm] TenantUpdateRequest tenant)
        {
            try
            {
                var updated = await _tenantService.UpdateTenantAsync(tenant, tenant.idVerification);
                if (!updated)
                    return NotFound(CreateResponse(404, false, "Tenant does not exist or no changes were made"));

                return Ok(CreateResponse(200, true, "Tenant updated successfully"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateResponse(500, false, "Internal server error: " + ex.Message));
            }
        }
        [Authorize]
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteTenant(string id)
        {
            try
            {
                await _tenantService.DeleteTenantAsync(id);
                return Ok(CreateResponse(200, true, "Tenant deleted successfully"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateResponse(500, false, "Internal server error: " + ex.Message));
            }
        }
    }
}
