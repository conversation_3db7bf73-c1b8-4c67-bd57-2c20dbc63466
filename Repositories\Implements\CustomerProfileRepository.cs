﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using MongoDB.Driver;
using Repositories.Interfaces;

namespace Repositories.Implements
{
    public class CustomerProfileRepository : GenericRepository<CustomerProfile>, ICustomerProfileRepository
    {
        private readonly IMongoCollection<CustomerProfile> _customerProfiles;

        public CustomerProfileRepository(RevoLandDbContext context) : base(context, "CustomerProfiles")
        {
            _customerProfiles = context.GetCollection<CustomerProfile>("CustomerProfiles");
        }

        public async Task<CustomerProfile?> GetByPlatformUserIdAsync(string platform, string platformUserId)
        {
            var filter = Builders<CustomerProfile>.Filter.And(
                Builders<CustomerProfile>.Filter.Eq(x => x.platform, platform),
                Builders<CustomerProfile>.Filter.Eq(x => x.platformUserId, platformUserId)
            );

            return await _customerProfiles.Find(filter).FirstOrDefaultAsync();
        }

        public async Task UpdateLastInteraction(string id)
        {
            var filter = Builders<CustomerProfile>.Filter.Eq(x => x.id, id);
            var update = Builders<CustomerProfile>.Update.Set(x => x.lastInteraction, DateTime.UtcNow);
            await _customerProfiles.UpdateOneAsync(filter, update);
        }
    }
}
