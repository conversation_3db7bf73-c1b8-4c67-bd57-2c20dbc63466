﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using MongoDB.Driver;
using Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Repositories.Implements
{
	public class AgentActivityLogRepository : GenericRepository<AgentActivityLog>, IAgentActivityLogRepository
	{
		private readonly IMongoCollection<AgentActivityLog> _mongoCollection;
		public AgentActivityLogRepository(RevoLandDbContext context) : base(context, "AgentActivityLogs")
		{
			_mongoCollection = context.GetCollection<AgentActivityLog>("AgentActivityLogs");
		}

		public Task<List<AgentActivityLog>> GetActivityLogsByAgentIdAsync(string agentId)
		{
			var filter = Builders<AgentActivityLog>.Filter.Eq(log => log.agentId, agentId);
			return _mongoCollection.Find(filter).ToListAsync();
		}
	}
}
