﻿using BusinessObjects.Models;
using MongoDB.Bson;
using MongoDB.Driver;
using System.Linq.Expressions;

namespace Repositories.Interfaces
{
    public interface IDealRepository : IGenericRepository<Deal>
    {
        Task<Deal> UpdateStatusAsync(string id, Deal.DealStatus status);

        // Phương thức truy vấn dựa trên filter, sort và phân trang
        Task<List<Deal>> GetDealsByFilterAsync(FilterDefinition<Deal> filter, SortDefinition<Deal> sort, int skip, int limit);

        Task<long> CountDealsWithFilterAsync(FilterDefinition<Deal> filter);

        public Task<List<Deal>> GetManyAsync(
            Expression<Func<Deal, bool>> filter,
            Func<IQueryable<Deal>, IOrderedQueryable<Deal>> orderBy);

        public Task<IEnumerable<TResult>> AggregateAsync<TResult>(BsonDocument[] pipeline);

        public Task<IEnumerable<TResult>> AggregateAsync<TResult>(PipelineDefinition<Deal, TResult> pipeline);
    }
}
