﻿using System.ComponentModel.DataAnnotations;

namespace BusinessObjects.DTOs.CalendarDTOs
{
    public class GoogleCalendarIntegration
    {
        /// <summary>
        /// Request model for getting user events (without access token)
        /// </summary>
        public class GetUserEventsRequest
        {
            [Required]
            public string calendarId { get; set; }

            public DateTime? timeMin { get; set; }
            public DateTime? timeMax { get; set; }

            [Range(1, 2500, ErrorMessage = "MaxResults phải từ 1 đến 2500")]
            public int maxResults { get; set; } = 250;

            public string orderBy { get; set; } = "startTime";

            public bool showDeleted { get; set; } = false;
            public bool singleEvents { get; set; } = true;
        }
        
    }    
}
