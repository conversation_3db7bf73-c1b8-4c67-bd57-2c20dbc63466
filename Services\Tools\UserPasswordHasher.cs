﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.UserDTOs;
using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace Services.Tools
{
    public class UserPasswordHasher : IPasswordHasher<UserDTO>
    {
        public string HashPassword(UserDTO user, string password)
        {
            using (var sha256 = SHA256.Create())
            {
                var bytes = Encoding.UTF8.GetBytes(password);
                var hashedPassword = sha256.ComputeHash(bytes);
                return Convert.ToBase64String(hashedPassword);
            }
        }

        public PasswordVerificationResult VerifyHashedPassword(UserDTO user, string hashedPassword, string providedPassword)
        {
            var providedHashed = HashPassword(user, providedPassword);
            if (providedHashed == hashedPassword)
                return PasswordVerificationResult.Success;
            return PasswordVerificationResult.Failed;
        }
    }
}
