﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Repositories.Implements
{
    public class PriceDetailRepository : GenericRepository<PriceDetail>, IPriceDetailRepository
    {
        private readonly RevoLandDbContext _context;

        public PriceDetailRepository(RevoLandDbContext context) : base(context, "Price_details")
        {
            _context = context;
        }
    }
}
