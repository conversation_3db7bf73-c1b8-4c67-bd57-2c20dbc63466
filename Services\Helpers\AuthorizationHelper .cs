﻿using BusinessObjects.Models;
using Repositories.Interfaces;

namespace Services.Helpers
{
    public class AuthorizationHelper : IAuthorizationHelper
    {
        private readonly IUnitOfWork _unitOfWork;

        public AuthorizationHelper(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<bool> CanUserModifyDealAsync(string dealId, string userId, string userRole)
        {
            var deal = await _unitOfWork.Deals.GetByIdAsync(dealId);
            if (deal == null) return false;

            return deal.salesRepId.ToString() == userId || userRole == UserRole.Admin.ToString();
        }

        public async Task<Deal> GetDealWithAuthorizationAsync(string dealId, string userId, string userRole)
        {
            var deal = await _unitOfWork.Deals.GetByIdAsync(dealId)
                ?? throw new InvalidDataException($"Deal with ID '{dealId}' does not exist.");

            if (deal.salesRepId.ToString() != userId && userRole != UserRole.Admin.ToString())
            {
                throw new UnauthorizedAccessException("You do not have permission to modify this deal.");
            }

            return deal;
        }
    }
}
