﻿using BusinessObjects.Models;
using BusinessObjects.Validations;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.DTOs.CompanyDTOs
{
	public class CompanyDTO
	{
		public string id { get; set; }
		public string name { get; set; }
		public string? email { get; set; }
		public string? phone { get; set; }
		public string password { get; set; }
		public string? logoUrl { get; set; }
		public string? address { get; set; }
		[BsonRepresentation(BsonType.String)]
		public Industry? industry { get; set; }
		public string? taxId { get; set; }
		public string? licenseNumber { get; set; }
		public string? website { get; set; }
		public string ownerId { get; set; }
	}
}
