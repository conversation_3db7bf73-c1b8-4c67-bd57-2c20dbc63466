﻿using BusinessObjects.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Services.Interface
{
    public interface ILocationService
    {
        Task<IEnumerable<Location>> GetAllLocationsAsync();
        Task<Location> GetLocationByIdAsync(string id);
        Task<Location> AddLocationAsync(Location location);
        Task<Location> UpdateLocationAsync(string id, Location location);
        Task<bool> DeleteLocationAsync(string id);
    }
}
