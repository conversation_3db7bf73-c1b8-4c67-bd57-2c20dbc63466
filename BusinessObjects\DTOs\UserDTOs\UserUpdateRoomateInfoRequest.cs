﻿using BusinessObjects.Models;
using System.ComponentModel.DataAnnotations;

namespace BusinessObjects.DTOs.UserDTOs
{
	public class UserUpdateRoomateInfoRequest
	{
		public string? introduction { get; set; }

		[Required(ErrorMessage = "Occupation is required")]
		public OccupationType occupation { get; set; }

		[Range(0, double.MaxValue, ErrorMessage = "Budget must be a positive number")]
		public decimal budgetMax { get; set; }

		[Required(ErrorMessage = "Pet ownership information is required")]
		public bool hasPets { get; set; }

		[Required(ErrorMessage = "Smoking habit information is required")]
		public bool isSmoking { get; set; }

		[Required(ErrorMessage = "Sleeping habit is required")]
		public SleepHabit sleepingHabit { get; set; }

		[Required(ErrorMessage = "Lifestyles are required")]
		[MinLength(1, ErrorMessage = "At least 1 lifestyle must be selected")]
		[MaxLength(10, ErrorMessage = "At most 10 lifestyles must be selected")]
		public List<Lifestyle> lifestyles { get; set; }
		[Required(ErrorMessage = "Interests are required")]
		[MinLength(1, ErrorMessage = "At least 1 lifestyle must be selected")]
		[MaxLength(10, ErrorMessage = "At most 10 interests must be selected")]
		public List<InterestType>? interests { get; set; }

		[Required(ErrorMessage = "Role matching is required")]
		public RoleMatching roleMatching { get; set; }
		public Location? currentLocation { get; set; }
		public Location? preferredLocation { get; set; }
		public List<string>? locationPictures { get; set; }
		public List<string>? selfPictures { get; set; }
	}
}
