﻿using BusinessObjects.DTOs;
using BusinessObjects.QueryObject;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Services.Interfaces;

namespace Controllers.Controllers
{
    [Route("api/conversations")]
    [ApiController]
    //[Authorize]
    public class ConversationController : ControllerBase
    {
        private readonly IConversationService _conversationService;

        public ConversationController(IConversationService conversationService)
        {
            _conversationService = conversationService;
        }

        private ApiResponse CreateResponse(int code, bool status, string message, object data = null)
        {
            return new ApiResponse { code = code, status = status, message = message, data = data };
        }

        [HttpGet]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> GetAllConversations([FromQuery] QueryConversation query)
        {
            try
            {
                var conversations = await _conversationService.GetAllConversationsAsync(query);
                return Ok(CreateResponse(200, true, "Thành công", conversations));
            }
            catch (Exception ex)
            {
                return Ok(CreateResponse(500, false, ex.Message));
            }
        }

        [HttpGet("user")]
        [Authorize(Roles = "User")]
        public async Task<IActionResult> GetUserConversations()
        {
            try
            {
                var conversations = await _conversationService.GetConversationByUserIdAsync();
                return Ok(CreateResponse(200, true, "Thành công", conversations));
            }
            catch (Exception ex)
            {
                return Ok(CreateResponse(500, false, ex.Message));
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetConversationById(string id)
        {
            try
            {
                var data = await _conversationService.GetConversationByIdAsync(id);
                return Ok(CreateResponse(200, true, "Thành công", data));
            }
            catch (Exception ex)
            {
                return Ok(CreateResponse(500, false, ex.Message));
            }
        }

        [HttpGet("seller")]
        [Authorize(Roles = "Saler")]
        public async Task<IActionResult> GetConversationsByCurrentSeller([FromQuery] QueryConversation query)
        {
            try
            {
                var currentSellerId = User.Claims.FirstOrDefault(c => c.Type == "UserID")?.Value;
                var result = await _conversationService.GetConversationByCurrentSellerAsync(currentSellerId, query);
                return Ok(CreateResponse(200, true, "Thành công", result));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(500, false, ex.Message));
            }
        }

        [HttpGet("seller/{sellerId}")]
        [Authorize(Roles = "User")]
        public async Task<IActionResult> GetConversationByUserIdAndSellerId(string sellerId)
        {
            try
            {
                var currentUserId = User.Claims.FirstOrDefault(c => c.Type == "UserID")?.Value;
                var result = await _conversationService.GetConversationByUserIdAndSellerIdAsync(currentUserId, sellerId);
                return Ok(CreateResponse(200, true, "Thành công", result));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(400, false, "Thất bại khi lấy conversation", null));
            }
        }
    }
}
