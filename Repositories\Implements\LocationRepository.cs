﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Repositories.Implements
{
    public class LocationRepository : GenericRepository<Location>, ILocationRepository
    {
        private readonly RevoLandDbContext _context;
        public LocationRepository(RevoLandDbContext context) : base(context, "Locations")
        {
            _context = context;
        }
    }
}
