﻿using BusinessObjects.Models;
using BusinessObjects.Validations;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.DTOs.LeadDTOs
{
    public class LeadUpdateRequest
    {
        [ValidName]
        public string? name { get; set; }
        [PhoneVN]
        public string? phone { get; set; }
        [EmailAddress]
        public string? email { get; set; }
        public string? address { get; set; }
    }
}
