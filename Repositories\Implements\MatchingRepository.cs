﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using MongoDB.Bson;
using MongoDB.Driver;
using Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Repositories.Implements
{
    public class MatchingRepository : GenericRepository<Matching>, IMatchingRepository
    {
        private readonly IMongoCollection<Matching> _matchings;
        private readonly IMongoCollection<User> _users;
        private readonly IMongoCollection<RoommatePreference> _roommatePreferences;
        private readonly IMongoCollection<Swipe> _swipes;

        public MatchingRepository(RevoLandDbContext context) : base(context, "Matchings")
        {
            _matchings = context.Matchings;
            _users = context.Users;
            _roommatePreferences = context.RoommatePreferences;
            _swipes = context.Swipes;
        }

        public async Task<bool> DeactivateMatchAsync(string matchId, string userId)
        {
            // Filter to find the matching and validate that the user is part of it
            var filter = Builders<Matching>.Filter.And(
                Builders<Matching>.Filter.Eq(m => m.id, matchId),
                Builders<Matching>.Filter.Or(
                    Builders<Matching>.Filter.Eq(m => m.userAId, userId),
                    Builders<Matching>.Filter.Eq(m => m.userBId, userId)
                )
            );

            // Update the matching to inactive state
            var update = Builders<Matching>.Update.Set(m => m.isActive, false);

            var result = await _matchings.UpdateOneAsync(filter, update);
            return result.ModifiedCount > 0;
        }

        public async Task<List<Matching>> GetMatchesForUserAsync(string userId)
        {
            var filter = Builders<Matching>.Filter.And(
                Builders<Matching>.Filter.Or(
                    Builders<Matching>.Filter.Eq(m => m.userAId, userId),
                    Builders<Matching>.Filter.Eq(m => m.userBId, userId)
                ),
                Builders<Matching>.Filter.Eq(m => m.isActive, true)
            );

            return await _matchings.Find(filter).ToListAsync();
        }

        public async Task<List<User>> GetRoommateSuggestionsAsync(string userId, int limit = 20, int skip = 0)
        {
            // Get the current user and their preferences
            var currentUser = await _users.Find(u => u.id == userId).FirstOrDefaultAsync();
            if (currentUser == null)
            {
                throw new Exception("User not found");
            }

            var currentUserPreference = await _roommatePreferences.Find(p => p.userId == userId).FirstOrDefaultAsync();
               

            // Get users who have already been swiped or matched to exclude them
            var swipedUserIds = await _swipes
                .Find(s => s.fromUserId == userId)
                .Project(s => s.toUserId)
                .ToListAsync();

            var matches = await _matchings.Find(
                m => (m.userAId == userId || m.userBId == userId) && m.isActive
            ).ToListAsync();
            
            var matchedUserIds = matches
                .Select(m => m.userAId == userId ? m.userBId : m.userAId)
                .ToList();

            // Combine all users to exclude
            var excludeUserIds = new HashSet<string>(swipedUserIds.Concat(matchedUserIds));
            excludeUserIds.Add(userId); // Exclude self

            // Initial filter for query
            var statusFilter = Builders<User>.Filter.Where(u => 
                u.status != Status.DoNotDisturb && u.status != Status.Invisible);

            var filter = Builders<User>.Filter.And(
                Builders<User>.Filter.Eq(u => u.isRoommateActive, true),
                statusFilter,
                Builders<User>.Filter.Not(Builders<User>.Filter.In(u => u.id, excludeUserIds))
            );

            // Apply additional filters based on preferences
            if (currentUserPreference != null)
            {
                // Gender preference
                if (currentUserPreference.preferredGender != Gender.Other)
                {
                    filter = Builders<User>.Filter.And(
                        filter,
                        Builders<User>.Filter.Eq(u => u.gender, currentUserPreference.preferredGender)
                    );
                }

                // Age filters
                if (currentUserPreference.ageMin.HasValue)
                {
                    var maxBirthDate = DateTime.Today.AddYears(-currentUserPreference.ageMin.Value);
                    filter = Builders<User>.Filter.And(
                        filter,
                        Builders<User>.Filter.Lte(u => u.birthdate, maxBirthDate)
                    );
                }

                if (currentUserPreference.ageMax.HasValue)
                {
                    var minBirthDate = DateTime.Today.AddYears(-currentUserPreference.ageMax.Value - 1).AddDays(1);
                    filter = Builders<User>.Filter.And(
                        filter,
                        Builders<User>.Filter.Gte(u => u.birthdate, minBirthDate)
                    );
                }

                // Smoking preference
                if (currentUserPreference.allowSmoking.HasValue && !currentUserPreference.allowSmoking.Value)
                {
                    filter = Builders<User>.Filter.And(
                        filter,
                        Builders<User>.Filter.Eq(u => u.isSmoking, false)
                    );
                }

                // Pet preference
                if (currentUserPreference.allowPets.HasValue && !currentUserPreference.allowPets.Value)
                {
                    filter = Builders<User>.Filter.And(
                        filter,
                        Builders<User>.Filter.Eq(u => u.hasPets, false)
                    );
                }
            }

            // Role matching
            if (currentUser.roleMatching.HasValue)
            {
                var desiredRole = currentUser.roleMatching == RoleMatching.ShareRoom ?
                    RoleMatching.FindRoom : RoleMatching.ShareRoom;

                filter = Builders<User>.Filter.And(
                    filter,
                    Builders<User>.Filter.Eq(u => u.roleMatching, desiredRole)
                );
            }
            // Get matching users from databasesss

            var users = await _users.Find(filter).ToListAsync();

            // Calculate compatibility scores
            var scoredUsers = users.Select(user => 
            {
                int score = 10; // Base score
                    
                // Verified user bonus
                if (user.isVerified)
                    score += 5;

                // Has profile pictures bonus
                if ((user.selfPictures != null && user.selfPictures.Count > 0) || 
                    (user.locationPictures != null && user.locationPictures.Count > 0))
                    score += 5;

                // Has introduction bonus
                if (!string.IsNullOrEmpty(user.introduction))
                    score += 3;

                // Recently updated bonus
                if (user.updatedAtRoommateInfo.HasValue && 
                    user.updatedAtRoommateInfo.Value >= DateTime.UtcNow.AddDays(-7))
                    score += 3;

                // Add lifestyle matching score if possible
                if (currentUser.lifestyles != null && user.lifestyles != null &&
                    currentUser.lifestyles.Count > 0 && user.lifestyles.Count > 0)
                {
                    int matchingLifestyles = currentUser.lifestyles.Intersect(user.lifestyles).Count();
                    score += matchingLifestyles * 2; // 2 points per matching lifestyle
                }if (currentUser.interests != null && user.interests != null &&
                    currentUser.interests.Count > 0 && user.interests.Count > 0)
                {
                    int matchinginterests = currentUser.interests.Intersect(user.interests).Count();
                    score += matchinginterests * 2; // 2 points per matching lifestyle
                }

                return new { User = user, Score = score };
            })
            .OrderByDescending(item => item.Score)
            .ThenByDescending(item => item.User.updatedAtRoommateInfo)
            .Skip(skip)
            .Take(limit)
            .Select(item => item.User)
            .ToList();

            return scoredUsers;
        }
    }
}
