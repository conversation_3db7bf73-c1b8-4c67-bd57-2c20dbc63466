﻿using BusinessObjects.DTOs.TeamDTOs;
using BusinessObjects.Models;
using Repositories.Interfaces;
using Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Services.Implements
{
	public class TeamService : ITeamService
	{
		private readonly ITeamRepository _teamRepository;
		private readonly IUnitOfWork _unitOfWork;

		public TeamService(ITeamRepository teamRepository, IUnitOfWork unitOfWork)
		{
			_teamRepository = teamRepository;
			_unitOfWork = unitOfWork;
		}

		public async Task<TeamCreateResponse> CreateTeamAsync(TeamCreateRequest request, string companyId)
		{
			var existingTeam = await _teamRepository.GetTeamByNameAsync(request.name);
			if (existingTeam != null)
			{
				throw new Exception("Team with this name already exists.");
			}
			var team = new Team
			{
				name = request.name,
				description = request.description,
				companyId = companyId
			};
			team = await _unitOfWork.Teams.AddAsync(team);
			var teamResponse = new TeamCreateResponse
			{
				name = team.name,
				description = team.description,
				companyId = team.companyId,
				createdAt = team.createdAt,
				updatedAt = team.updatedAt
			};
			return teamResponse;
		}

		public async Task DeleteTeamAsync(string teamId)
		{
			var existingTeam = await _unitOfWork.Teams.GetByIdAsync(teamId);
			if (existingTeam == null)
			{
				throw new Exception("Team not found.");
			}
			await _unitOfWork.Teams.DeleteAsync(teamId);

			await _teamRepository.DeleteTeamAgentLinkByTeamIdAsync(teamId);
		}

		public async Task<IEnumerable<TeamResponse>> GetAll()
		{
			var teams = await _unitOfWork.Teams.GetAllAsync();
			var teamResponses = teams.Select(team => new TeamResponse
			{
				id = team.id,
				name = team.name,
				description = team.description,
				companyId = team.companyId,
				createdAt = team.createdAt,
				updatedAt = team.updatedAt
			});
			return teamResponses;
		}

		public async Task<TeamResponse> GetTeamByIdAsync(string teamId)
		{
			var teams = await _unitOfWork.Teams.GetByIdAsync(teamId);
			var teamResponse = new TeamResponse
			{
				id = teams.id,
				name = teams.name,
				description = teams.description,
				companyId = teams.companyId,
				createdAt = teams.createdAt,
				updatedAt = teams.updatedAt
			};
			return teamResponse;
		}

		public async Task<TeamUpdateResponse> UpdateTeamAsync(TeamUpdateRequest request, string teamId)
		{
			var existingTeam = await _unitOfWork.Teams.GetByIdAsync(teamId);
			if (existingTeam == null)
			{
				throw new Exception("Team not found.");
			}
			existingTeam.name = request.name ?? existingTeam.name;
			existingTeam.description = request.description ?? existingTeam.description;
			existingTeam.updatedAt = DateTime.UtcNow.AddHours(7);

			var team = await _unitOfWork.Teams.UpdateAsync(teamId, existingTeam);
			var teamResponse = new TeamUpdateResponse
			{
				name = team.name,
				description = team.description,
				companyId = team.companyId,
				createdAt = team.createdAt,
				updatedAt = team.updatedAt
			};
			return teamResponse;
		}
	}
}
