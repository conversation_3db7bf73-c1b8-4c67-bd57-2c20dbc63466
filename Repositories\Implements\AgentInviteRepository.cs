﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using MongoDB.Driver;
using Repositories.Interfaces;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Repositories.Implements
{
	public class AgentInviteRepository : GenericRepository<AgentInvite>, IAgentInviteRepository
	{
		private readonly IMongoCollection<AgentInvite> _mongoCollection;
		public AgentInviteRepository(RevoLandDbContext context) : base(context, "AgentInvites")
		{
			_mongoCollection = context.GetCollection<AgentInvite>("AgentInvites");
		}
		public async Task<AgentInvite> GetByTokenAsync(string token)
		{
			return await _mongoCollection.Find(x => x.token == token).FirstOrDefaultAsync();
		}

		public async Task<AgentInvite> GetByEmailAndCompanyAsync(string email, string companyId)
		{
			var filter = Builders<AgentInvite>.Filter.And(
				Builders<AgentInvite>.Filter.Eq(x => x.email, email),
				Builders<AgentInvite>.Filter.Eq(x => x.companyId, companyId)
			);
			return await _mongoCollection.Find(filter).FirstOrDefaultAsync();
		}

		public async Task CreateNewAsync(string email, string companyId, string token, AgentCompanyRole role, int validHours)
		{
			var invite = new AgentInvite
			{
				email = email,
				companyId = companyId,
				token = token,
				createdAt = DateTime.UtcNow,
				expiredAt = DateTime.UtcNow.AddHours(validHours),
				isUsed = false,
				role = role
			};
			await _mongoCollection.InsertOneAsync(invite);
		}

		public async Task UpdateInviteTokenAsync(string email, string companyId, string token, int validHours)
		{
			var filter = Builders<AgentInvite>.Filter.And(
				Builders<AgentInvite>.Filter.Eq(x => x.email, email),
				Builders<AgentInvite>.Filter.Eq(x => x.companyId, companyId)
			);

			var update = Builders<AgentInvite>.Update
				.Set(x => x.token, token)
				.Set(x => x.createdAt, DateTime.UtcNow)
				.Set(x => x.expiredAt, DateTime.UtcNow.AddHours(validHours))
				.Set(x => x.isUsed, false);

			await _mongoCollection.UpdateOneAsync(filter, update);
		}

		public async Task MarkUsedAsync(string id)
		{
			var update = Builders<AgentInvite>.Update.Set(x => x.isUsed, true);
			await _mongoCollection.UpdateOneAsync(x => x.id == id, update);
		}
	}
}
