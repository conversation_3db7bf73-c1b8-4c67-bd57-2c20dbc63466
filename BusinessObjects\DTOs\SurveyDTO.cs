using System.ComponentModel.DataAnnotations;
using BusinessObjects.Models;

namespace BusinessObjects.DTOs
{
    public class SurveyCreateRequest
    {
        [Required]
        public string fullName { get; set; }

        [Required]
        [EmailAddress]
        public string email { get; set; }

        [Required]
        [Phone]
        public string phoneNumber { get; set; }

        public string propertyTypeInterest { get; set; }

        public BudgetCategory? budgetCategory { get; set; }

        public string locationPreference { get; set; }

        public string additionalComments { get; set; }
    }

    public class SurveyResponse
    {
        public string id { get; set; }
        public string fullName { get; set; }
        public string email { get; set; }
        public string phoneNumber { get; set; }
        public string propertyTypeInterest { get; set; }
        public BudgetCategory? budgetCategory { get; set; }
        public string locationPreference { get; set; }
        public string additionalComments { get; set; }
        public DateTime createdAt { get; set; }
        public bool isConvertedToLead { get; set; }
    }
} 