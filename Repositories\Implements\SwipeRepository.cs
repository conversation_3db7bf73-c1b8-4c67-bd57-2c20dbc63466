﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using MongoDB.Driver;
using Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Repositories.Implements
{
    public class SwipeRepository : GenericRepository<Swipe>, ISwipeRepository
    {
        private readonly IMongoCollection<Swipe> _swipes;
        public SwipeRepository(RevoLandDbContext context) : base(context, "Swipes")
        {
            _swipes = context.GetCollection<Swipe>("Swipes"); 
        }

        public async Task<Swipe?> GetSwipeAsync(string fromUserId, string toUserId)
        {
            var filter = Builders<Swipe>.Filter.And(
                Builders<Swipe>.Filter.Eq(s => s.fromUserId, fromUserId),
                Builders<Swipe>.Filter.Eq(s => s.toUserId, toUserId)
            );
            return await _swipes.Find(filter).FirstOrDefaultAsync();
        }
    }
}
