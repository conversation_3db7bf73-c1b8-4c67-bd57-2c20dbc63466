﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.Models
{
    public class Agent
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        [BsonElement("agentId")]
        public string id { get; set; } = ObjectId.GenerateNewId().ToString();

        [Required]
        public string? fullName { get; set; }

        [Required]
        [EmailAddress]
        public string email { get; set; }

        [Required]
        [Phone]
        public string? phone { get; set; }

        public string? avatar { get; set; }

        [BsonRepresentation(BsonType.String)]
        public Gender? gender { get; set; }

        public string? bio { get; set; }
        public DateTime joinedAt { get; set; } = DateTime.UtcNow.AddHours(7);
	}

}
