﻿using BusinessObjects.Models;
using static BusinessObjects.DTOs.CalendarDTOs.GoogleCalendar;
using static BusinessObjects.DTOs.CalendarDTOs.GoogleCalendarIntegration;

namespace Services.Interfaces
{
    public interface ICalendarIntegrationService
    {
        Task<string?> CreateCalendarEventForAppointmentAsync(
                                                             Appointment appointment,
                                                             string? targetUserId = null);
        Task<bool> UpdateCalendarEventForAppointmentAsync(
                                                            Appointment appointment,
                                                            string userId,
                                                            string eventId);
        Task<bool> DeleteCalendarEventForAppointmentAsync(
                                                            Appointment appointment,
                                                            string userId,
                                                            string eventId);
        Task<(bool userDeleted, bool salerDeleted)> DeleteAllCalendarEventsForAppointmentAsync(
                                                            Appointment appointment);
        Task<CalendarEventSyncResult> SyncAppointmentToCalendarsAsync(Appointment appointment);
        
        /// <summary>
        /// Get all calendars for a user using their access token
        /// </summary>
        /// <param name="userId">User ID to get credentials for</param>
        /// <returns>List of user's calendars</returns>
        Task<List<CalendarInfo>> GetUserCalendarsAsync(string userId);
        
        /// <summary>
        /// Get events from user's calendar within specified time range
        /// </summary>
        /// <param name="userId">User ID to get credentials for</param>
        /// <param name="request">Event query parameters</param>
        /// <returns>List of calendar events</returns>
        Task<List<EventInfo>> GetUserEventsAsync(string userId, GetUserEventsRequest request);
        
        /// <summary>
        /// Sync all user appointments to their calendar
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="syncOptions">Sync configuration options</param>
        /// <returns>Sync result summary</returns>
        //Task<CalendarSyncResult> SyncUserAppointmentsAsync(string userId, CalendarSyncOptions? syncOptions = null);
        


    }
}
