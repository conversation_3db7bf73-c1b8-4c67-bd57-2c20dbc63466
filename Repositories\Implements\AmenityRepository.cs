﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Repositories.Implements
{
    public class AmenityRepository : GenericRepository<Amenity>, IAmenityRepository
    {
        private readonly RevoLandDbContext _context;

        public AmenityRepository(RevoLandDbContext context) : base(context, "Amenities")
        {
            _context = context;
        }
    }
}
