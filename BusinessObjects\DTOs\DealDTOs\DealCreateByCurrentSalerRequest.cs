﻿using BusinessObjects.Validations;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System.ComponentModel.DataAnnotations;

namespace BusinessObjects.DTOs.DealDTOs
{
    public class DealCreateByCurrentSalerRequest
    {
        [Required(ErrorMessage = "leadId is required.")]
        [BsonRepresentation(BsonType.ObjectId)]
        [ValidObjectId(ErrorMessage = "leadId must be a valid ObjectId")]
        public required string leadId { get; set; }
        public string? title { get; set; }

        public string? description { get; set; }

        /// <summary>
        /// Filter by priority , e.g. "Low", "Medium", "High"
        /// </summary>
        public string? priority { get; set; }
    }
}
