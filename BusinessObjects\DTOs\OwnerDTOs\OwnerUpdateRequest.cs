using BusinessObjects.Models;
using Microsoft.AspNetCore.Http;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace BusinessObjects.DTOs.OwnerDTOs
{
    public class OwnerUpdateRequest
    {
        public string? name { get; set; }
        [Phone]
        public string? phone { get; set; }
        [EmailAddress]
        public string? email { get; set; }
        public string? address { get; set; }
        public Gender? gender { get; set; }
        public string? nationality { get; set; }
        public IFormFile? idVerificationFile { get; set; }
    }
}