﻿using BusinessObjects.DTOs.AgentDTOs;
using BusinessObjects.Models;
using Repositories.Interfaces;
using Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Services.Implements
{
	public class AgentService : IAgentService
	{
		private readonly IAgentRepository _agentRepository;
		private readonly IUnitOfWork _unitOfWork;

		public AgentService(IAgentRepository agentRepository, IUnitOfWork unitOfWork)
		{
			_agentRepository = agentRepository;
			_unitOfWork = unitOfWork;
		}
		public async Task AssignAgentToTeam(AddAgentToTeamRequest request)
		{
			var team = _unitOfWork.Teams.GetByIdAsync(request.teamId);
			var agent = _unitOfWork.Agents.GetByIdAsync(request.agentId);
			if(team == null)
			{
				throw new Exception("Team not found.");
			}
			if(agent == null)
			{
				throw new Exception("Agent not found.");
			}
			await _agentRepository.AssignAgentToTeam(request);
		}
		public async Task DeleteAgentFromTeam(string teamId, string agentId)
		{
			var team = _unitOfWork.Teams.GetByIdAsync(teamId);
			var agent = _unitOfWork.Agents.GetByIdAsync(agentId);
			if (team == null)
			{
				throw new Exception("Team not found.");
			}
			if (agent == null)
			{
				throw new Exception("Agent not found.");
			}
			await _agentRepository.DeleteAgentFromTeam(teamId, agentId);
		}
		public async Task<AgentResponse> CreateAgentAsync(CreateAgentRequest request)
		{
			var agent = new Agent
			{
				fullName = request.fullName,
				email = request.email,
				phone = request.phone,
				avatar = request.avatar,
				bio = request.bio,
				gender = request.gender,
			};
			agent = await _unitOfWork.Agents.AddAsync(agent);
			var response = new AgentResponse
			{
				id = agent.id,
				fullName = agent.fullName,
				email = agent.email,
				phone = agent.phone,
				avatar = agent.avatar,
				bio = agent.bio,
				gender = agent.gender,
				joinedAt = agent.joinedAt
			};
			return response;
		}
		public async Task<AgentResponse> GetAgentByIdAsync(string id)
		{
			if (string.IsNullOrEmpty(id))
			{
				throw new Exception("Agent ID cannot be null or empty.");
			}
			var agent = await _unitOfWork.Agents.GetByIdAsync(id);
			var response = new AgentResponse();
			response.id = agent.id;
			response.fullName = agent.fullName;
			response.email = agent.email;
			response.phone = agent.phone;
			response.avatar = agent.avatar;
			response.gender = agent.gender;
			response.bio = agent.bio;
			response.joinedAt = agent.joinedAt;
			return response;
		}
		public async Task<IEnumerable<AgentResponse>> GetAllAgentsAsync()
		{
			var agents = await _unitOfWork.Agents.GetAllAsync();
			var agentResponses = agents.Select(agent => new AgentResponse
			{
				id = agent.id,
				fullName = agent.fullName,
				email = agent.email,
				phone = agent.phone,
				avatar = agent.avatar,
				gender = agent.gender,
				bio = agent.bio,
				joinedAt = agent.joinedAt
			});
			return agentResponses;
		}
		public async Task<List<AgentResponse>> GetAllAgentsByCompanyIdAsync(string companyId)
		{
			var agents = await _agentRepository.GetAllAgentsByCompanyIdAsync(companyId);
			var agentResponses = agents.Select(agent => new AgentResponse
			{
				id = agent.id,
				fullName = agent.fullName,
				email = agent.email,
				phone = agent.phone,
				avatar = agent.avatar,
				gender = agent.gender,
				bio = agent.bio,
				joinedAt = agent.joinedAt
			}).ToList();
			return agentResponses;
		}
	}
}
