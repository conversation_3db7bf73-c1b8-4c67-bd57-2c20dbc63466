﻿using BusinessObjects.DTOs.CompanyDTOs;
using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace Services.Tools
{
	public class CompanyPasswordHasher : IPasswordHasher<CompanyDTO>
	{
		public string HashPassword(CompanyDTO company, string password)
		{
			using (var sha256 = SHA256.Create())
			{
				var bytes = Encoding.UTF8.GetBytes(password);
				var hashedPassword = sha256.ComputeHash(bytes);
				return Convert.ToBase64String(hashedPassword);
			}
		}
		public PasswordVerificationResult VerifyHashedPassword(CompanyDTO company, string hashedPassword, string providedPassword)
		{
			var providedHashed = HashPassword(company, providedPassword);
			if (providedHashed == hashedPassword)
				return PasswordVerificationResult.Success;
			return PasswordVerificationResult.Failed;
		}
	}
}
