using BusinessObjects.Models;

namespace BusinessObjects.DTOs.CollectionDTOs
{
    public class PropertyResponseDTO
    {
        public string id { get; set; }
        public string? title { get; set; }
        public string? salerId { get; set; }
        public string? name { get; set; }
        public string? slug { get; set; }
        public string? description { get; set; }
        public PropertyTransactionType? transactionType { get; set; }
        public PropertyType? type { get; set; }
        public PropertyStatus? status { get; set; }
        public string? adminNote { get; set; }
        public string? code { get; set; }
        public string? ownerId { get; set; }
        public Location? location { get; set; }
        public PropertyDetail? propertyDetails { get; set; }
        public PriceDetail? priceDetails { get; set; }
        public Amenity? amenities { get; set; }
        public List<string>? images { get; set; }
        public List<string>? floorPlans { get; set; }
        public PropertyVideo? video { get; set; }
        public int? yearBuilt { get; set; }
        public List<string>? legalDocuments { get; set; }
        public List<TransactionHistory>? transactionHistory { get; set; }
        public DateTime createdAt { get; set; }
        public DateTime updatedAt { get; set; }
        public string? contactName { get; set; }
        public string? contactPhone { get; set; }
        public string? contactEmail { get; set; }
        public bool isFeatured { get; set; }
        public bool isVerified { get; set; }
        public bool isDraft { get; set; }
    }
} 