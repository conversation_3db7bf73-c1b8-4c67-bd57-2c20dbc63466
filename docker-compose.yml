version: '3.8'
services:
  quinland-service-api:
    container_name: quinland-api
    networks:
      - quinland
    build:
      context: .
      dockerfile: dockerfile
    ports:
      - "8080:8080"

  quinland-service-dev:
    container_name: quinland-dev
    networks:
      - quinland
    build:
      context: .
      dockerfile: dockerfile
    ports:
      - "8081:8080"

  redis-service:
    image: "redis:alpine"
    networks:
      - quinland   
    ports:
      - "6379:6379"
    volumes:
      - ./redis-data:/var/lib/redis
      - ./redis.conf:/usr/local/etc/redis/redis.conf

networks:
  quinland:
    driver: bridge