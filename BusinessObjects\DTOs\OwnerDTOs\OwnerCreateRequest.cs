﻿using BusinessObjects.Models;
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Http;
using BusinessObjects.Validations;

namespace BusinessObjects.DTOs.OwnerDTOs
{
    public class OwnerCreatedRequest
    {
       

        [BsonElement("name")]
        [Required]
        public string name { get; set; }

        [BsonElement("phone")]
        [Phone]
        [Required]
        public string phone { get; set; }

        [BsonElement("email")]
        [EmailAddress]
        [Required]
        public string email { get; set; }

        [BsonElement("address")]
        [Required]
        public string address { get; set; }

        [BsonElement("gender")]
        [Required]
        public Gender? gender { get; set; }

        [BsonElement("nationality")]
        [Required]
        public string nationality { get; set; }
        [Required]
        public IFormFile idVerificationFile { get; set; }

        [ValidObjectId]
        public string? userId { get; set; }

    }
}
