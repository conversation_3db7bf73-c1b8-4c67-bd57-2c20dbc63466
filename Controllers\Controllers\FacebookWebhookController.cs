﻿using Microsoft.AspNetCore.Mvc;
using Services.Interfaces;
using Services.Tools;
using System.Text;
using System.Text.Json;

namespace Controllers.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class FacebookWebhookController : ControllerBase
    {
        private readonly IChatMessageService _messageService;
        private readonly IConfiguration _configuration;
        public FacebookWebhookController(IChatMessageService messageService, IConfiguration configuration)
        {
            _messageService = messageService;
            _configuration = configuration;
        }
        [HttpGet]
        public IActionResult VerifyWebhook(
                                            [FromQuery(Name = "hub.mode")] string hubMode,
                                            [FromQuery(Name = "hub.challenge")] string hubChallenge,
                                            [FromQuery(Name = "hub.verify_token")] string hubVerifyToken)
        {
            // sanity-check log
            Console.WriteLine($"VerifyWebhook → mode={hubMode}; token={hubVerifyToken}; challenge={hubChallenge}");

            if (hubMode == "subscribe" && hubVerifyToken == _configuration["VerifyToken"])
            {
                // must return the plain challenge string, not JSON
                return Ok(hubChallenge);
            }

            return BadRequest("Webhook verification failed.");
        }


        [HttpPost]
        public async Task<IActionResult> ReceiveWebhook()
        {
            // Cho phép đọc lại body nhiều lần
            Request.EnableBuffering();

            // 1) Đọc raw JSON
            using var reader = new StreamReader(Request.Body, Encoding.UTF8, leaveOpen: true);
            var raw = await reader.ReadToEndAsync();
            Console.WriteLine("Raw webhook payload:\n" + raw);

            // 2) Reset position để không vướng lỗi nếu ai đó bind tiếp
            Request.Body.Position = 0;

            // 3) Deserialize thủ công
            MessengerWebhookPayloadPlain plain;
            try
            {
                plain = JsonSerializer.Deserialize<MessengerWebhookPayloadPlain>(
                    raw,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true,
                        // (tuỳ chọn) IgnoreUnknownProperties = true  // mặc định là true
                    }
                ) ?? throw new Exception("Deserialized payload is null");
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine("Webhook deserialization error: " + ex.Message);
                return BadRequest("Invalid payload format");
            }

            // 4) Map xuống flat DTO rồi xử lý attachments & text
            var flat = plain.ToFlat();

            await _messageService.HandleIncomingMessengerMessageAsync(flat);
            return Ok();
        }

    }
}
