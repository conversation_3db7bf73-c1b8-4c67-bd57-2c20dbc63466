﻿using System.Text.Json.Serialization;

public class MessengerWebhookPayloadPlain
{
    [Json<PERSON>ropertyName("object")]
    public string type { get; set; }

    [JsonPropertyName("entry")]
    public List<MessengerEntry> entry { get; set; }
}

public class MessengerEntry
{
    [JsonPropertyName("id")]
    public string id { get; set; }

    [Json<PERSON>ropertyName("time")]
    public long time { get; set; }

    [JsonPropertyName("messaging")]
    public List<MessengerMessaging> messaging { get; set; }
}

public class MessengerMessaging
{
    [JsonPropertyName("sender")]
    public MessengerSender sender { get; set; }
    [JsonPropertyName("recipient")]
    public MessengerRecipient recipient { get; set; }
    [JsonPropertyName("timestamp")]
    public long timestamp { get; set; }
    [JsonPropertyName("message")]
    public MessengerMessage message { get; set; }


}

public class MessengerSender { [JsonPropertyName("id")] public string id { get; set; } }
public class MessengerRecipient { [Json<PERSON>ropertyName("id")] public string id { get; set; } }
public class MessengerMessage
{
    [Json<PERSON>ropertyName("mid")] public string mid { get; set; }
    [Json<PERSON>ropertyName("text")] public string text { get; set; }

    [JsonPropertyName("is_echo")]
    public bool? isEcho { get; set; }

    [JsonPropertyName("attachments")]
    public List<MessengerAttachment>? attachments { get; set; }
}
public class MessengerAttachmentPayload
{
    [JsonPropertyName("url")]      // Đường dẫn tạm thời
    public string url { get; set; }

    // Có thể có "attachment_id" nếu file được upload trước
    //[JsonPropertyName("attachment_id")]
    //public string? attachmentId { get; set; }
}
public class MessengerAttachment
{
    [JsonPropertyName("type")]   // "image", "file", "audio", "video", "fallback"
    public string type { get; set; }

    [JsonPropertyName("payload")]
    public MessengerAttachmentPayload payload { get; set; }
}

