﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.ReminderDTO;
using Microsoft.AspNetCore.Mvc;
using Repositories.Interfaces;
using Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using BusinessObjects.Models;
using System.Collections.Generic;
using System.Linq;

namespace Controllers.Controllers
{
    [ApiController]
    [Route("api/notifications")]
    public class NotificationController : ControllerBase
    {
        private readonly INotificationService _notificationService;
        private readonly INotificationRepository _notificationRepository;
        private readonly IRentalContractReminderService _reminderService;
        private readonly ITenantService _tenantService;

        public NotificationController(
            INotificationService notificationService, 
            INotificationRepository notificationRepository,
            IRentalContractReminderService reminderService, 
            ITenantService tenantService)
        {
            _notificationService = notificationService;
            _notificationRepository = notificationRepository;
            _reminderService = reminderService ?? throw new ArgumentNullException(nameof(reminderService));
            _tenantService = tenantService ?? throw new ArgumentNullException(nameof(tenantService));
        }
        
        private ApiResponse CreateResponse(int code, bool status, string message, object data = null)
        {
            return new ApiResponse { code = code, status = status, message = message, data = data };
        }


        [HttpPost("send")]
        public async Task<IActionResult> SendNotification([FromBody] NotificationRequest request)
        {
              await _notificationService.SendNotificationAsync(request.email, request.phoneNumber, request.userId, request.message);
            return Ok(CreateResponse(200, true, "Notification sent.", request.message));
        }

        [HttpGet("user/{userId}")]
        public async Task<IActionResult> GetUserNotifications(string userId)
        {
            try
            {
                var notifications = await _notificationRepository.GetNotificationsByUserIdAsync(userId);
                return Ok(CreateResponse(200, true, "Get Notification Successfully", notifications.Count > 0));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateResponse(500, false, "Get Notification faild. " + ex.Message));
            }
        }
        
        [HttpGet("rental-contract-reminders")]
        [Authorize]
        public async Task<IActionResult> GetUserReminders()
        {
            try
            {
                // Lấy UserId từ claims của token xác thực, kiểm tra các key phổ biến
                var userId = User.FindFirst("id")?.Value ??
                             User.FindFirst("UserID")?.Value ??
                             User.FindFirst("sub")?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(CreateResponse(401, false, "Unauthorized, User not found"));
                }

                // Lấy thông tin user để lấy email
                var userEmail = User.FindFirst("email")?.Value;
                var reminders = new List<RentalContractReminder>();

                // Tìm ID của tenant trong bảng tenant dựa trên email nếu có
                if (!string.IsNullOrEmpty(userEmail))
                {
                    var tenant = await _tenantService.GetTenantByEmailAsync(userEmail);
                    if (tenant != null && !string.IsNullOrEmpty(tenant.id))
                    {
                        // Lấy thông báo dựa trên ID của tenant
                        var tenantReminders = await _reminderService.GetRemindersByUserIdAsync(tenant.id);
                        reminders.AddRange(tenantReminders);
                    }
                }

                // Lấy thông báo dựa trên userId (có thể là saler hoặc user khác)
                var userReminders = await _reminderService.GetRemindersByUserIdAsync(userId);
                reminders.AddRange(userReminders);

                // Loại bỏ trùng lặp nếu có
                reminders = reminders.DistinctBy(r => r.id).ToList();

                return Ok(CreateResponse(200, true, "Get Rental Contract Reminders Successfully", reminders));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateResponse(500, false, $"Error when get rental contract reminders: {ex.Message}"));
            }
        }
    }
}
