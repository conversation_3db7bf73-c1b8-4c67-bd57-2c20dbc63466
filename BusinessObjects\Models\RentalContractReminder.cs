using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;

namespace BusinessObjects.Models
{
    public class RentalContractReminder
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string id { get; set; }
        public string userId { get; set; }
        public string contractId { get; set; }
        public string title { get; set; }
        public string message { get; set; }
        public DateTime expiringDate { get; set; }
    }
} 