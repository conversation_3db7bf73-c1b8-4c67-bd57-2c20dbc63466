﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace BusinessObjects.Models
{
    public class PropertyViewHistory
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string id { get; set; }
        public string userId { get; set; }
        public string propertyId { get; set; }
        public DateTime viewedAt { get; set; } = DateTime.UtcNow;
    }
}
