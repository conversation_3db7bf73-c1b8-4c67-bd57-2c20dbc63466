﻿namespace BusinessObjects.DTOs.GoogleDTOs
{
    public class GoogleUserDTOs
    {
        // ✅ DTOs for Google UserInfo API response
        public class GoogleUserInfo
        {
            public string id { get; set; } = string.Empty;
            public string email { get; set; } = string.Empty;
            public bool verifiedEmail { get; set; }
            public string name { get; set; } = string.Empty;
            public string givenName { get; set; } = string.Empty;
            public string familyName { get; set; } = string.Empty;
            public string picture { get; set; } = string.Empty;
            public string locale { get; set; } = string.Empty;
            public string hd { get; set; } = string.Empty; // Hosted domain

            // ✅ Additional computed properties
            public string FullName => !string.IsNullOrEmpty(name) ? name : $"{givenName} {familyName}".Trim();
            public bool IsGSuiteUser => !string.IsNullOrEmpty(hd);
        }
        // ✅ Domain model for Google User Info
        public class GoogleUserInfoResponse : GoogleUserInfo
        {
        }
    }
}
