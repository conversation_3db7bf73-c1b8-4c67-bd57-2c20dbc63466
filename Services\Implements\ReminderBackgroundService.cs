﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Services.Implements
{
    public class ReminderBackgroundService : BackgroundService
    {
        //private readonly IServiceScopeFactory _serviceScopeFactory;

        //public ReminderBackgroundService(IServiceScopeFactory serviceScopeFactory)
        //{
        //    _serviceScopeFactory = serviceScopeFactory;
        //}

        //protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        //{
        //    while (!stoppingToken.IsCancellationRequested)
        //    {
        //        using (var scope = _serviceScopeFactory.CreateScope())
        //        {
        //            var reminderService = scope.ServiceProvider.GetRequiredService<IReminderService>();
        //            await reminderService.ProcessPendingReminders();
        //        }

        //        await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
        //    }
        //}

        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<ReminderBackgroundService> _logger;

        public ReminderBackgroundService(IServiceProvider serviceProvider, ILogger<ReminderBackgroundService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                _logger.LogInformation("ReminderBackgroundService is running...");

                using (var scope = _serviceProvider.CreateScope())
                {
                    var reminderService = scope.ServiceProvider.GetRequiredService<IReminderService>();

                    //await reminderService.CreateSampleReminderAsync();

                    var reminders = await reminderService.GetRemindersToNotifyAsync();
                    foreach (var reminder in reminders)
                    {
                        await reminderService.SendReminderAsync(reminder);
                    }
                }

                await Task.Delay(TimeSpan.FromHours(1), stoppingToken);
            }
        }
    }
}
