﻿using BusinessObjects.Models;

namespace BusinessObjects.DTOs.UserDTOs
{
	public class UserRoomateInfoResponse
	{
		public string fullName { get; set; }
		public string phoneNumber { get; set; }
		public string? avatar { get; set; }
		public Status? status { get; set; }
		public string? about { get; set; }
		public Gender gender { get; set; }
		public DateTime? birthdate { get; set; }

		// Profile matching
		public bool isRoommateActive { get; set; } = false;
		public string? introduction { get; set; }
		public OccupationType? occupation { get; set; }
		public decimal? budgetMax { get; set; }
		public bool? hasPets { get; set; }
		public bool? isSmoking { get; set; }
		public DateTime? updatedAtRoommateInfo { get; set; }
		public SleepHabit? sleepingHabit { get; set; }
		public List<Lifestyle>? lifestyles { get; set; }
		public List<InterestType>? interests { get; set; }
		public RoleMatching? roleMatching { get; set; }
		public Location? currentLocation { get; set; }
		public Location? preferredLocation { get; set; }
		public List<string>? locationPictures { get; set; }
		public List<string>? selfPictures { get; set; }

	}
}
