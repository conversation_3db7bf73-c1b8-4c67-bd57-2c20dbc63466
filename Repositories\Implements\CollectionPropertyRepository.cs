﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using Microsoft.EntityFrameworkCore;
using MongoDB.Bson;
using MongoDB.Driver;
using Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Repositories.Implements
{
    public class CollectionPropertyRepository : GenericRepository<CollectionProperty>, ICollectionPropertyRepository
    {



        private readonly RevoLandDbContext _context;
        private readonly IMongoCollection<CollectionProperty> _collectionProperties;
        private readonly IMongoCollection<Property> _property;


        public CollectionPropertyRepository(RevoLandDbContext context) : base(context, "CollectionProperty")
        {
            _context = context;
            _collectionProperties = context.GetCollection<CollectionProperty>("CollectionProperty");
            _property = context.GetCollection<Property>("Properties");
        }
        public async Task<CollectionProperty?> FindExistCollectionProperty(string collectionId)
        {
            if (string.IsNullOrWhiteSpace(collectionId))
            {
                throw new ArgumentException("Collection ID cannot be empty.", nameof(collectionId));
            }
            return await _collectionProperties.Find(c => c.id.ToString() == collectionId).FirstOrDefaultAsync();
        }



        public IQueryable<CollectionProperty> GetAll()
        {
            return _collectionProperties.AsQueryable();
        }

        public Task<IEnumerable<CollectionProperty>> GetAllByUserIdAsync(string id)
        {
            if (string.IsNullOrWhiteSpace(id))
            {
                throw new ArgumentException("User ID cannot be null or empty.", nameof(id));
            }
            // Tìm kiếm toàn bộ propert trong collection theo userId
            return _collectionProperties.Find(c => c.userId == id).ToListAsync()
                .ContinueWith(task => (IEnumerable<CollectionProperty>)task.Result);
        }
        public async Task UpdateCollectionImagesAsync(string collectionId)
        {
            var collection = await _collectionProperties.Find(c => c.id.ToString() == collectionId).FirstOrDefaultAsync();
            if (collection == null || collection.listProperties == null) return;

            // Get the first 4 property IDs
            var firstFourPropertyIds = collection.listProperties.Take(4).ToList();

            // Fetch properties and their images
            var properties = await _property.Find(p => firstFourPropertyIds.Contains(p.id.ToString())).ToListAsync();
            var images = properties
                .Where(p => p.images != null)
                .SelectMany(p => p.images)
                .Take(4)
                .ToList();

            // Update collection with the first 4 images
            if (images.Any())
            {
                var update = Builders<CollectionProperty>.Update.Set(c => c.collectionImage, images);
                await _collectionProperties.UpdateOneAsync(c => c.id.ToString() == collectionId, update);
            }
        }


    }
}

