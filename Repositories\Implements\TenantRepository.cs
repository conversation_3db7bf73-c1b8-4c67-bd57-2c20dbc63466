﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using MongoDB.Driver;
using Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Repositories.Implements
{
    public class TenantRepository : GenericRepository<Tenant>, ITenantRepository
    {
        private readonly IMongoCollection<Tenant> _tenants;

        public TenantRepository(RevoLandDbContext context) : base(context, "Tenants")
        {
            _tenants = context.GetCollection<Tenant>("Tenants");
        }

        public async Task<Tenant> GetTenantByPhone(string phone)
        {
            var filter = Builders<Tenant>.Filter.Eq(t => t.phone, phone);
            return await _tenants.Find(filter).FirstOrDefaultAsync();
        }
        public async Task<bool> IsPhoneExist(string phone)
        {
            var filter = Builders<Tenant>.Filter.Eq(t => t.phone, phone);
            return await _tenants.Find(filter).AnyAsync();
        }

        public async Task<bool> IsEmailExist(string email)
        {
            var filter = Builders<Tenant>.Filter.Eq(t => t.email, email);
            return await _tenants.Find(filter).AnyAsync();
        }
    }
}
