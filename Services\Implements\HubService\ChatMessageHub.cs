﻿using BusinessObjects.DTOs.ChatMessageDTOs;
using BusinessObjects.Models;
using BusinessObjects.QueryObject;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.SignalR;
using MongoDB.Bson;
using Repositories.Interfaces;
using Services.Interfaces;
using System.Security.Claims;

namespace Services.Implements.HubService
{
    public class ChatMessageHub : Hub
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IChatMessageService _chatMessageService;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IUserRepository _userRepository;

        public ChatMessageHub(IHttpContextAccessor httpContextAccessor, IChatMessageService chatMessageService, IUnitOfWork unitOfWork, IUserRepository userRepository)
        {
            _httpContextAccessor = httpContextAccessor;
            _chatMessageService = chatMessageService;
            _unitOfWork = unitOfWork;
            _userRepository = userRepository;
        }

        public override async Task OnConnectedAsync()
        {
            var userId = _httpContextAccessor.HttpContext?.User?.FindFirstValue("UserID");
            var role = _httpContextAccessor.HttpContext?.User?.FindFirst(ClaimTypes.Role)?.Value;

            if (!string.IsNullOrEmpty(userId) && role == "Admin")
            {
                await Groups.AddToGroupAsync(Context.ConnectionId, userId);
            }

            await base.OnConnectedAsync();
        }


        public async Task JoinConversation(string conversationId, string userId = null)
        {
            try
            {
                if (string.IsNullOrEmpty(conversationId) || !ObjectId.TryParse(conversationId, out _))
                    throw new HubException("Conversation ID không đúng định dạng.");

                // Lấy thông tin user từ SignalR Context thay vì HttpContext
                var user = Context.User; // Lấy ClaimsPrincipal từ SignalR Context
                var contextUserId = Context.UserIdentifier; // UserId từ SignalR Context

                string role = "Anonymous"; // Default cho anonymous user
                bool isAuthenticated = user?.Identity?.IsAuthenticated == true;

                if (isAuthenticated)
                {
                    // User đã đăng nhập - lấy thông tin từ JWT claims
                    var userIdFromClaims = user.FindFirstValue("UserID");
                    role = user.FindFirstValue(ClaimTypes.Role) ?? "Anonymous";

                    // Ưu tiên userId từ JWT token (bảo mật)
                    userId = userIdFromClaims ?? contextUserId;
                }
                else
                {
                    // Anonymous user - cần userId từ client
                    if (string.IsNullOrEmpty(userId))
                    {
                        throw new HubException("❌ Anonymous user cần cung cấp User ID.");
                    }
                }

                // Debug log để kiểm tra
                Console.WriteLine($"🔍 Context.UserIdentifier: {contextUserId}");
                Console.WriteLine($"🔍 UserId from claims: {user?.FindFirstValue("UserID")}");
                Console.WriteLine($"🔍 Final userId: {userId}");
                Console.WriteLine($"🔍 Role: {role}");
                Console.WriteLine($"🔍 IsAuthenticated: {isAuthenticated}");

                // Validate userId
                if (string.IsNullOrEmpty(userId) || !ObjectId.TryParse(userId, out _))
                    throw new HubException("❌ User ID không hợp lệ.");

                var conversation = await _unitOfWork.Conversations.GetByIdAsync(conversationId);
                if (conversation == null)
                    throw new HubException("❌ Conversation không tồn tại.");

                // Authorization logic
                var isAdmin = string.Equals(role, "Admin", StringComparison.OrdinalIgnoreCase);
                var isOwner = string.Equals(conversation.userId, userId, StringComparison.Ordinal);
                var isAnonymous = string.Equals(role, "Anonymous", StringComparison.OrdinalIgnoreCase);

                // Kiểm tra quyền truy cập
                if (!isAdmin && !isOwner)
                {
                    throw new HubException("Bạn không có quyền truy cập cuộc trò chuyện này.");
                }

                // Join group
                await Groups.AddToGroupAsync(Context.ConnectionId, conversation.id);

                // Load message history
                var query = new QueryChatMessage { pageNumber = 1, pageSize = 10 };
                var messages = await _chatMessageService.GetChatMessagesByConversationIdAsync(conversation.id, query);

                await Clients.Caller.SendAsync("LoadMessageHistory", messages);

                // Success log
                Console.WriteLine($"User {userId} (Role: {role}) joined conversation {conversationId}");

                // Thông báo cho client về việc join thành công
                await Clients.Caller.SendAsync("JoinedConversation", new
                {
                    conversationId = conversation.id,
                    userId = userId,
                    role = role,
                    message = "Tham gia cuộc trò chuyện thành công"
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ JoinConversation Error: {ex.Message}");
                throw new HubException($"Tham gia cuộc trò chuyện thất bại: {ex.Message}");
            }
        }


        public async Task NotifyNewConversation(string conversationId, string userId)
        {
            await Clients.Caller.SendAsync("NewConversationCreated", new
            {
                conversationId,
                userId
            });
        }

        public async Task LoadMoreMessages(string conversationId, QueryChatMessage query)
        {
            var messages = await _chatMessageService.GetChatMessagesByConversationIdAsync(conversationId, query);
            var response = messages.Select(m => new ChatMessageResponse
            {
                id = m.id.ToString(),
                senderId = m.senderId,
                direction = m.direction,
                content = m.content,
                createdAt = m.createdAt,
                isDeleted = m.isDeleted,
                replyToMessageId = m.replyToMessageId,
                attachments = m.attachments?.Select(a => new ChatMessageAttachments
                {
                    type = a.type,
                    url = a.url,
                    fileName = a.fileName
                }).ToList(),
            }).ToList();
            await Clients.Caller.SendAsync("LoadMessages", response);
        }

        public async Task LeaveConversation(string conversationId)
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, conversationId);
        }

        public async Task ReceiveNotification(string receiverId)
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, receiverId);
        }

        internal async Task BroadcastMessage(string conversationId, string senderId, string content, SenderType senderType)
        {
            await Clients.Group(conversationId).SendAsync("ReceiveMessage", senderId, content, senderType);
            var adminUserIds = await _userRepository.GetAdminUserIdsAsync();

            foreach (var adminId in adminUserIds)
            {
                var conversation = await _unitOfWork.Conversations.GetByIdAsync(conversationId);
                await Clients.Group(adminId).SendAsync("ConversationUpdated", conversation);
            }
        }

        internal async Task BroadcastNotification(string receiverId, string title, string body)
        {
            await Clients.Group(receiverId).SendAsync("ReceiveNotification", title, body);
        }
    }
}
