﻿using BusinessObjects.DTOs.CollectionDTOs;
using BusinessObjects.Models;
using MongoDB.Bson;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Services.Interfaces
{
    public interface ICollectionPropertyService 
    {
        Task<CollectionGetResponse> GetCollectionByIdAsync(string id);
        Task<List<CollectionGetResponse>> GetCollectionByUserIdAsync();
        Task<CollectionProperty> CreateCollectionAsync(CollectionCreateRequest createRequest);

        Task <CollectionProperty>UpdateCollectionAsync(string id, CollectionUpdateRequest updateCollection);
        Task<CollectionProperty> DeleteCollectionAsync(string id);
        Task<List<CollectionGetResponse>> GetAllCollectionsAsync();

        Task<CollectionProperty> AddPropertyToCollection(string collectionId, string propertyId);
        Task<CollectionProperty> RemovePropertyFromCollection(string collectionId, string propertyId);


    }
}
