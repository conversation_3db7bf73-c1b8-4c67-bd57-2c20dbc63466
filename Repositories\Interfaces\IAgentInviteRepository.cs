﻿using BusinessObjects.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Repositories.Interfaces
{
	public interface IAgentInviteRepository : IGenericRepository<AgentInvite>
	{
		Task<AgentInvite> GetByEmailAndCompanyAsync(string email, string companyId);
		Task CreateNewAsync(string email, string companyId, string token, AgentCompanyRole role, int validHours);
		Task UpdateInviteTokenAsync(string email, string companyId, string token, int validHours);
		Task MarkUsedAsync(string id);
		Task<AgentInvite> GetByTokenAsync(string token);
	}
}
