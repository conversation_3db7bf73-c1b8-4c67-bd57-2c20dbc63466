﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.DraftPropertyDTOs;
using BusinessObjects.Models;
using Repositories.Interfaces;
using Services.Interfaces;
using System.Globalization;
using System.Text;
using System.Text.RegularExpressions;

namespace Services.Implements
{
    public class DraftPropertyService : IDraftPropertyService
    {
        private readonly IUnitOfWork _unitOfWork;

        public DraftPropertyService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task AddDraftProperty(CreateDraftPropertyRequest request, string userId)
        {
            if (request == null)
            {
                throw new ArgumentNullException(nameof(request), "Request cannot be null");
            }

            var existedUser = await _unitOfWork.Users.GetByIdAsync(userId) ?? throw new InvalidDataException("Không tìm thấy ID người dùng");
            var location = new Location
            {
                city = request.location?.city ?? "",
                district = request.location?.district ?? "",
                ward = request.location?.ward ?? "",
                address = request.location?.address ?? "",
                latitude = request.location?.latitude ?? 0,
                longitude = request.location?.longitude ?? 0
            };

            var priceDetail = new PriceDetail
            {
                currency = request.priceDetails?.currency ?? "VND",
                depositAmount = request.priceDetails?.depositAmount,
                maintenanceFee = request.priceDetails?.maintenanceFee,
                paymentMethods = request.priceDetails?.paymentMethods ?? new List<string>(),
                pricePerSquareMeter = request.priceDetails?.pricePerSquareMeter,
                salePrice = request.priceDetails?.salePrice,
                rentalPrice = request.priceDetails?.rentalPrice
            };

            var property = new DraftProperty
            {
                salerId = existedUser.role == UserRole.Saler || existedUser.role == UserRole.Admin ? existedUser.id : null,
                ownerId = existedUser.role == UserRole.Owner ? existedUser.id : null,
                title = string.IsNullOrEmpty(request.title) ? request.title : null,
                name = string.IsNullOrEmpty(request.name) ? request.name : null,
                slug = string.IsNullOrEmpty(request.name) ? CreateSlug(request.name) : null,
                description = string.IsNullOrEmpty(request.description) ? CreateSlug(request.description) : null,
                transactionType = request.transactionType ?? PropertyTransactionType.ForRent,
                type = request.type ?? PropertyType.Apartment,
                status = request.status ?? PropertyStatus.Available,
                adminNote = string.IsNullOrEmpty(request.adminNote) ? request.adminNote : null,
                code = string.IsNullOrEmpty(request.code) ? request.code : null,
                yearBuilt = request.yearBuilt,
                contactName = string.IsNullOrEmpty(existedUser.userName) ? existedUser.userName : null,
                contactPhone = string.IsNullOrEmpty(existedUser.phoneNumber) ? existedUser.phoneNumber : null,
                contactEmail = string.IsNullOrEmpty(existedUser.email) ? existedUser.email : null,
                createdAt = DateTime.UtcNow,
                updatedAt = DateTime.UtcNow,
                location = location,
                propertyDetails = request.propertyDetails ?? new PropertyDetail(),
                amenities = request.amenities ?? new Amenity(),
                images = request.images?.ToList() ?? new List<string>(),
                legalDocuments = request.legalDocuments?.ToList() ?? new List<string>(),
                floorPlans = request.floorPlans?.ToList() ?? new List<string>(),
                video = request.video ?? new PropertyVideo(),
                priceDetails = priceDetail,
            };

            await _unitOfWork.DraftProperties.AddAsync(property);
            await _unitOfWork.SaveAsync();
        }

        public async Task<PagedResult<DraftPropertyResponse>> GetDraftPropertiesByUserAsync(string userId, string role, int pageSize, int currentPage)
        {
            User existedUser = await _unitOfWork.Users.GetByIdAsync(userId) ?? throw new InvalidDataException("Không tìm thấy ID người dùng");
            var (total, properties) = await _unitOfWork.DraftProperties.GetDraftPropertiesByUserAsync(userId, role, pageSize, currentPage);

            //convert to response
            var propertyResponses = await Task.WhenAll(properties.Select(ToDraftPropertiesResponse));
            var data = propertyResponses.ToList();


            return new PagedResult<DraftPropertyResponse>
            {
                data = data,
                pageSize = pageSize,
                currentPage = currentPage,
                totalCount = total,
                totalPages = (int)Math.Ceiling((double)total / pageSize)
            };
        }

        public async Task DeleteByIdAsync(string userId, string propertyId)
        {
            if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(propertyId))
            {
                throw new ArgumentException("User ID và Property ID không tìm thấy");
            }
            var property = await _unitOfWork.DraftProperties.GetByIdAsync(propertyId) ?? throw new KeyNotFoundException("Không tìm thấy property nháp");
            if (property.salerId != userId && property.ownerId != userId)
            {
                throw new UnauthorizedAccessException("Bạn không có quyền thực hiện hành động này");
            }
            await _unitOfWork.DraftProperties.DeleteAsync(propertyId);
            await _unitOfWork.SaveAsync();
        }


        public async Task<DraftPropertyResponse> GetDraftPropertyByIdAsync(string userId, string propertyId)
        {
            if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(propertyId))
            {
                throw new ArgumentException("User ID và Property ID không tìm thấy");
            }
            var property = await _unitOfWork.DraftProperties.GetByIdAsync(propertyId) ?? throw new KeyNotFoundException("Không tìm thấy property nháp");
            if (property.salerId != userId && property.ownerId != userId)
            {
                throw new UnauthorizedAccessException("Bạn không có quyền thực hiện hành động này");
            }
            return await ToDraftPropertiesResponse(property);
        }

        private Task<DraftPropertyResponse> ToDraftPropertiesResponse(DraftProperty property)
        {
            var location = new Location
            {
                city = property.location?.city ?? "",
                district = property.location?.district ?? "",
                ward = property.location?.ward ?? "",
                address = property.location?.address ?? "",
                latitude = property.location?.latitude ?? 0,
                longitude = property.location?.longitude ?? 0
            };

            var priceDetail = new PriceDetail
            {
                currency = property.priceDetails?.currency ?? "VND",
                depositAmount = property.priceDetails?.depositAmount,
                maintenanceFee = property.priceDetails?.maintenanceFee,
                paymentMethods = property.priceDetails?.paymentMethods ?? new List<string>(),
                pricePerSquareMeter = property.priceDetails?.pricePerSquareMeter,
                salePrice = property.priceDetails?.salePrice,
                rentalPrice = property.priceDetails?.rentalPrice
            };

            var response = new DraftPropertyResponse
            {
                id = property.id.ToString(),
                title = property.title,
                name = property.name,
                description = property.description,
                transactionType = property.transactionType.ToString(),
                type = property.type.ToString(),
                status = property.status.ToString(),
                adminNote = property.adminNote,
                code = property.code,
                location = location,
                propertyDetails = property.propertyDetails,
                priceDetails = property.priceDetails,
                amenities = property.amenities,
                imageUrls = property.images,
                floorPlanUrls = property.floorPlans,
                video = property.video,
                yearBuilt = property.yearBuilt,
                legalDocumentUrls = property.legalDocuments,
                contactName = property.contactName,
                contactPhone = property.contactPhone,
                contactEmail = property.contactEmail,
                createdAt = property.createdAt,
                updatedAt = property.updatedAt
            };
            return Task.FromResult(response);
        }

        //slug hellper
        private string CreateSlug(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
                return string.Empty;

            text = text.ToLowerInvariant();
            text = RemoveDiacritics(text);
            text = text.Replace("đ", "d");

            text = Regex.Replace(text, @"[^a-z0-9\s]", "");
            text = Regex.Replace(text, @"\s+", "-");

            return text.Trim('-');
        }

        private string RemoveDiacritics(string text)
        {
            var normalizedString = text.Normalize(NormalizationForm.FormD);
            var stringBuilder = new StringBuilder();

            foreach (var c in normalizedString)
            {
                var unicodeCategory = CharUnicodeInfo.GetUnicodeCategory(c);
                if (unicodeCategory != UnicodeCategory.NonSpacingMark)
                {
                    stringBuilder.Append(c);
                }
            }

            return stringBuilder.ToString().Normalize(NormalizationForm.FormC);
        }

    }
}
