﻿using BusinessObjects.DTOs.AppoinmentDTOs;
using BusinessObjects.Models;
using static BusinessObjects.DTOs.ResultDTOs.ResultDTO;

namespace Repositories.Interfaces
{
    public interface IAppointmentRepository : IGenericRepository<Appointment>
    {
        Task<Appointment> GetByLeadIdAndPropertyIdAsync(string guestId, string propertyId);
        Task<List<Appointment>> GetBySalerIdAsync(string sellerId, string status);
        Task<List<Appointment>> GetByLeadIdAsync(string leadId, string status);

        Task<List<Appointment>> GetUserAppointmentsByDateRangeAsync(string userId, DateTime startDate, DateTime endDate);

        Task<bool> HasTimeConflictAsync(string userId, DateTime appointmentDate, int durationMinutes = 60, string excludeAppointmentId = null);

        Task<List<AppointmentConflictInfo>> GetConflictingAppointmentsAsync(string userId, DateTime appointmentDate, int durationMinutes = 60, string? excludeAppointmentId = null);

        //Check conflict for both user and saler (useful for reschedule)
        Task<bool> HasTimeConflictForBothPartiesAsync(string userId, string salerId, DateTime appointmentDate, int durationMinutes = 60, string? excludeAppointmentId = null);

        Task<ConflictCheckResult> GetConflictingAppointmentsForBothPartiesAsync(string userId, string salerId, DateTime appointmentDate, int durationMinutes = 60, string? excludeAppointmentId = null);

    }
}
