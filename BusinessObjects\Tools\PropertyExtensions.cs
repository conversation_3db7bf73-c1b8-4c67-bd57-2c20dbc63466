using BusinessObjects.DTOs.CollectionDTOs;
using BusinessObjects.Models;

namespace BusinessObjects.Tools
{
    public static class PropertyExtensions
    {
        public static PropertyResponseDTO ToPropertyResponseDTO(this Property property)
        {
            if (property == null) return null;

            return new PropertyResponseDTO
            {
                id = property.id.ToString(), // Convert ObjectId to string
                title = property.title,
                salerId = property.salerId,
                name = property.name,
                slug = property.slug,
                description = property.description,
                transactionType = property.transactionType,
                type = property.type,
                status = property.status,
                adminNote = property.adminNote,
                code = property.code,
                ownerId = property.ownerId,
                location = property.location,
                propertyDetails = property.propertyDetails,
                priceDetails = property.priceDetails,
                amenities = property.amenities,
                images = property.images,
                floorPlans = property.floorPlans,
                video = property.video,
                yearBuilt = property.yearBuilt,
                legalDocuments = property.legalDocuments,
                transactionHistory = property.transactionHistory,
                createdAt = property.createdAt,
                updatedAt = property.updatedAt,
                contactName = property.contactName,
                contactPhone = property.contactPhone,
                contactEmail = property.contactEmail,
                isFeatured = property.isFeatured,
                isVerified = property.isVerified,
                isDraft = property.isDraft
            };
        }

        public static List<PropertyResponseDTO> ToPropertyResponseDTOs(this IEnumerable<Property> properties)
        {
            return properties?.Select(p => p.ToPropertyResponseDTO()).Where(p => p != null).ToList() ?? new List<PropertyResponseDTO>();
        }
    }
} 