using BusinessObjects.Models;
using MongoDB.Driver;
using Repositories.Interfaces;
using System.Threading.Tasks;
using System.Collections.Generic;
using BusinessObjects.DatabaseSettings;

namespace Repositories.Implements
{
    public class SurveyRepository : ISurveyRepository
    {
        private readonly IMongoCollection<Survey> _surveys;

        public SurveyRepository(RevoLandDbContext context)
        {
            _surveys = context.GetCollection<Survey>("surveys");
        }

        public async Task<Survey> AddSurveyAsync(Survey survey)
        {
            await _surveys.InsertOneAsync(survey);
            return survey;
        }

        public async Task<Survey> GetSurveyByIdAsync(string id)
        {
            return await _surveys.Find(s => s.id == id).FirstOrDefaultAsync();
        }

        public async Task<List<Survey>> GetAllSurveysAsync()
        {
            return await _surveys.Find(_ => true).ToListAsync();
        }

        public async Task<Survey> UpdateSurveyAsync(string id, Survey survey)
        {
            var filter = Builders<Survey>.Filter.Eq(s => s.id, id);
            var update = Builders<Survey>.Update
                .Set(s => s.fullName, survey.fullName)
                .Set(s => s.email, survey.email)
                .Set(s => s.phoneNumber, survey.phoneNumber)
                .Set(s => s.propertyTypeInterest, survey.propertyTypeInterest)
                .Set(s => s.budgetCategory, survey.budgetCategory)
                .Set(s => s.locationPreference, survey.locationPreference)
                .Set(s => s.additionalComments, survey.additionalComments)
                .Set(s => s.isConvertedToLead, survey.isConvertedToLead);

            return await _surveys.FindOneAndUpdateAsync(filter, update, new FindOneAndUpdateOptions<Survey> { ReturnDocument = ReturnDocument.After });
        }

        public async Task<bool> DeleteSurveyAsync(string id)
        {
            var result = await _surveys.DeleteOneAsync(s => s.id == id);
            return result.DeletedCount > 0;
        }

        public async Task<Survey> ConvertToLeadAsync(string id, bool isConverted)
        {
            var filter = Builders<Survey>.Filter.Eq(s => s.id, id);
            var update = Builders<Survey>.Update.Set(s => s.isConvertedToLead, isConverted);
            return await _surveys.FindOneAndUpdateAsync(filter, update, new FindOneAndUpdateOptions<Survey> { ReturnDocument = ReturnDocument.After });
        }
    }
} 