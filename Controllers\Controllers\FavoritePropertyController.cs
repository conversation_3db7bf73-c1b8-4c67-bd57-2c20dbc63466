﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.FavoritePropertyDTOs;
using BusinessObjects.QueryObject;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Services.Interfaces;

namespace Controllers.Controllers
{
    [Route("api/favorite-properties")]
    [ApiController]
    [Authorize]
    public class FavoritePropertyController : ControllerBase
    {
        private readonly IFavoritePropertyService _favoritePropertyService;

        public FavoritePropertyController(IFavoritePropertyService favoritePropertyService)
        {
            _favoritePropertyService = favoritePropertyService;
        }

        private ApiResponse CreateResponse(int code, bool status, string message, object data = null)
        {
            return new ApiResponse { code = code, status = status, message = message, data = data };
        }

        [HttpPost]
        public async Task<IActionResult> AddFavoriteProperty(FavoritePropertyCreateRequest request)
        {
            try
            {
                var result = await _favoritePropertyService.AddFavoritePropertyAsync(request);
                return Ok(CreateResponse(200, true, "Thành công"));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(500, false, ex.Message));
            }
        }

        [HttpGet("user")]
        public async Task<IActionResult> GetFavoriteProperties([FromQuery] QueryProperty query)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(CreateResponse(400, false, "Query không hợp lệ", ModelState));
            }
            try
            {
                var currentUserId = User.Claims.FirstOrDefault(c => c.Type == "UserID")?.Value;
                if (string.IsNullOrEmpty(currentUserId))
                {
                    return Unauthorized(CreateResponse(401, false, "Người dùng chưa được xác thực"));
                }

                var result = await _favoritePropertyService.GetFavoritePropertyByUserIdAsync(query, currentUserId);
                return Ok(CreateResponse(200, true, "Thành công", result));
            }
            catch (InvalidDataException ex)
            {
                return NotFound(CreateResponse(404, false, ex.Message));
            }
            catch (InvalidOperationException ex)
            {
                return Conflict(CreateResponse(409, false, ex.Message));
            }
            catch (ArgumentException ex)
            {
                return BadRequest(CreateResponse(400, false, ex.Message));
            }
        }

        [Authorize(Roles = "User")]
        [HttpDelete("{propertyId}")]
        public async Task<IActionResult> DeleteFavoriteProperty(string propertyId)
        {
            try
            {
                if (string.IsNullOrEmpty(propertyId))
                {
                    return BadRequest(CreateResponse(400, false, "Property ID không được để trống"));
                }
                await _favoritePropertyService.DeleteFavoritePropertyAsync(propertyId);
                return Ok(CreateResponse(200, true, "Thành công"));
            }
            catch (InvalidDataException ex)
            {
                return NotFound(CreateResponse(404, false, ex.Message));
            }
            catch (InvalidOperationException ex)
            {
                return Conflict(CreateResponse(409, false, ex.Message));
            }
            catch (ArgumentException ex)
            {
                return BadRequest(CreateResponse(400, false, ex.Message));
            }
        }
    }
}