﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using MongoDB.Driver;
using Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Repositories.Implements
{
    public class ZaloTokenRepository : GenericRepository<ZaloToken>, IZaloTokenRepository
    {
        private readonly IMongoCollection<ZaloToken> _zaloTokens;
        public ZaloTokenRepository(RevoLandDbContext context) : base(context, "ZaloTokens")
        {
            _zaloTokens = context.GetCollection<ZaloToken>("ZaloTokens");
        }
    }
}
