﻿using BusinessObjects.Models;
using FirebaseAdmin.Messaging;
using Microsoft.AspNetCore.SignalR;
using MongoDB.Bson;
using Repositories.Interfaces;
using Services.Implements.HubService;
using Services.Interfaces;


namespace Services.Implements
{
    public class ChatService : IChatService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IHubContext<ChatMessageHub> _hubContext;
        public ChatService(IUnitOfWork unitOfWork, IHubContext<ChatMessageHub> hubContext)
        {
            _unitOfWork = unitOfWork;
            _hubContext = hubContext;
        }

        public async Task<ChatSession> StartChatSessionAsync(string? userId)
        {
            ChatSession chatSession;

            if (string.IsNullOrEmpty(userId))
            {
                // Anonymous session
                var anonymousSession = new AnonymousSession
                {
                    sessionId = ObjectId.GenerateNewId().ToString()
                };

                await _unitOfWork.AnonymousSessions.AddAsync(anonymousSession);

                chatSession = new ChatSession
                {
                    sessionId = ObjectId.GenerateNewId().ToString(),
                    anonymousSessionId = anonymousSession.sessionId,
                    status = "anonymous"
                };
            }
            else
            {
                // Authenticated session
                var user = await _unitOfWork.Users.GetByIdAsync(userId);
                if (user == null)
                {
                    throw new ArgumentException("User does not exist.", nameof(userId));
                }

                var existingSessions = await _unitOfWork.ChatSessions.GetByUserIdAsync(userId);
                if (existingSessions != null && existingSessions.Any())
                {
                    return existingSessions.First(); // Return existing session
                }

                chatSession = new ChatSession
                {
                    sessionId = ObjectId.GenerateNewId().ToString(),
                    userId = userId,
                    status = "authenticated"
                };
            }

            await _unitOfWork.ChatSessions.AddAsync(chatSession);
            await _unitOfWork.SaveAsync();

            // Notify admins about new session
            await _hubContext.Clients.Group("admin_notifications").SendAsync(
                "ReceiveNotification",
                $"New {chatSession.status} Chat Session",
                $"Session started: {chatSession.sessionId}"
            );

            return chatSession;
        }

        public async Task SendMessageAsync(string sessionId, string? senderId, string content, SenderType senderType)
        {
            // Kiểm tra xem session có tồn tại không
            var chatSession = await _unitOfWork.ChatSessions.GetByIdAsync(sessionId);
            if (chatSession == null)
            {
                throw new ArgumentException("Chat session does not exist.", nameof(sessionId));
            }

            if (!string.IsNullOrEmpty(senderId))
            {
                var sender = await _unitOfWork.Users.GetByIdAsync(senderId);
                if (sender == null)
                {
                    throw new ArgumentException("Sender does not exist.", nameof(senderId));
                }
            }

            var message = new BusinessObjects.Models.Message
            {
                senderId = senderId ?? "anonymous",
                content = content,
                senderType = senderType
            };

            await _unitOfWork.ChatSessions.AddMessageAsync(sessionId, message);
            await _unitOfWork.SaveAsync();
            await _hubContext.Clients.Group(sessionId).SendAsync("ReceiveMessage", senderId ?? "anonymous", content, senderType.ToString());

            if (senderType == SenderType.User || senderId == null)
            {
                await _hubContext.Clients.Group("admin_notifications").SendAsync(
                    "ReceiveNotification",
                    $"New Message from {senderType}",
                    $"{senderType} {senderId ?? "anonymous"}: {content}"
                );

                await NotifyAdminAsync(chatSession.userId ?? "anonymous", content, senderType);
            }
        }

        public async Task<ChatSession> GetChatSessionAsync(string sessionId)
        {
            var chatSession = await _unitOfWork.ChatSessions.GetByIdAsync(sessionId);
            if (chatSession == null)
            {
                throw new ArgumentException("Chat session does not exist.", nameof(sessionId));
            }
            return chatSession;
        }

        public async Task<List<ChatSession>> GetUserChatSessionsAsync(string userId)
        {
            // Kiểm tra xem user có tồn tại không
            var user = await _unitOfWork.Users.GetByIdAsync(userId);
            if (user == null)
            {
                throw new ArgumentException("User does not exist.", nameof(userId));
            }

            return await _unitOfWork.ChatSessions.GetByUserIdAsync(userId);
        }

        public async Task<List<ChatSession>> GetAllChatSessionsAsync()
        {
            return await _unitOfWork.ChatSessions.GetAllChatSessionsAsync();
        }

        private async Task NotifyAdminAsync(string userId, string content, SenderType senderType)
        {
            var message = new FirebaseAdmin.Messaging.Message
            {
                Notification = new FirebaseAdmin.Messaging.Notification
                {
                    Title = $"New Message from {senderType}",
                    Body = $"{userId}: {content}"
                },
                Topic = "admin_notifications"
            };

            try
            {
                await FirebaseMessaging.DefaultInstance.SendAsync(message);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to send FCM notification: {ex.Message}");
            }
        }
    }
}
