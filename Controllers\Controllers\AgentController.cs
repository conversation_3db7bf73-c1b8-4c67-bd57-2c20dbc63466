﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.AgentDTOs;
using BusinessObjects.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Services.Interfaces;

namespace Controllers.Controllers
{
	[Route("api/agent")]
	[ApiController]
	public class AgentController : ControllerBase
	{
		private readonly IAgentService _agentService;
		public AgentController(IAgentService agentService)
		{
			_agentService = agentService;
		}
		private ApiResponse CreateResponse(int code, bool status, string message, object data = null)
		{
			return new ApiResponse { code = code, status = status, message = message, data = data };
		}
		/// <summary>
		/// Create new Agent
		/// </summary>
		[HttpPost]
		public async Task<IActionResult> CreateAgent([FromForm] CreateAgentRequest request)
		{
			try
			{
				var agent = await _agentService.CreateAgentAsync(request);
				return Ok(CreateResponse(201, true, "Agent created successfully", agent));
			}
			catch (Exception ex)
			{
				return BadRequest(CreateResponse(400, false, ex.Message));
			}
		}

		
		//[HttpGet]
		//public async Task<IActionResult> GetAllAgents()
		//{
		//	try
		//	{
		//		var agents = await _agentService.GetAllAgentsAsync();
		//		return Ok(CreateResponse(200, true, "Fetch all Agents successfully", agents));
		//	}
		//	catch (Exception ex)
		//	{
		//		return BadRequest(CreateResponse(400, false, ex.Message));
		//	}
		//}

		[HttpGet("get-by-current-company")]
		public async Task<IActionResult> GetAgentsByCurrentCompany()
		{
			var companyId = User.Claims.FirstOrDefault(c => c.Type == "CompanyID")?.Value;
			if (string.IsNullOrEmpty(companyId))
			{
				return BadRequest(CreateResponse(400, false, "CompanyID not found in token."));
			}
			try
			{
				var agents = await _agentService.GetAllAgentsByCompanyIdAsync(companyId);
				return Ok(CreateResponse(200, true, "Fetch Agents by company successfully", agents));
			}
			catch (Exception ex)
			{
				return BadRequest(CreateResponse(400, false, ex.Message));
			}
		}

		[HttpGet("{id}")]
		public async Task<IActionResult> GetAgentById(string id)
		{
			try
			{
				var agent = await _agentService.GetAgentByIdAsync(id);
				if (agent == null)
				{
					return NotFound(CreateResponse(404, false, "Agent not found"));
				}
				return Ok(CreateResponse(200, true, "Fetch Agent successfully", agent));
			}
			catch (Exception ex)
			{
				return BadRequest(CreateResponse(400, false, ex.Message));
			}
		}

		[HttpPost("assign-agent-to-team")]
		public async Task<IActionResult> AssignAgentToTeam([FromForm] AddAgentToTeamRequest request)
		{
			try
			{
				await _agentService.AssignAgentToTeam(request);
				return Ok(CreateResponse(200, true, "Assign agent to team successfully"));
			}
			catch (Exception ex)
			{
				return BadRequest(CreateResponse(400, false, ex.Message));
			}
		}

		[HttpDelete("team/{teamId}/agent/{agentId}")]
		public async Task<IActionResult> DeleteAgentFromTeam(string teamId, string agentId)
		{
			try
			{
				await _agentService.DeleteAgentFromTeam(teamId,agentId);
				return Ok(CreateResponse(200, true, "Delete agent from team successfully"));
			}
			catch (Exception ex)
			{
				return BadRequest(CreateResponse(400, false, ex.Message));
			}
		}
	}
}
