﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace BusinessObjects.Models
{
    public class Owner
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public ObjectId id { get; set; } = ObjectId.GenerateNewId();

        [BsonElement("name")]
        public string name { get; set; }

        [BsonElement("phone")]
        public string phone { get; set; }

        [BsonElement("email")]
        public string email { get; set; }

        [BsonElement("address")]
        public string address { get; set; }

        [BsonElement("gender")]
        [BsonRepresentation(BsonType.String)]
        public Gender? gender { get; set; }

        [BsonElement("userId")]
        public string userId { get; set; }

        [BsonElement("nationality")]
        public string nationality { get; set; }
        public string idVerification { get; set; }

        [BsonElement("createdAt")]
        public DateTime createdAt { get; set; } = DateTime.UtcNow;

        [BsonElement("updatedAt")]
        public DateTime updatedAt { get; set; } = DateTime.UtcNow;

    }

}
