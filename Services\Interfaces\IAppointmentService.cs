﻿using BusinessObjects.DTOs.AppoinmentDTOs;
using BusinessObjects.Models;
using static BusinessObjects.DTOs.ResultDTOs.ResultDTO;

namespace Services.Interfaces
{
    public interface IAppointmentService
    {
        Task<AppointmentResponse> AddAppointmentAsync(AppointmentCreateRequest appointment);
        Task CancelAppointmentAsync(string id);

        Task CancelAppointmentAsync(
                                    string id,
                                    string cancellationReason = null,
                                    CancellationType? cancellationType = null,
                                    string notes = null,
                                    string relatedTicketId = null);
        //Task<Appointment?> UpdateAppointmentAsync(string id, UpdateAppointmentRequest updateRequest);

        Task<Appointment> GetAppointmentByIdAsync(string id);

        Task CreateMessageForAppointmentAsync(string appointmentId, AppointmentMessageCreateRequest request);
        Task<List<AppointmentResponse>> GetAppointmentsByUserIdAsync(string status);
        Task RescheduleAppointmentAsync(string id, DateTime date);

        Task<AssignmentResult> AssignSellerToAppointmentAsyncs(string appointmentId, AssignSellerToAppointmentRequest request);

        Task<ReassignmentResult> ReassignSellerToAppointmentAsync(
            string appointmentId,
            ReassignSellerRequest request);

        Task<List<AppointmentResponse>> GetAppointmentsAsync();

        Task DeleteAppointmentAsync(string id);

        //Task AddApppointmentToCalendar(string appointmentId, CreateEventRequestForAppointment request);
    }
}
