﻿namespace BusinessObjects.Validations
{
    public class AppointmentTimeValidator
    {
        private const int DEFAULT_BUFFER_MINUTES = 30;

        /// <summary>
        /// Validates appointment time - cannot be in past and must have minimum buffer from current time
        /// </summary>
        /// <param name="appointmentDate">The requested appointment date/time</param>
        /// <param name="bufferMinutes">Minimum minutes from current time (default: 30)</param>
        /// <returns>True if valid, False if invalid</returns>
        public static bool IsValidAppointmentTime(DateTime appointmentDate, int bufferMinutes = DEFAULT_BUFFER_MINUTES)
        {
            var currentTime = DateTime.UtcNow;
            var minimumAllowedTime = currentTime.AddMinutes(bufferMinutes);

            // Check if appointment is in the past or too soon
            return appointmentDate >= minimumAllowedTime;
        }

        /// <summary>
        /// Gets the minimum allowed appointment time based on buffer
        /// </summary>
        /// <param name="bufferMinutes">Buffer minutes from current time (default: 30)</param>
        /// <returns>Minimum allowed DateTime</returns>
        public static DateTime GetMinimumAllowedTime(int bufferMinutes = DEFAULT_BUFFER_MINUTES)
        {
            return DateTime.UtcNow.AddMinutes(bufferMinutes);
        }
    }
}
