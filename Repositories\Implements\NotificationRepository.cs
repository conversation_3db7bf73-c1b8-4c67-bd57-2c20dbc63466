﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using MongoDB.Driver;
using Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Repositories.Implements
{
    public class NotificationRepository : INotificationRepository
    {
        private readonly IMongoCollection<Notification> _notificationCollection;

        public NotificationRepository(RevoLandDbContext dbContext)
        {
            _notificationCollection = dbContext.Notifications;
        }

        public async Task<List<Notification>> GetNotificationsByUserIdAsync(string userId)
        {
            var filter = Builders<Notification>.Filter.Eq(n => n.userId, userId);
            return await _notificationCollection.Find(filter)
                                                .SortByDescending(n => n.sentAt)
                                                .ToListAsync();
        }

        public async Task CreateAsync(Notification notification)
        {
            await _notificationCollection.InsertOneAsync(notification);
        }
    }
}
