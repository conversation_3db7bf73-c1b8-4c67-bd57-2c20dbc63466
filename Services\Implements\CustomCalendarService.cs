﻿using Google.Apis.Calendar.v3;
using Google.Apis.Calendar.v3.Data;
using Google.Apis.Services;
using Microsoft.Extensions.Logging;
using Repositories.Interfaces;
using Services.Interfaces;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using static BusinessObjects.DTOs.CalendarDTOs.GoogleCalendar;

namespace Services.Implements
{
    public class CustomCalendarService : ICustomCalendarService
    {
        private readonly ILogger<CustomCalendarService> _logger;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IHttpClientFactory _httpClientFactory;
        private const string GoogleCalendarApiBaseUrl = "https://www.googleapis.com/calendar/v3";

        public CustomCalendarService(ILogger<CustomCalendarService> logger, IUnitOfWork unitOfWork, IHttpClientFactory httpClientFactory)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
            _httpClientFactory = httpClientFactory;
        }
        public async Task<CreateCalendarResponse> CreateCalendarAsync(CreateCalendarRequest request)
        {
            try
            {
                _logger.LogInformation($"Creating calendar: {request.summary}");

                // Validate request
                ValidateCreateCalendarRequest(request);

                var service = CreateGoogleCalendarService(request.accessToken);

                // Tạo calendar object
                var calendar = new Calendar
                {
                    Summary = request.summary,
                    Description = request.description,
                    TimeZone = request.timeZone,
                    Location = request.location
                };

                // Tạo calendar
                var insertRequest = service.Calendars.Insert(calendar);
                var createdCalendar = await insertRequest.ExecuteAsync();

                _logger.LogInformation($"Calendar created successfully: {createdCalendar.Id}");

                // Thêm calendar vào CalendarList với custom colors
                var calendarListEntry = new CalendarListEntry
                {
                    Id = createdCalendar.Id,
                    BackgroundColor = request.backgroundColor,
                    ForegroundColor = request.foregroundColor
                };

                if (!string.IsNullOrEmpty(request.backgroundColor) || !string.IsNullOrEmpty(request.foregroundColor))
                {
                    var updateListRequest = service.CalendarList.Update(calendarListEntry, createdCalendar.Id);
                    await updateListRequest.ExecuteAsync();
                    _logger.LogInformation($"Calendar colors updated for: {createdCalendar.Id}");
                }

                // Xử lý chia sẻ calendar
                var sharedWith = new List<string>();
                if (request.shareRules?.Any() == true)
                {
                    sharedWith = await ShareCalendarAsync(service, createdCalendar.Id, request.shareRules);
                }

                // Nếu là public calendar
                if (request.isPublic)
                {
                    await MakeCalendarPublicAsync(service, createdCalendar.Id);
                    sharedWith.Add("public");
                }

                return new CreateCalendarResponse
                {
                    success = true,
                    calendarId = createdCalendar.Id,
                    summary = createdCalendar.Summary,
                    description = createdCalendar.Description,
                    timeZone = createdCalendar.TimeZone,
                    htmlLink = $"https://calendar.google.com/calendar/embed?src={Uri.EscapeDataString(createdCalendar.Id)}",
                    backgroundColor = request.backgroundColor,
                    foregroundColor = request.foregroundColor,
                    sharedWith = sharedWith,
                    message = "Calendar đã được tạo thành công"
                };
            }
            catch (Google.GoogleApiException ex)
            {
                _logger.LogError(ex, "Google API error while creating calendar");
                throw new InvalidOperationException($"Lỗi Google API: {ex.Message}", ex);
            }
            catch (ArgumentException ex)
            {
                _logger.LogError(ex, "Validation error while creating calendar");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error while creating calendar");
                throw new InvalidOperationException("Có lỗi xảy ra khi tạo calendar", ex);
            }
        }

        public async Task<string> CreateCalendarAsyncReturnId(CreateCalendarRequest request)
        {
            try
            {
                _logger.LogInformation($"Creating calendar: {request.summary}");

                // Validate request
                ValidateCreateCalendarRequest(request);

                var service = CreateGoogleCalendarService(request.accessToken);

                // Tạo calendar object
                var calendar = new Calendar
                {
                    Summary = request.summary,
                    Description = request.description,
                    TimeZone = request.timeZone,
                    Location = request.location
                };

                // Tạo calendar
                var insertRequest = service.Calendars.Insert(calendar);
                var createdCalendar = await insertRequest.ExecuteAsync();

                _logger.LogInformation($"Calendar created successfully: {createdCalendar.Id}");

                // Thêm calendar vào CalendarList với custom colors
                var calendarListEntry = new CalendarListEntry
                {
                    Id = createdCalendar.Id,
                    BackgroundColor = request.backgroundColor,
                    ForegroundColor = request.foregroundColor
                };

                if (!string.IsNullOrEmpty(request.backgroundColor) || !string.IsNullOrEmpty(request.foregroundColor))
                {
                    var updateListRequest = service.CalendarList.Update(calendarListEntry, createdCalendar.Id);
                    await updateListRequest.ExecuteAsync();
                    _logger.LogInformation($"Calendar colors updated for: {createdCalendar.Id}");
                }

                // Xử lý chia sẻ calendar
                var sharedWith = new List<string>();
                if (request.shareRules?.Any() == true)
                {
                    sharedWith = await ShareCalendarAsync(service, createdCalendar.Id, request.shareRules);
                }

                // Nếu là public calendar
                if (request.isPublic)
                {
                    await MakeCalendarPublicAsync(service, createdCalendar.Id);
                    sharedWith.Add("public");
                }

                return createdCalendar.Id;
            }
            catch (Google.GoogleApiException ex)
            {
                _logger.LogError(ex, "Google API error while creating calendar");
                throw new InvalidOperationException($"Lỗi Google API: {ex.Message}", ex);
            }
            catch (ArgumentException ex)
            {
                _logger.LogError(ex, "Validation error while creating calendar");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error while creating calendar");
                throw new InvalidOperationException("Có lỗi xảy ra khi tạo calendar", ex);
            }
        }


        public async Task<CreateEventResponse> CreateEventAsync(CreateEventRequest request)
        {
            try
            {
                _logger.LogInformation($"Creating event: {request.title}");

                // Validate request
                ValidateCreateEventRequest(request);

                // Tạo Google Calendar service
                var service = CreateGoogleCalendarService(request.accessToken);

                // Tạo event object
                var calendarEvent = new Event
                {
                    Summary = request.title,
                    Description = request.description,
                    Location = request.location,
                    Start = new EventDateTime
                    {
                        DateTime = request.isAllDay ? null : request.startTime,
                        Date = request.isAllDay ? request.startTime.ToString("yyyy-MM-dd") : null,
                        TimeZone = request.timeZone
                    },
                    End = new EventDateTime
                    {
                        DateTime = request.isAllDay ? null : request.startTime,
                        Date = request.isAllDay ? request.endTime.ToString("yyyy-MM-dd") : null,
                        TimeZone = request.timeZone
                    },
                    ColorId = request.colorId?.ToString(),
                    Attendees = request.attendees.Select(email => new EventAttendee { Email = email }).ToList()
                };

                // Thêm reminders
                if (request.reminders.Count != 0)
                {
                    calendarEvent.Reminders = new Event.RemindersData
                    {
                        UseDefault = false,
                        Overrides = request.reminders.Select(r => new Google.Apis.Calendar.v3.Data.EventReminder
                        {
                            Method = r.method,
                            Minutes = r.minutes
                        }).ToList()
                    };
                }

                // Thêm recurrence rules
                if (request.recurrenceRules.Count != 0)
                {
                    calendarEvent.Recurrence = request.recurrenceRules;
                }

                // Tạo event
                var insertRequest = service.Events.Insert(calendarEvent, request.calendarId);
                insertRequest.SendNotifications = request.sendNotifications;

                var createdEvent = await insertRequest.ExecuteAsync();

                _logger.LogInformation($"Event created successfully: {createdEvent.Id}");

                return new CreateEventResponse
                {
                    success = true,
                    eventId = createdEvent.Id,
                    htmlLink = createdEvent.HtmlLink,
                    startTime = createdEvent.Start.DateTime,
                    endTime = createdEvent.End.DateTime,
                    calendarId = request.calendarId,
                    message = "Event đã được tạo và thêm vào calendar"
                };
            }
            catch (Google.GoogleApiException ex)
            {
                _logger.LogError(ex, "Google API error while creating event");
                throw new InvalidOperationException($"Lỗi Google API: {ex.Message}", ex);
            }
            catch (ArgumentException ex)
            {
                _logger.LogError(ex, "Validation error while creating event");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error while creating event");
                throw new InvalidOperationException("Có lỗi xảy ra khi tạo event", ex);
            }
        }

        //public async Task<CreateEventResponse> CreateEventAsync(Appointment appointment, CreateEventRequestForAppointment request)
        //{
        //    try
        //    {
        //        var lead = await _unitOfWork.Leads.GetByIdAsync(appointment.leadId) ?? throw new ArgumentException("Không tìm thấy lead với ID đã cho");
        //        User saler = null;
        //        List<string> attendees = new List<string>();
        //        if (!string.IsNullOrEmpty(appointment.salerId))
        //        {
        //            saler = await _unitOfWork.Users.GetByIdAsync(appointment.salerId) ?? throw new ArgumentException("Không tìm thấy saler với ID đã cho");
        //            attendees.Add(saler.email);
        //        }


        //        if (!string.IsNullOrEmpty(lead.email))
        //        {
        //            attendees.Add(lead.email);
        //        }


        //        CreateEventRequest createEventRequest = new()
        //        {
        //            accessToken = request.accessToken,
        //            title = request.title,
        //            calendarId = request.calendarId,
        //            colorId = request.colorId,
        //            description = request.description,
        //            location = request.location,
        //            reminders = request.reminders,
        //            sendNotifications = request.sendNotifications,
        //            startTime = request.startTime,
        //            endTime = request.endTime,
        //            isAllDay = request.isAllDay,
        //            attendees = attendees,
        //        };

        //        _logger.LogInformation($"Creating event: {request.title}");


        //        // Validate request
        //        ValidateCreateEventRequest(createEventRequest);

        //        // Tạo Google Calendar service
        //        var service = CreateGoogleCalendarService(request.accessToken);

        //        // Tạo event object
        //        var calendarEvent = new Event
        //        {
        //            Summary = request.title,
        //            Description = request.description,
        //            Location = request.location,
        //            Start = new EventDateTime
        //            {
        //                DateTime = request.isAllDay ? null : request.startTime,
        //                Date = request.isAllDay ? request.startTime.ToString("yyyy-MM-dd") : null,
        //                TimeZone = request.timeZone
        //            },
        //            End = new EventDateTime
        //            {
        //                DateTime = request.isAllDay ? null : request.startTime,
        //                Date = request.isAllDay ? request.endTime.ToString("yyyy-MM-dd") : null,
        //                TimeZone = request.timeZone
        //            },
        //            ColorId = request.colorId?.ToString(),
        //            Attendees = createEventRequest.attendees.Select(email => new EventAttendee { Email = email }).ToList()
        //        };

        //        // Thêm reminders
        //        if (request.reminders.Any())
        //        {
        //            calendarEvent.Reminders = new Event.RemindersData
        //            {
        //                UseDefault = false,
        //                Overrides = request.reminders.Select(r => new Google.Apis.Calendar.v3.Data.EventReminder
        //                {
        //                    Method = r.method,
        //                    Minutes = r.minutes
        //                }).ToList()
        //            };
        //        }

        //        // Thêm recurrence rules
        //        if (createEventRequest.recurrenceRules.Any())
        //        {
        //            calendarEvent.Recurrence = createEventRequest.recurrenceRules;
        //        }

        //        // Tạo event
        //        var insertRequest = service.Events.Insert(calendarEvent, request.calendarId);
        //        insertRequest.SendNotifications = request.sendNotifications;

        //        var createdEvent = await insertRequest.ExecuteAsync();

        //        _logger.LogInformation($"Event created successfully: {createdEvent.Id}");

        //        return new CreateEventResponse
        //        {
        //            success = true,
        //            eventId = createdEvent.Id,
        //            htmlLink = createdEvent.HtmlLink,
        //            startTime = createdEvent.Start.DateTime,
        //            endTime = createdEvent.End.DateTime,
        //            calendarId = request.calendarId,
        //            message = "Event đã được tạo và thêm vào calendar"
        //        };
        //    }
        //    catch (Google.GoogleApiException ex)
        //    {
        //        _logger.LogError(ex, "Google API error while creating event");
        //        throw new InvalidOperationException($"Lỗi Google API: {ex.Message}", ex);
        //    }
        //    catch (ArgumentException ex)
        //    {
        //        _logger.LogError(ex, "Validation error while creating event");
        //        throw;
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError(ex, "Unexpected error while creating event");
        //        throw new InvalidOperationException("Có lỗi xảy ra khi tạo event", ex);
        //    }
        //}

        public async Task<List<CalendarInfo>> GetCalendarsAsync(string accessToken)
        {
            try
            {
                _logger.LogInformation("Getting calendars list");

                if (string.IsNullOrEmpty(accessToken))
                    throw new ArgumentException("Access token không được để trống");

                var service = CreateGoogleCalendarService(accessToken);
                var calendarList = await service.CalendarList.List().ExecuteAsync();

                var calendars = calendarList.Items.Select(cal => new CalendarInfo
                {
                    id = cal.Id,
                    summary = cal.Summary,
                    description = cal.Description,
                    timeZone = cal.TimeZone,
                    accessRole = cal.AccessRole,
                    primary = cal.Primary ?? false,
                    backgroundColor = cal.BackgroundColor,
                    foregroundColor = cal.ForegroundColor
                }).ToList();

                _logger.LogInformation($"Retrieved {calendars.Count} calendars");
                return calendars;
            }
            catch (Google.GoogleApiException ex)
            {
                _logger.LogError(ex, "Google API error while getting calendars");
                throw new InvalidOperationException($"Lỗi Google API: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error while getting calendars");
                throw new InvalidOperationException("Có lỗi xảy ra khi tải danh sách calendar", ex);
            }
        }

        public async Task<List<EventInfo>> GetEventsAsync(GetEventsRequest request)
        {
            try
            {
                _logger.LogInformation($"Getting events from calendar: {request.calendarId}");

                if (string.IsNullOrEmpty(request.accessToken))
                    throw new ArgumentException("Access token không được để trống");

                if (string.IsNullOrEmpty(request.calendarId))
                    throw new ArgumentException("Calendar ID không được để trống");

                var service = CreateGoogleCalendarService(request.accessToken);

                var eventsRequest = service.Events.List(request.calendarId);
                eventsRequest.TimeMin = request.timeMin ?? DateTime.Now;
                eventsRequest.TimeMax = request.timeMax ?? DateTime.Now.AddMonths(1);
                eventsRequest.MaxResults = request.maxResults;
                eventsRequest.OrderBy = EventsResource.ListRequest.OrderByEnum.StartTime;
                eventsRequest.ShowDeleted = request.showDeleted;
                eventsRequest.SingleEvents = request.singleEvents;

                var events = await eventsRequest.ExecuteAsync();

                var eventInfos = events.Items.Select(evt => new EventInfo
                {
                    id = evt.Id,
                    summary = evt.Summary,
                    description = evt.Description,
                    location = evt.Location,
                    startTime = evt.Start.DateTime,
                    endTime = evt.End.DateTime,
                    isAllDay = evt.Start.Date != null,
                    htmlLink = evt.HtmlLink,
                    status = evt.Status,
                    attendees = evt.Attendees?.Select(a => a.Email).ToList() ?? new List<string>(),
                    creatorEmail = evt.Creator?.Email,
                    organizerEmail = evt.Organizer?.Email,
                    created = evt.Created,
                    updated = evt.Updated
                }).ToList();

                _logger.LogInformation($"Retrieved {eventInfos.Count} events");
                return eventInfos;
            }
            catch (Google.GoogleApiException ex)
            {
                _logger.LogError(ex, "Google API error while getting events");
                throw new InvalidOperationException($"Lỗi Google API: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error while getting events");
                throw new InvalidOperationException("Có lỗi xảy ra khi tải danh sách events", ex);
            }
        }

        public async Task<CreateEventResponse> UpdateEventAsync(string calendarId, string eventId, UpdateEventRequest request)
        {
            try
            {
                _logger.LogInformation($"Updating event: {eventId}");

                // Validate basic required fields
                ValidateUpdateEventRequest(request);

                // Kiểm tra xem có trường nào để update không
                if (!HasFieldsToUpdate(request))
                {
                    return new CreateEventResponse
                    {
                        success = false,
                        eventId = eventId,
                        calendarId = calendarId,
                        message = "Không có thông tin nào để cập nhật. Vui lòng cung cấp ít nhất một trường để thay đổi."
                    };
                }

                var service = CreateGoogleCalendarService(request.accessToken);

                // Lấy event hiện tại
                var existingEvent = await service.Events.Get(calendarId, eventId).ExecuteAsync();

                // Track các thay đổi
                var changes = new List<string>();

                // Partial update - chỉ update các trường được cung cấp
                if (!string.IsNullOrEmpty(request.title))
                {
                    existingEvent.Summary = request.title;
                    changes.Add("tiêu đề");
                }

                if (request.description != null) // Cho phép set empty string
                {
                    existingEvent.Description = request.description;
                    changes.Add("mô tả");
                }

                if (request.location != null) // Cho phép set empty string
                {
                    existingEvent.Location = request.location;
                    changes.Add("địa điểm");
                }

                // Update thời gian (cần cả startTime và endTime hoặc không có gì)
                if (request.startTime.HasValue || request.endTime.HasValue)
                {
                    if (!request.startTime.HasValue || !request.endTime.HasValue)
                    {
                        throw new ArgumentException("Khi cập nhật thời gian, cần cung cấp cả startTime và endTime");
                    }

                    if (request.startTime.Value >= request.endTime.Value)
                    {
                        throw new ArgumentException("Thời gian bắt đầu phải nhỏ hơn thời gian kết thúc");
                    }

                    var isAllDay = request.isAllDay ?? false;
                    var timeZone = request.timeZone ?? "Asia/Ho_Chi_Minh";

                    existingEvent.Start = new EventDateTime
                    {
                        DateTime = isAllDay ? null : request.startTime.Value,
                        Date = isAllDay ? request.startTime.Value.ToString("yyyy-MM-dd") : null,
                        TimeZone = timeZone
                    };

                    existingEvent.End = new EventDateTime
                    {
                        DateTime = isAllDay ? null : request.endTime.Value,
                        Date = isAllDay ? request.endTime.Value.ToString("yyyy-MM-dd") : null,
                        TimeZone = timeZone
                    };

                    changes.Add("thời gian");
                }

                // Update attendees
                if (request.attendees != null)
                {
                    existingEvent.Attendees = request.attendees.Select(email => new EventAttendee
                    {
                        Email = email,
                        ResponseStatus = "needsAction"
                    }).ToList();
                    changes.Add("danh sách tham gia");
                }

                // Update color
                if (request.colorId.HasValue)
                {
                    if (request.colorId.Value < 1 || request.colorId.Value > 11)
                    {
                        throw new ArgumentException("ColorId phải từ 1 đến 11");
                    }
                    existingEvent.ColorId = request.colorId.Value.ToString();
                    changes.Add("màu sắc");
                }

                // Update reminders
                if (request.reminders != null)
                {
                    existingEvent.Reminders = new Event.RemindersData
                    {
                        UseDefault = false,
                        Overrides = request.reminders.Select(r => new Google.Apis.Calendar.v3.Data.EventReminder
                        {
                            Method = r.method,
                            Minutes = r.minutes
                        }).ToList()
                    };
                    changes.Add("nhắc nhở");
                }

                // Update recurrence
                if (request.recurrenceRules != null)
                {
                    existingEvent.Recurrence = request.recurrenceRules.Any() ? request.recurrenceRules : null;
                    changes.Add("lặp lại");
                }

                // Thực hiện update
                var updateRequest = service.Events.Update(existingEvent, calendarId, eventId);
                updateRequest.SendNotifications = request.sendNotifications ?? true;

                var updatedEvent = await updateRequest.ExecuteAsync();

                _logger.LogInformation($"Event updated successfully: {updatedEvent.Id}. Changes: {string.Join(", ", changes)}");

                return new CreateEventResponse
                {
                    success = true,
                    eventId = updatedEvent.Id,
                    htmlLink = updatedEvent.HtmlLink,
                    startTime = updatedEvent.Start.DateTime ?? DateTime.Parse(updatedEvent.Start.Date),
                    endTime = updatedEvent.End.DateTime ?? DateTime.Parse(updatedEvent.End.Date),
                    calendarId = calendarId,
                    message = $"Event đã được cập nhật thành công. Các thay đổi: {string.Join(", ", changes)}"
                };
            }
            catch (Google.GoogleApiException ex)
            {
                _logger.LogError(ex, "Google API error while updating event");
                throw new InvalidOperationException($"Lỗi Google API: {ex.Message}", ex);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Validation error while updating event");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error while updating event");
                throw new InvalidOperationException("Có lỗi xảy ra khi cập nhật event", ex);
            }
        }

        public async Task<DeleteEventResponse> DeleteEventAsync(string calendarId, string eventId, string accessToken)
        {
            try
            {
                _logger.LogInformation($"Deleting event: {eventId} from calendar: {calendarId}");

                // ✅ Validate parameters
                ValidateDeleteEventRequest(calendarId, eventId, accessToken);

                var service = CreateGoogleCalendarService(accessToken);

                // ✅ Check if event exists first (optional but good practice)
                try
                {
                    var existingEvent = await service.Events.Get(calendarId, eventId).ExecuteAsync();
                    _logger.LogInformation($"Found event to delete: {existingEvent.Summary} ({existingEvent.Id})");
                }
                catch (Google.GoogleApiException ex) when (ex.HttpStatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    _logger.LogWarning($"Event {eventId} not found in calendar {calendarId}. It may have been already deleted.");
                    return new DeleteEventResponse
                    {
                        success = true, // Consider as success since event doesn't exist
                        eventId = eventId,
                        calendarId = calendarId,
                        message = "Event not found (may have been already deleted)"
                    };
                }

                // ✅ Delete the event
                var deleteRequest = service.Events.Delete(calendarId, eventId);
                deleteRequest.SendNotifications = true; // Notify attendees about cancellation

                await deleteRequest.ExecuteAsync();

                _logger.LogInformation($"Event deleted successfully: {eventId}");

                return new DeleteEventResponse
                {
                    success = true,
                    eventId = eventId,
                    calendarId = calendarId,
                    message = "Event deleted successfully"
                };
            }
            catch (Google.GoogleApiException ex)
            {
                _logger.LogError(ex, $"Google API error while deleting event {eventId}");

                // ✅ Handle specific Google API errors
                var errorMessage = ex.HttpStatusCode switch
                {
                    System.Net.HttpStatusCode.NotFound => "Event không tồn tại hoặc đã bị xóa",
                    System.Net.HttpStatusCode.Forbidden => "Không có quyền xóa event này",
                    System.Net.HttpStatusCode.Unauthorized => "Token không hợp lệ hoặc đã hết hạn",
                    _ => $"Lỗi Google API: {ex.Message}"
                };

                return new DeleteEventResponse
                {
                    success = false,
                    eventId = eventId,
                    calendarId = calendarId,
                    message = errorMessage
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error while deleting event {eventId}");
                return new DeleteEventResponse
                {
                    success = false,
                    eventId = eventId,
                    calendarId = calendarId,
                    message = "Có lỗi xảy ra khi xóa event"
                };
            }
        }

        private void ValidateDeleteEventRequest(string calendarId, string eventId, string accessToken)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token is required");
            if (string.IsNullOrEmpty(calendarId))
                throw new ArgumentException("Calendar ID is required");
            if (string.IsNullOrEmpty(eventId))
                throw new ArgumentException("Event ID is required");
        }


        public async Task<EventInfo> GetEventByIdAsync(string accessToken, string calendarId, string eventId)
        {
            try
            {
                _logger.LogInformation($"Getting event: {eventId}");

                if (string.IsNullOrEmpty(accessToken))
                    throw new ArgumentException("Access token không được để trống");

                if (string.IsNullOrEmpty(calendarId))
                    throw new ArgumentException("Calendar ID không được để trống");

                if (string.IsNullOrEmpty(eventId))
                    throw new ArgumentException("Event ID không được để trống");

                var service = CreateGoogleCalendarService(accessToken);
                var evt = await service.Events.Get(calendarId, eventId).ExecuteAsync();

                var eventInfo = new EventInfo
                {
                    id = evt.Id,
                    summary = evt.Summary,
                    description = evt.Description,
                    location = evt.Location,
                    startTime = evt.Start.DateTime,
                    endTime = evt.End.DateTime,
                    isAllDay = evt.Start.Date != null,
                    htmlLink = evt.HtmlLink,
                    status = evt.Status,
                    attendees = evt.Attendees?.Select(a => a.Email).ToList() ?? new List<string>(),
                    creatorEmail = evt.Creator?.Email,
                    organizerEmail = evt.Organizer?.Email,
                    created = evt.Created,
                    updated = evt.Updated
                };

                _logger.LogInformation($"Retrieved event: {eventInfo.summary}");
                return eventInfo;
            }
            catch (Google.GoogleApiException ex)
            {
                _logger.LogError(ex, "Google API error while getting event");
                throw new InvalidOperationException($"Lỗi Google API: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error while getting event");
                throw new InvalidOperationException("Có lỗi xảy ra khi tải event", ex);
            }
        }


        public async Task<string> CreateUserCalendarAsync(string accessToken, string userId)
        {
            try
            {
                var calendarName = "RevoLand's User Calendar";

                var calendarData = new
                {
                    summary = calendarName,
                    description = $"RevoLand's User Calendar for user {userId}",
                    timeZone = "UTC", // You can make this configurable
                    selected = true, // Make it visible in user's calendar list
                    defaultReminders = new[]
                    {
                    new { method = "popup", minutes = 15 }
                }
                };

                using var httpClient = _httpClientFactory.CreateClient("GoogleCalendarClient");

                var json = JsonSerializer.Serialize(calendarData, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                // ✅ Set authorization header
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                var response = await httpClient.PostAsync($"{GoogleCalendarApiBaseUrl}/calendars", content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var calendarResponse = JsonSerializer.Deserialize<JsonElement>(responseContent);

                    var calendarId = calendarResponse.GetProperty("id").GetString();
                    _logger.LogInformation($"✅ Created calendar for user {userId}: {calendarId}");

                    return calendarId ?? throw new Exception("Calendar ID is null in response");
                }
                else
                {
                    var error = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"❌ Failed to create calendar for user {userId}: {response.StatusCode} - {error}");
                    throw new HttpRequestException($"Failed to create Google Calendar: {response.StatusCode} - {error}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"❌ Error creating calendar for user {userId}");
                throw;
            }
        }

        public async Task<bool> CalendarExistsAsync(string accessToken, string calendarId)
        {
            try
            {
                if (string.IsNullOrEmpty(calendarId))
                    return false;

                using var httpClient = _httpClientFactory.CreateClient("GoogleCalendarClient");
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                // ✅ URL encode calendar ID to handle special characters
                var encodedCalendarId = Uri.EscapeDataString(calendarId);
                var response = await httpClient.GetAsync($"{GoogleCalendarApiBaseUrl}/calendars/{encodedCalendarId}");

                var exists = response.IsSuccessStatusCode;
                _logger.LogDebug($"Calendar {calendarId} exists: {exists}");

                return exists;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, $" Error checking calendar existence: {calendarId}");
                return false;
            }
        }

        public async Task<string> GetOrCreateUserCalendarAsync(string accessToken, string userId, string calendarId)
        {
            try
            {
                // ✅ First, try to get existing calendars to see if we already created one
                var existingCalendars = await GetUserCalendarsAsync(accessToken);

                // Look for our created calendar
                //    var userCalendarPattern = new[]
                //    {
                //    $"User {userId} Calendar",
                //    $"RevoLand's User Calendar"
                //}.Where(p => !string.IsNullOrEmpty(p));

                //foreach (var calendar in existingCalendars)
                //{
                //    if (userCalendarPattern.Any(pattern =>
                //        calendar.summary?.Contains(pattern, StringComparison.OrdinalIgnoreCase) == true))
                //    {
                //        _logger.LogInformation($"✅ Found existing calendar for user {userId}: {calendar.id}");
                //        return calendar.id;
                //    }
                //}
                foreach (var calendar in existingCalendars)
                {
                    if (calendar.id == calendarId) return calendar.id;
                }

                // ✅ If no existing calendar found, create new one
                _logger.LogInformation($"📅 No existing calendar found for user {userId}, creating new one");
                return await CreateUserCalendarAsync(accessToken, userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"❌ Error in GetOrCreateUserCalendarAsync for user {userId}");
                throw;
            }
        }

        public async Task<List<CalendarInfo>> GetUserCalendarsAsync(string accessToken)
        {
            try
            {
                using var httpClient = _httpClientFactory.CreateClient("GoogleCalendarClient");
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                var response = await httpClient.GetAsync($"{GoogleCalendarApiBaseUrl}/users/me/calendarList");

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var calendarList = JsonSerializer.Deserialize<JsonElement>(responseContent);

                    var calendars = new List<CalendarInfo>();

                    if (calendarList.TryGetProperty("items", out var items))
                    {
                        foreach (var calendar in items.EnumerateArray())
                        {
                            var calendarInfo = new CalendarInfo
                            {
                                id = calendar.GetProperty("id").GetString() ?? "",
                                summary = calendar.TryGetProperty("summary", out var summary) ? summary.GetString() : "",
                                description = calendar.TryGetProperty("description", out var desc) ? desc.GetString() : "",
                                primary = calendar.TryGetProperty("primary", out var primary) && primary.GetBoolean(),
                                accessRole = calendar.TryGetProperty("accessRole", out var role) ? role.GetString() : ""
                            };

                            calendars.Add(calendarInfo);
                        }
                    }

                    _logger.LogDebug($"📋 Retrieved {calendars.Count} calendars for user");
                    return calendars;
                }
                else
                {
                    var error = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"❌ Failed to get calendars: {response.StatusCode} - {error}");
                    throw new HttpRequestException($"Failed to get Google Calendars: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error getting user calendars");
                throw;
            }
        }

        public async Task<bool> DeleteCalendarAsync(string accessToken, string calendarId)
        {
            try
            {
                if (string.IsNullOrEmpty(calendarId) || calendarId == "primary")
                {
                    _logger.LogWarning("⚠️ Cannot delete primary calendar or empty calendar ID");
                    return false;
                }

                using var httpClient = _httpClientFactory.CreateClient("GoogleCalendarClient");
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                var encodedCalendarId = Uri.EscapeDataString(calendarId);
                var response = await httpClient.DeleteAsync($"{GoogleCalendarApiBaseUrl}/calendars/{encodedCalendarId}");

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation($"✅ Deleted calendar: {calendarId}");
                    return true;
                }
                else
                {
                    var error = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"❌ Failed to delete calendar {calendarId}: {response.StatusCode} - {error}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"❌ Error deleting calendar: {calendarId}");
                return false;
            }
        }




        #region Private Methods


        private Google.Apis.Calendar.v3.CalendarService CreateGoogleCalendarService(string accessToken)
        {
            var credential = Google.Apis.Auth.OAuth2.GoogleCredential.FromAccessToken(accessToken);

            return new Google.Apis.Calendar.v3.CalendarService(new BaseClientService.Initializer()
            {
                HttpClientInitializer = credential,
                ApplicationName = "Your App Name",
            });
        }

        private void ValidateCreateCalendarRequest(CreateCalendarRequest request)
        {
            if (string.IsNullOrWhiteSpace(request.accessToken))
                throw new ArgumentException("Access token không được để trống");

            if (string.IsNullOrWhiteSpace(request.summary))
                throw new ArgumentException("Tên calendar không được để trống");

            if (string.IsNullOrWhiteSpace(request.timeZone))
                throw new ArgumentException("Time zone không được để trống");

            // Validate share rules
            if (request.shareRules?.Any() == true)
            {
                var validRoles = new[] { "owner", "reader", "writer", "freeBusyReader" };
                var validTypes = new[] { "user", "group", "domain", "default" };

                foreach (var rule in request.shareRules)
                {
                    if (string.IsNullOrWhiteSpace(rule.email))
                        throw new ArgumentException("Email trong share rule không được để trống");

                    if (!validRoles.Contains(rule.role))
                        throw new ArgumentException($"Role '{rule.role}' không hợp lệ. Các role hợp lệ: {string.Join(", ", validRoles)}");

                    if (!validTypes.Contains(rule.type))
                        throw new ArgumentException($"Type '{rule.type}' không hợp lệ. Các type hợp lệ: {string.Join(", ", validTypes)}");
                }
            }
        }

        private async Task<List<string>> ShareCalendarAsync(Google.Apis.Calendar.v3.CalendarService service, string calendarId, List<CalendarShareRule> shareRules)
        {
            var sharedWith = new List<string>();

            try
            {
                foreach (var rule in shareRules)
                {
                    var aclRule = new AclRule
                    {
                        Role = rule.role,
                        Scope = new AclRule.ScopeData
                        {
                            Type = rule.type,
                            Value = rule.email
                        }
                    };

                    var insertAclRequest = service.Acl.Insert(aclRule, calendarId);
                    await insertAclRequest.ExecuteAsync();

                    sharedWith.Add(rule.email);
                    _logger.LogInformation($"Calendar {calendarId} shared with {rule.email} as {rule.role}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, $"Error sharing calendar {calendarId}");
                // Không throw exception, chỉ log warning vì calendar đã được tạo thành công
            }

            return sharedWith;
        }

        private async Task MakeCalendarPublicAsync(Google.Apis.Calendar.v3.CalendarService service, string calendarId)
        {
            try
            {
                var publicRule = new AclRule
                {
                    Role = "reader",
                    Scope = new AclRule.ScopeData
                    {
                        Type = "default"
                    }
                };

                var insertAclRequest = service.Acl.Insert(publicRule, calendarId);
                await insertAclRequest.ExecuteAsync();

                _logger.LogInformation($"Calendar {calendarId} made public");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, $"Error making calendar {calendarId} public");
                // Không throw exception, chỉ log warning
            }
        }


        private void ValidateCreateEventRequest(CreateEventRequest request)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            if (string.IsNullOrEmpty(request.title))
                throw new ArgumentException("Tiêu đề event không được để trống");

            if (string.IsNullOrEmpty(request.calendarId))
                throw new ArgumentException("Calendar ID không được để trống");

            if (string.IsNullOrEmpty(request.accessToken))
                throw new ArgumentException("Access token không được để trống");

            if (request.startTime >= request.endTime)
                throw new ArgumentException("Thời gian kết thúc phải sau thời gian bắt đầu");

            if (request.startTime < DateTime.Now.AddMinutes(-5))
                throw new ArgumentException("Thời gian bắt đầu không thể ở quá khứ");

            // Validate attendees emails
            foreach (var email in request.attendees)
            {
                if (!IsValidEmail(email))
                    throw new ArgumentException($"Email không hợp lệ: {email}");
            }

            // Validate color ID
            if (request.colorId.HasValue && (request.colorId < 1 || request.colorId > 11))
                throw new ArgumentException("Color ID phải từ 1 đến 11");

            // Validate reminders
            foreach (var reminder in request.reminders)
            {
                if (reminder.method != "email" && reminder.method != "popup")
                    throw new ArgumentException("Reminder method chỉ có thể là 'email' hoặc 'popup'");
            }
        }

        private void ValidateUpdateEventRequest(UpdateEventRequest request)
        {

            if (string.IsNullOrWhiteSpace(request.accessToken))
                throw new ArgumentException("Access token không được để trống");
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private bool HasFieldsToUpdate(UpdateEventRequest request)
        {
            return !string.IsNullOrEmpty(request.title) ||
                   request.description != null ||
                   request.location != null ||
                   request.startTime.HasValue ||
                   request.endTime.HasValue ||
                   request.timeZone != null ||
                   request.isAllDay.HasValue ||
                   request.attendees != null ||
                   request.colorId.HasValue ||
                   request.sendNotifications.HasValue ||
                   request.reminders != null ||
                   request.recurrenceRules != null;
        }

        #endregion
    }
}
