﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.Models
{
    public class PriceDetail
    {
        [BsonElement("salePrice")]
        public double? salePrice { get; set; }

        [BsonElement("rentalPrice")]
        public double? rentalPrice { get; set; }

        [BsonElement("pricePerSquareMeter")]
        public double? pricePerSquareMeter { get; set; }

        [BsonElement("currency")]
        public string currency { get; set; }

        [BsonElement("depositAmount")]
        public double? depositAmount { get; set; }

        [BsonElement("maintenanceFee")]
        public double? maintenanceFee { get; set; }

        [BsonElement("paymentMethods")]
        public List<string> paymentMethods { get; set; }
    }

}
