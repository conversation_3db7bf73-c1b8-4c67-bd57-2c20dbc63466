﻿using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace BusinessObjects.Validations
{
    public class PhoneVNAttribute : ValidationAttribute
    {
        protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
        {
            if (value == null || string.IsNullOrWhiteSpace(value.ToString()))
            {
                return ValidationResult.Success;
            }

            string phone = value.ToString()!.Trim().Replace(" ", "").Replace("-", "");
            string pattern = @"^(0|\+84)(3[2-9]|5[6|8|9]|7[0|6-9]|8[1-5]|9[0-9])[0-9]{7}$";

            if (!Regex.IsMatch(phone, pattern))
            {
                return new ValidationResult("Invalid phone number (must be a valid Vietnam phone number format)");
            }

            return ValidationResult.Success;
        }
    }
}
