﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace BusinessObjects.Models
{
    public class Appointment
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string id { get; set; }
        public string leadId { get; set; }
        public string salerId { get; set; }
        public string userId { get; set; }
        public List<AppointmentMessage> messages { get; set; } = new List<AppointmentMessage>();
        public string propertyId { get; set; }

        [BsonRepresentation(BsonType.String)]
        public AppointmentStatus status { get; set; }
        public DateTime date { get; set; }

        // ✅ Google Calendar Integration
        public string? userGoogleCalendarEventId { get; set; }
        public string? salerGoogleCalendarEventId { get; set; }

        [BsonElement("calendarSyncStatus")]
        [BsonRepresentation(BsonType.String)]
        public CalendarSyncStatus calendarSyncStatus { get; set; } = CalendarSyncStatus.Pending;

        // ✅ Cancellation Information
        public CancellationInfo? cancellationInfo { get; set; }

        // ✅ Audit Trail
        public DateTime createdAt { get; set; } = DateTime.UtcNow;
        public DateTime updatedAt { get; set; } = DateTime.UtcNow;
        public string? lastModifiedBy { get; set; }

        // ✅ Status History
        public List<AppointmentStatusHistory> statusHistory { get; set; } = new List<AppointmentStatusHistory>();
    }

    // ✅ CancellationInfo class
    public class CancellationInfo
    {
        public string reason { get; set; }
        public DateTime cancelledAt { get; set; } = DateTime.UtcNow;
        public string cancelledBy { get; set; } // UserId của người cancel

        [BsonRepresentation(BsonType.String)]
        public CancellationType type { get; set; }

        // ✅ Calendar event deletion tracking
        public CalendarDeletionInfo calendarDeletion { get; set; } = new CalendarDeletionInfo();

        // ✅ Additional metadata
        public string? notes { get; set; }
        public bool isAutomatic { get; set; } = false; // System auto-cancel
        public string? relatedTicketId { get; set; } // Support ticket reference
    }

    // ✅ CalendarDeletionInfo class - Track calendar event deletion
    public class CalendarDeletionInfo
    {
        public bool isDeleted { get; set; } = false;
        public DateTime? deletedAt { get; set; }
        public bool userEventDeleted { get; set; } = false;
        public bool salerEventDeleted { get; set; } = false;
        public string? deletionStatus { get; set; } // "Success", "Failed", "Partial"
        public string? failureReason { get; set; }
        public int retryCount { get; set; } = 0;
        public DateTime? lastRetryAt { get; set; }
    }

    public enum AppointmentStatus
    {
        Open,//pending
        Confirmed,// assigned to a saler
        Completed,
        Cancelled,
        Rescheduled
    }

    public enum CancellationType
    {
        UserCancelled,
        SalerCancelled,
        SystemCancelled,
        AdminCancelled,
        NoResponse,
        PropertyUnavailable,
        TechnicalIssue,
        WeatherCondition,
        Emergency
    }

    // ✅ Calendar Sync Status Enum
    public enum CalendarSyncStatus
    {
        // ✅ Initial states
        NotSynced = 0,           // Chưa sync với calendar nào
        Pending = 1,             // Đang trong queue để sync



        // ✅ Partial sync states (khi có nhiều participants)
        UserSynced = 2,          // Chỉ sync với user calendar
        SalerSynced = 3,         // Chỉ sync với saler calendar
        PartiallySynced = 4,     // Sync được một số, fail một số

        // ✅ Complete sync states
        FullySynced = 5,         // Sync thành công tất cả calendars

        // ✅ Error states
        Failed = 6,              // Sync fail hoàn toàn
        SyncError = 7,           // Có lỗi trong quá trình sync

        // ✅ Special states
        ManuallyCreated = 8,     // User tự tạo calendar event
        Disabled = 9,             // Tắt calendar sync cho appointment này

        Deleted = 10,          // Đã xóa khỏi calendar
        PartiallyDeleted = 11, // Chỉ xóa một phần (ví dụ: chỉ xóa của user hoặc saler)
    }

    public class AppointmentStatusHistory
    {
        [BsonRepresentation(BsonType.String)]
        public AppointmentStatus previousStatus { get; set; }
        [BsonRepresentation(BsonType.String)]
        public AppointmentStatus newStatus { get; set; }
        public DateTime changedAt { get; set; } = DateTime.UtcNow;
        public string changedBy { get; set; }
        public string? reason { get; set; }
        public string? notes { get; set; }
    }
}
