﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="EPPlus" Version="7.7.0" />
    <PackageReference Include="Google.Apis.Oauth2.v2" Version="1.68.0.1869" />
    <PackageReference Include="Google.Cloud.Storage.V1" Version="4.11.0" />
    <PackageReference Include="NPOI" Version="2.7.3" />
    <PackageReference Include="Twilio" Version="7.10.0" />
    <PackageReference Include="Hangfire.AspNetCore" Version="1.8.10" />
    <PackageReference Include="Hangfire.Core" Version="1.8.10" />
    <PackageReference Include="Hangfire.MemoryStorage" Version="1.8.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Repositories\Repositories.csproj" />
  </ItemGroup>

</Project>
