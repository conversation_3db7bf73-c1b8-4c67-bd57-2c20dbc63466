﻿using BusinessObjects.Models;
using BusinessObjects.Validations;
using System.ComponentModel.DataAnnotations;

namespace BusinessObjects.DTOs.UserDTOs
{
    public class UserRegister
    {
        public string? userName { get; set; }

        public string? fullName { get; set; }

        [Required(ErrorMessage = "Email or Phone number is required")]
        [EmailOrPhoneVN(ErrorMessage = "Must be a valid email address or Vietnamese phone number")]
        public string keyRegister { get; set; }

        [Required(ErrorMessage = "Password is required")]
        [RegularExpression(@"^(?=.*[A-Z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,}$",
            ErrorMessage = "Password must have at least 8 characters, one capital letter and one digit")]
        public string password { get; set; }

        public string? about { get; set; }
        public DateTime? birthDate { get; set; }

        [Required(ErrorMessage = "User role is required")]
        public UserRole role { get; set; }
        public string? avatarUrl { get; set; }

    }
}
