﻿using System.Text.Json.Serialization;

namespace BusinessObjects.DTOs.FacebookDTOs
{
    public class MessengerWebhookPayload
    {
        [Json<PERSON>ropertyName("sender")]
        public MessengerSender sender { get; set; }

        [JsonPropertyName("recipient")]
        public MessengerRecipient recipient { get; set; }

        [JsonPropertyName("timestamp")]
        public long timestamp { get; set; }

        [JsonPropertyName("message")]
        public MessengerMessage message { get; set; }
    }

    public class MessengerSender
    {
        [JsonPropertyName("id")]
        public string id { get; set; }
    }

    public class MessengerRecipient
    {
        [JsonPropertyName("id")]
        public string id { get; set; }
    }

    public class MessengerMessage
    {
        /// <summary>
        /// Message ID do Facebook cấp
        /// </summary>
        [JsonPropertyName("mid")]
        public string mid { get; set; }

        /// <summary>
        /// Nội dung text người dùng gửi
        /// </summary>
        [Json<PERSON>ropertyName("text")]
        public string text { get; set; }

        [JsonPropertyName("is_echo")]
        public bool? isEcho { get; set; }

        [Json<PERSON>ropertyName("attachments")]
        public List<MessengerAttachment>? attachments { get; set; }

    }
    public class MessengerAttachmentPayload
    {
        [JsonPropertyName("url")]      // Đường dẫn tạm thời
        public string url { get; set; }
        // Có thể có "attachment_id" nếu file được upload trước
        [JsonPropertyName("attachment_id")]
        public string? attachmentId { get; set; }
    }


    public class MessengerAttachment
    {
        [JsonPropertyName("type")]   // "image", "file", "audio", "video", "fallback"
        public string type { get; set; }
        [JsonPropertyName("payload")]
        public MessengerAttachmentPayload payload { get; set; }

    }

}
