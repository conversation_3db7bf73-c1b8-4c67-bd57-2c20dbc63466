﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
	<GenerateDocumentationFile>true</GenerateDocumentationFile>
	<NoWarn>$(NoWarn);1591</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FirebaseAdmin" Version="3.1.0" />
    <PackageReference Include="Google.Apis.Auth" Version="1.69.0" />
    <PackageReference Include="Hangfire.AspNetCore" Version="1.8.10" />
    <PackageReference Include="Hangfire.Core" Version="1.8.18" />
    <PackageReference Include="Hangfire.MemoryStorage" Version="1.8.0" />
    <PackageReference Include="Hangfire.Mongo" Version="1.11.5" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Google" Version="8.0.0" />
    <PackageReference Include="NPOI" Version="2.7.3" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="8.1.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Services\Services.csproj" />
  </ItemGroup>

</Project>
