﻿using BusinessObjects.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.DTOs.ReminderDTO
{
    public class ReminderDTO
    {
        public string userId {  get; set; }
        public string title { get; set; }
        public string message { get; set; }
        public DateTime scheduledTime { get; set; }
        public string email { get; set; }
        public string phoneNumber { get; set; }



        public static class ReminderMapper
        {
            public static ReminderDTO ToDTO(Reminder reminder)
            {
                return new ReminderDTO
                {
                    title = reminder.title,
                    message = reminder.message,
                    scheduledTime = reminder.scheduledTime,
                    email = reminder.email,
                    phoneNumber = reminder.phoneNumber
                };
            }
            public static Reminder ToModel(ReminderDTO reminderDTO)
            {
                return new Reminder
                {
                    title = reminderDTO.title,
                    message = reminderDTO.message,
                    scheduledTime = reminderDTO.scheduledTime,
                    email = reminderDTO.email,
                    phoneNumber = reminderDTO.phoneNumber,
                };
            }
        }
    }
}
