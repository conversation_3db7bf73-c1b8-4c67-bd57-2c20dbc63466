﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.CompanyDTOs;
using BusinessObjects.DTOs.UserDTOs;
using BusinessObjects.Settings;
using Repositories.Tools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Services.Tools
{
    public class TokenTools
    {
        private readonly TokenTool _token;
        public TokenTools(TokenTool token)
        {
            _token = token;
        }
        public async Task<TokenSetting> GenerateToken(UserDTO user)
        {
            return await _token.GenerateToken(user);
        }

		public async Task<TokenSetting> GenerateCompanyToken(CompanyDTO company)
		{
			return await _token.GenerateCompanyToken(company);
		}

		public DateTime ConvertUnixTimeToDateTime(long utcExpireDate)
        {
            var dateTimeInterval = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);
            dateTimeInterval.AddSeconds(utcExpireDate).ToUniversalTime();

            return dateTimeInterval;
        }
    }
}
