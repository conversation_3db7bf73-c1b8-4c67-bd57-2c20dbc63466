﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.AppoinmentDTOs;
using BusinessObjects.Models;
using BusinessObjects.Validations;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using Repositories.Interfaces;
using Services.Interfaces;
using Services.Tools;
using static BusinessObjects.DTOs.ResultDTOs.ResultDTO;
using TaskStatus = BusinessObjects.DTOs.ResultDTOs.ResultDTO.TaskStatus;

namespace Services.Implements
{
    public class AppointmentService : IAppointmentService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICurrentUserService _currentUserService;
        private readonly IEmailService _emailService;
        private readonly INotificationService _notificationService;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly ILogger<AppointmentService> _logger;

        public AppointmentService(IUnitOfWork unitOfWork, ICurrentUserService currentUserService, IEmailService emailService, INotificationService notificationService, IServiceScopeFactory serviceScopeFactory, ILogger<AppointmentService> logger)
        {
            _unitOfWork = unitOfWork;
            _currentUserService = currentUserService;
            _emailService = emailService;
            _notificationService = notificationService;
            _serviceScopeFactory = serviceScopeFactory;
            _logger = logger;

        }

        public async Task<List<AppointmentResponse>> GetAppointmentsAsync()
        {
            var appointments = await _unitOfWork.Appointments.GetAllAsync();
            var result = await Task.WhenAll(appointments.Select(ToAppointmentResponse));
            return result.ToList();
        }


        public async Task DeleteAppointmentAsync(string id)
        {
            var (userId, role) = _currentUserService.GetCurrentUser();
            var appointment = await _unitOfWork.Appointments.GetByIdAsync(id) ?? throw new Exception("Appointment not found");
            if (role == "User")
            {
                var lead = await _unitOfWork.Leads.GetLeadByUserIdAsync(userId) ?? throw new Exception("Lead not found");
                if (appointment.leadId != lead.id)
                {
                    throw new Exception("Bạn không có quyền thực hiện hành động này");
                }
            }
            else if (role != "Admin")
            {
                throw new BadHttpRequestException("Vai trò không hợp lệ");
            }
            await _unitOfWork.Appointments.DeleteAsync(id);
        }

        public async Task<AppointmentResponse> AddAppointmentAsync(AppointmentCreateRequest request)
        {
            var (userId, role) = _currentUserService.GetCurrentUser();

            ValidateAppointmentTime(request.date);

            //STEP 1: Validate basic requirements
            var property = await _unitOfWork.Properties.GetByIdAsync(request.propertyId)
                ?? throw new Exception("Property not found");

            //STEP 2: Check for time conflicts FIRST (before other validations)
            var hasTimeConflict = await _unitOfWork.Appointments.HasTimeConflictAsync(userId, request.date, 60); // 1 hour buffer
            if (hasTimeConflict)
            {
                var conflictingAppointments = await _unitOfWork.Appointments.GetConflictingAppointmentsAsync(userId, request.date, 60);

                var conflictDetails = conflictingAppointments.Select(c =>
                    $"• {c.propertyTitle} at {c.propertyAddress} on {c.date:yyyy-MM-dd HH:mm}").ToList();

                var errorMessage = $"You already have appointment(s) scheduled around this time:\n{string.Join("\n", conflictDetails)}";

                throw new InvalidOperationException(errorMessage);
            }


            //STEP 3: Handle Lead logic
            var lead = await _unitOfWork.Leads.GetLeadByUserIdAsync(userId);

            if (lead != null)
            {
                //Check if already booked THIS specific property
                var existing = await _unitOfWork.Appointments.GetByLeadIdAndPropertyIdAsync(lead.id, request.propertyId);
                if (existing != null)
                    throw new Exception("You already booked this property");
            }

            if (lead == null)
            {
                var user = await _unitOfWork.Users.GetByIdAsync(userId)
                    ?? throw new Exception("Không tìm thấy người dùng");

                lead = new Lead
                {
                    userId = user.id,
                    name = user.fullName,
                    phone = user.phoneNumber,
                    email = user.email,
                    source = LeadSource.Appointment,
                    score = LeadScore.Hot,
                    assignedTo = new List<AssignedTo>()
                };
                lead = await _unitOfWork.Leads.AddAsync(lead);
            }
            else
            {
                lead.score = LeadScore.Hot;
                lead.updatedAt = DateTime.UtcNow;
                await _unitOfWork.Leads.UpdateAsync(lead.id, lead);
            }

            //STEP 4: Create appointment
            var appointment = new Appointment
            {
                leadId = lead.id,
                salerId = property.salerId,
                userId = userId,
                propertyId = request.propertyId,
                date = request.date,
                status = string.IsNullOrEmpty(property.salerId) ? AppointmentStatus.Open : AppointmentStatus.Confirmed,
                messages = new List<AppointmentMessage>()
            };

            //STEP 5: Handle seller notifications
            if (property.salerId != null)
            {
                var foundSeller = await _unitOfWork.Users.GetByIdAsync(property.salerId)
                    ?? throw new Exception("Saler not found");

                if (foundSeller.role == UserRole.Saler)
                {
                    var emailContent = EmailTemplate.GetAppointmentEmailContent(
                        foundSeller.fullName, lead.name, property.location.address, request.date);

                    _emailService.SendEmailAsync(foundSeller.email, "New Appointment Assigned", emailContent);

                    var notificationMessage = $"You have a new appointment with {lead.name} at {property.location.address} on {request.date:yyyy-MM-dd HH:mm}.";
                    await _notificationService.SendNotificationToSellerAsync(foundSeller.id, notificationMessage);
                }
            }

            //STEP 6:Save appointment
            appointment = await _unitOfWork.Appointments.AddAsync(appointment);
            await _unitOfWork.SaveAsync();

            // ✅ Chạy Google Calendar creation cho BOTH user và saler trong background
            _ = Task.Run(async () =>
            {
                try
                {
                    _logger.LogInformation($"Starting background calendar event creation for appointment {appointment.id}");

                    // ✅ Tạo scope mới từ IServiceScopeFactory
                    using var scope = _serviceScopeFactory.CreateScope();
                    var scopedUnitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();
                    var scopedCalendarService = scope.ServiceProvider.GetRequiredService<ICalendarIntegrationService>();

                    var appointmentToUpdate = await scopedUnitOfWork.Appointments.GetByIdAsync(appointment.id);
                    if (appointmentToUpdate == null)
                    {
                        _logger.LogError($"Appointment {appointment.id} not found during background task");
                        return;
                    }

                    // ✅ 1. Tạo calendar event cho USER (người book appointment)
                    string? userEventId = null;
                    try
                    {
                        //if appointment already have salerId => not required saler => use appointment after update

                        userEventId = await scopedCalendarService.CreateCalendarEventForAppointmentAsync(appointmentToUpdate, userId);
                        if (!string.IsNullOrEmpty(userEventId))
                        {
                            _logger.LogInformation($"User calendar event {userEventId} created for appointment {appointment.id}");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Failed to create user calendar event for appointment {appointment.id}");
                    }

                    // ✅ 2. Tạo calendar event cho SALER (nếu có salerId)
                    string? salerEventId = null;
                    if (!string.IsNullOrEmpty(property.salerId))
                    {
                        try
                        {
                            //if appointment does not have salerId <->  property.salerId = null
                            salerEventId = await scopedCalendarService.CreateCalendarEventForAppointmentAsync(appointmentToUpdate, property.salerId);

                            if (!string.IsNullOrEmpty(salerEventId))
                            {
                                _logger.LogInformation($"Saler calendar event {salerEventId} created for appointment {appointment.id}");
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, $"Failed to create saler calendar event for appointment {appointment.id}");
                        }
                    }

                    // ✅ 3. Cập nhật appointment với cả 2 eventId
                    if (!string.IsNullOrEmpty(userEventId) || !string.IsNullOrEmpty(salerEventId))
                    {
                        appointmentToUpdate.userGoogleCalendarEventId = userEventId; // User event ID
                        appointmentToUpdate.salerGoogleCalendarEventId = salerEventId; // ✅ New field for saler event ID
                        appointmentToUpdate.updatedAt = DateTime.UtcNow;

                        await scopedUnitOfWork.Appointments.UpdateAsync(appointmentToUpdate.id, appointmentToUpdate);
                        await scopedUnitOfWork.SaveAsync();

                        _logger.LogInformation($"Calendar events linked to appointment {appointment.id}: User Event ID={userEventId}, Saler={salerEventId}");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Failed to create calendar events for appointment {appointment.id}");

                    // ✅ Optional: Add to retry queue
                    //await _retryQueueService.AddCalendarEventCreationToRetryAsync(appointment.id);
                }
            });

            _logger.LogInformation($"Appointment {appointment.id} created successfully. Calendar events creation running in background.");

            // ✅ Return ngay lập tức
            return await ToAppointmentResponse(appointment);
        }

        private void ValidateAppointmentTime(DateTime appointmentDate)
        {
            //VALIDATE APPOINTMENT TIME FIRST
            if (!AppointmentTimeValidator.IsValidAppointmentTime(appointmentDate))
            {
                var minTime = AppointmentTimeValidator.GetMinimumAllowedTime();
                throw new InvalidOperationException(
                    $"Appointment time must be at least 30 minutes from now. " +
                    $"Minimum allowed time: {minTime:yyyy-MM-dd HH:mm}");
            }
            return;
        }

        public async Task<Appointment> GetAppointmentByIdAsync(string id)
        {
            var (userId, role) = _currentUserService.GetCurrentUser();
            var appointment = await _unitOfWork.Appointments.GetByIdAsync(id) ?? throw new Exception("Appointment not found");
            if (role == "User")
            {
                var lead = await _unitOfWork.Leads.GetLeadByUserIdAsync(userId);
                if (appointment.leadId != lead.id)
                {
                    throw new Exception("Bạn không có quyền thực hiện hành động này");
                }
                return appointment;
            }
            else if (role == "Saler")
            {
                if (appointment.salerId != userId)
                {
                    throw new Exception("Bạn không có quyền thực hiện hành động này");
                }
                return appointment;
            }
            else if (role != "Admin")
            {
                throw new BadHttpRequestException("Vai trò không hợp lệ");
            }

            return appointment;
        }

        public async Task CancelAppointmentAsync(string id)
        {
            var (userId, role) = _currentUserService.GetCurrentUser();
            var lead = await _unitOfWork.Leads.GetLeadByUserIdAsync(userId) ?? throw new Exception("Lead not found");
            var appointment = await _unitOfWork.Appointments.GetByIdAsync(id) ?? throw new Exception("Appointment not found");
            if (appointment.leadId != lead.id)
            {
                throw new Exception("Bạn không có quyền thực hiện hành động này");
            }
            appointment.status = AppointmentStatus.Cancelled;
            await _unitOfWork.Appointments.UpdateAsync(id, appointment);
        }

        public async Task CancelAppointmentAsync(
                                                 string id,
                                                 string cancellationReason = null,
                                                 CancellationType? cancellationType = null,
                                                 string notes = null,
                                                 string relatedTicketId = null)
        {
            var appointment = await _unitOfWork.Appointments.GetByIdAsync(id) ?? throw new Exception("Appointment not found");
            var (userId, role) = _currentUserService.GetCurrentUser();

            // ✅ Authorization check
            if (role == "User")
            {
                var lead = await _unitOfWork.Leads.GetLeadByUserIdAsync(userId);
                if (appointment.leadId != lead.id)
                {
                    throw new UnauthorizedAccessException("You can not do this action");
                }
            }
            else if (role == "Saler")
            {
                if (appointment.salerId != userId)
                {
                    throw new UnauthorizedAccessException("You can not do this action");
                }
            }
            else if (role != "Admin")
            {
                throw new UnauthorizedAccessException("You can not do this action");
            }

            // ✅ Prevent double cancellation
            if (appointment.status == AppointmentStatus.Cancelled)
            {
                throw new Exception("Appointment is already cancelled");
            }

            // ✅ Store previous status for history
            var previousStatus = appointment.status;

            // ✅ Determine cancellation type if not provided
            if (!cancellationType.HasValue)
            {
                cancellationType = role switch
                {
                    "User" => CancellationType.UserCancelled,
                    "Saler" => CancellationType.SalerCancelled,
                    "Admin" => CancellationType.AdminCancelled,
                    _ => CancellationType.SystemCancelled
                };
            }

            // ✅ Create CancellationInfo object
            appointment.cancellationInfo = new CancellationInfo
            {
                reason = cancellationReason ?? "No reason provided",
                cancelledAt = DateTime.UtcNow,
                cancelledBy = userId,
                type = cancellationType.Value,
                notes = notes,
                isAutomatic = role == "System",
                relatedTicketId = relatedTicketId,
                calendarDeletion = new CalendarDeletionInfo() // Initialize empty
            };

            // ✅ Update appointment status
            appointment.status = AppointmentStatus.Cancelled;
            appointment.updatedAt = DateTime.UtcNow;
            appointment.lastModifiedBy = userId;

            // ✅ Add to status history
            appointment.statusHistory.Add(new AppointmentStatusHistory
            {
                previousStatus = previousStatus,
                newStatus = AppointmentStatus.Cancelled,
                changedAt = DateTime.UtcNow,
                changedBy = userId,
                reason = cancellationReason,
                notes = $"Cancelled by {role} ({cancellationType})"
            });

            await _unitOfWork.Appointments.UpdateAsync(id, appointment);
            await _unitOfWork.SaveAsync();

            _logger.LogInformation($"Appointment {id} cancelled by {role} {userId}. Type: {cancellationType}, Reason: {cancellationReason}");

            // ✅ Delete Google Calendar events trong background
            _ = Task.Run(async () =>
            {
                await ProcessCalendarEventDeletionAsync(id, userId);
            });

            _logger.LogInformation($"Appointment {id} cancelled successfully. Calendar events deletion running in background.");
        }

        public async Task RescheduleAppointmentAsync(string id, DateTime date)
        {
            var appointment = await _unitOfWork.Appointments.GetByIdAsync(id) ?? throw new Exception("Appointment not found");

            var (userId, role) = _currentUserService.GetCurrentUser();
            ValidateAppointmentTime(date);
            // ✅ STEP 1: Authorization check
            if (role == "User")
            {
                var lead = await _unitOfWork.Leads.GetLeadByUserIdAsync(userId);
                if (appointment.leadId != lead.id)
                {
                    throw new Exception("Bạn không có quyền thực hiện hành động này");
                }
            }
            else if (role == "Saler")
            {
                if (appointment.salerId != userId)
                {
                    throw new Exception("Bạn không có quyền thực hiện hành động này");
                }
            }
            else
            {
                throw new Exception("Bạn không có quyền thực hiện hành động này");
            }

            // ✅ STEP 2: Check for time conflicts for BOTH user and saler
            // Check conflict for the user (lead owner)
            var userHasTimeConflict = await _unitOfWork.Appointments.HasTimeConflictAsync(
                appointment.userId, date, 60, excludeAppointmentId: id);

            if (userHasTimeConflict)
            {
                var userConflictingAppointments = await _unitOfWork.Appointments.GetConflictingAppointmentsAsync(
                    appointment.userId, date, 60, excludeAppointmentId: id);

                var userConflictDetails = userConflictingAppointments.Select(c =>
                    $"• {c.propertyTitle} at {c.propertyAddress} on {c.date:yyyy-MM-dd HH:mm}").ToList();

                var userErrorMessage = $"User already has appointment(s) scheduled around this time:\n{string.Join("\n", userConflictDetails)}";
                throw new InvalidOperationException(userErrorMessage);
            }

            // Check conflict for the saler (if exists)
            if (!string.IsNullOrEmpty(appointment.salerId))
            {
                var salerHasTimeConflict = await _unitOfWork.Appointments.HasTimeConflictAsync(
                    appointment.salerId, date, 60, excludeAppointmentId: id);

                if (salerHasTimeConflict)
                {
                    var salerConflictingAppointments = await _unitOfWork.Appointments.GetConflictingAppointmentsAsync(
                        appointment.salerId, date, 60, excludeAppointmentId: id);

                    var salerConflictDetails = salerConflictingAppointments.Select(c =>
                        $"• {c.propertyTitle} at {c.propertyAddress} on {c.date:yyyy-MM-dd HH:mm}").ToList();

                    var salerErrorMessage = $"Saler already has appointment(s) scheduled around this time:\n{string.Join("\n", salerConflictDetails)}";
                    throw new InvalidOperationException(salerErrorMessage);
                }
            }

            //STEP 3: Store old data for calendar updates and notifications
            var oldDate = appointment.date;
            var oldUserCalendarEventId = appointment.userGoogleCalendarEventId;
            var oldSalerCalendarEventId = appointment.salerGoogleCalendarEventId;


            //STEP 4: Update appointment
            appointment.date = date;
            appointment.status = AppointmentStatus.Rescheduled; // Reset to rescheduled
            appointment.updatedAt = DateTime.UtcNow;

            await _unitOfWork.Appointments.UpdateAsync(id, appointment);

            _logger.LogInformation($"Appointment {id} rescheduled from {oldDate:yyyy-MM-dd HH:mm} to {date:yyyy-MM-dd HH:mm}");

            //Update Google Calendar events trong background
            _ = Task.Run(async () =>
            {
                try
                {
                    _logger.LogInformation($"Starting background calendar event updates for rescheduled appointment {id}");

                    // ✅ Tạo scope mới
                    using var scope = _serviceScopeFactory.CreateScope();
                    var scopedUnitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();
                    var scopedCalendarService = scope.ServiceProvider.GetRequiredService<ICalendarIntegrationService>();

                    var appointmentToUpdate = await scopedUnitOfWork.Appointments.GetByIdAsync(id);
                    if (appointmentToUpdate == null)
                    {
                        _logger.LogError($"Appointment {id} not found during background calendar update");
                        return;
                    }

                    var updateTasks = new List<Task>();

                    // ✅ 1. Update User's calendar event
                    if (!string.IsNullOrEmpty(appointmentToUpdate.userGoogleCalendarEventId))
                    {
                        updateTasks.Add(UpdateUserCalendarEventAsync(scopedCalendarService, appointmentToUpdate));
                    }

                    // ✅ 2. Update Saler's calendar event
                    if (!string.IsNullOrEmpty(appointmentToUpdate.salerGoogleCalendarEventId) &&
                        !string.IsNullOrEmpty(appointmentToUpdate.salerId))
                    {
                        updateTasks.Add(UpdateSalerCalendarEventAsync(scopedCalendarService, appointmentToUpdate));
                    }

                    // ✅ Execute all updates in parallel
                    if (updateTasks.Any())
                    {
                        await Task.WhenAll(updateTasks);
                        _logger.LogInformation($" All calendar events updated for appointment {id}");
                    }
                    else
                    {
                        _logger.LogWarning($" No calendar events found to update for appointment {id}");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $" Failed to update calendar events for rescheduled appointment {id}");

                    // ✅ Optional: Add to retry queue
                    // await _retryQueueService.AddCalendarEventUpdateToRetryAsync(id, date);
                }
            });

            _logger.LogInformation($" Appointment {id} rescheduled successfully. Calendar events update running in background.");
        }

        public async Task CreateMessageForAppointmentAsync(string appointmentId, AppointmentMessageCreateRequest request)
        {
            var appointment = await _unitOfWork.Appointments.GetByIdAsync(appointmentId) ?? throw new Exception("Không tìm thấy cuộc hẹn");
            string? userIdToCompare = null;
            var (userId, role) = _currentUserService.GetCurrentUser();

            if (role == "Saler")
            {
                var saler = await _unitOfWork.Users.GetByIdAsync(userId)
                    ?? throw new Exception("Không tìm thấy seller");

                userIdToCompare = userId;
            }
            else if (role == "User")
            {
                var lead = await _unitOfWork.Leads.GetLeadByUserIdAsync(userId)
                    ?? throw new Exception("Không tìm thấy khách hàng tiềm năng");

                userIdToCompare = lead.id;
            }
            else
            {
                throw new Exception("Vai trò không hợp lệ");
            }

            if (appointment.salerId != userIdToCompare && appointment.leadId != userIdToCompare)
            {
                throw new Exception("Bạn không có quyền thực hiện hành động này");
            }

            var message = new AppointmentMessage
            {
                userId = userIdToCompare,
                content = request.content,
            };

            appointment.messages.Add(message);
            await _unitOfWork.Appointments.UpdateAsync(appointmentId, appointment);
        }

        public async Task<List<AppointmentResponse>> GetAppointmentsByUserIdAsync(string status)
        {
            var (userId, role) = _currentUserService.GetCurrentUser();
            var foundUser = await _unitOfWork.Users.GetByIdAsync(userId) ?? throw new Exception("User not found");

            var appointments = new List<Appointment>();
            if (foundUser.role == UserRole.Saler)
            {
                appointments = await _unitOfWork.Appointments.GetBySalerIdAsync(userId, status);
            }
            else
            {
                var lead = await _unitOfWork.Leads.GetLeadByUserIdAsync(userId);
                if (lead == null)
                {
                    return new List<AppointmentResponse>();
                }
                appointments = await _unitOfWork.Appointments.GetByLeadIdAsync(lead.id, status);
            }

            var tasks = appointments.Select(async appointment =>
            {
                User saler = null;
                Property property = null;

                if (!string.IsNullOrEmpty(appointment.salerId))
                {
                    saler = await _unitOfWork.Users.GetByIdAsync(appointment.salerId);
                }

                if (!string.IsNullOrEmpty(appointment.propertyId))
                {
                    property = await _unitOfWork.Properties.GetByIdAsync(appointment.propertyId);
                }

                return await ToAppointmentResponse(appointment);
            });


            var appointmentResponses = await Task.WhenAll(tasks);

            return appointmentResponses.ToList();
        }


        public async Task<AssignmentResult> AssignSellerToAppointmentAsyncs(string appointmentId, AssignSellerToAppointmentRequest request)
        {
            try
            {
                _logger.LogInformation($"Starting seller assignment for appointment {appointmentId}");

                // ✅ 1. Parallel validation và data fetching
                var validationResult = await ValidateAndFetchDataAsync(appointmentId, request);
                if (!validationResult.isValid)
                {
                    throw new InvalidDataException(validationResult.message);
                }

                var (appointment, seller, lead, property) = validationResult.data;

                // ✅ 2. Business rules validation
                await ValidateBusinessRulesAsync(appointment, seller, lead);

                // ✅ 3. Perform atomic updates (MongoDB optimized)
                var updatedAppointment = await PerformAtomicUpdatesAndGetResultAsync(appointment, seller, lead, property, request);

                _logger.LogInformation($" Seller {seller.fullName} assigned to appointment {appointmentId}");

                // ✅ 4. Background tasks (non-blocking)
                var backgroundResult = await TriggerBackgroundTasksAsync(updatedAppointment, seller, lead, property);

                return new AssignmentResult
                {
                    isSuccess = true,
                    appointmentId = appointmentId,
                    sellerId = seller.id,
                    sellerName = seller.fullName,
                    calendarTaskStatus = backgroundResult.calendarStatus,
                    emailTaskStatus = backgroundResult.emailStatus,
                    message = "Seller assigned successfully"
                };
            }
            //catch (BusinessException)
            //{
            //    throw; // Re-throw business exceptions as-is
            //}
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to assign seller to appointment {appointmentId}");
                throw new SystemException($"Failed to assign seller: {ex.Message}", ex);
            }
        }

        // ✅ Parallel data fetching và validation
        private async Task<ValidationResultDTO<(Appointment appointment, User seller, Lead lead, Property property)>> ValidateAndFetchDataAsync(string appointmentId, AssignSellerToAppointmentRequest request)
        {
            try
            {
                _logger.LogInformation($"Validating assignment data for appointment {appointmentId}");

                // ✅ Parallel queries for better performance
                var appointmentTask = _unitOfWork.Appointments.GetByIdAsync(appointmentId);
                var sellerTask = _unitOfWork.Users.GetByIdAsync(request.sellerId);

                await Task.WhenAll(appointmentTask, sellerTask);

                var appointment = appointmentTask.Result;
                var seller = sellerTask.Result;

                // ✅ Early validation using existing pattern
                if (appointment == null)
                    return ValidationResultDTO<(Appointment, User, Lead, Property)>.Fail("Appointment not found");

                if (seller == null)
                    return ValidationResultDTO<(Appointment, User, Lead, Property)>.Fail("Seller not found");

                if (seller.role != UserRole.Saler)
                    return ValidationResultDTO<(Appointment, User, Lead, Property)>.Fail("Selected user is not a seller");

                // ✅ Check if already assigned
                if (appointment.salerId == request.sellerId)
                    return ValidationResultDTO<(Appointment, User, Lead, Property)>.Fail("Appointment is already assigned to this seller");

                // ✅ Fetch related entities in parallel
                var leadTask = _unitOfWork.Leads.GetByIdAsync(appointment.leadId);
                var propertyTask = _unitOfWork.Properties.GetByIdAsync(appointment.propertyId);

                await Task.WhenAll(leadTask, propertyTask);

                var lead = leadTask.Result;
                var property = propertyTask.Result;

                if (lead == null)
                    return ValidationResultDTO<(Appointment, User, Lead, Property)>.Fail("Lead not found");

                if (property == null)
                    return ValidationResultDTO<(Appointment, User, Lead, Property)>.Fail("Property not found");

                _logger.LogInformation($"Validation successful for appointment {appointmentId}");

                return ValidationResultDTO<(Appointment, User, Lead, Property)>.Success((appointment, seller, lead, property));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error during validation for appointment {appointmentId}");
                return ValidationResultDTO<(Appointment, User, Lead, Property)>.Fail("Validation failed due to system error");
            }
        }

        // ✅ Business rules validation
        private async Task ValidateBusinessRulesAsync(Appointment appointment, User seller, Lead lead)
        {
            // ✅ Check appointment status
            if (appointment.status == AppointmentStatus.Completed)
                throw new InvalidOperationException("Cannot assign seller to completed appointment");

            if (appointment.status == AppointmentStatus.Cancelled)
                throw new InvalidOperationException("Cannot assign seller to cancelled appointment");

            // ✅ Check if appointment already has a seller
            if (!string.IsNullOrEmpty(appointment.salerId))
                throw new InvalidOperationException("Appointment already has an assigned seller. Use reassignment instead.");

            // ✅ Check appointment date
            if (appointment.date <= DateTime.UtcNow)
                throw new InvalidOperationException("Cannot assign seller to past appointments");

            // ✅ Check seller availability (if service exists)
            //if (_sellerAvailabilityService != null)
            //{
            //    var isAvailable = await _sellerAvailabilityService.IsAvailableAsync(seller.id, appointment.date);
            //    if (!isAvailable)
            //        throw new BusinessException($"Seller {seller.fullName} is not available at {appointment.date:yyyy-MM-dd HH:mm}");
            //}

            // ✅ Check seller workload
            //if (_sellerWorkloadService != null)
            //{
            //    var workload = await _sellerWorkloadService.GetWorkloadAsync(seller.id, appointment.date.Date);
            //    if (workload.appointmentCount >= 10) // Configurable limit
            //        throw new BusinessException($"Seller {seller.fullName} has reached maximum appointments for this day");
            //}
        }


        // ✅ MongoDB atomic updates
        private async Task<Appointment> PerformAtomicUpdatesAndGetResultAsync(
                                                      Appointment appointment,
                                                      User seller,
                                                      Lead lead,
                                                      Property property,
                                                      AssignSellerToAppointmentRequest request)
        {
            var currentUserId = _currentUserService.GetUserId();
            var now = DateTime.UtcNow;

            // ✅ 1. Update appointment và get updated version
            var updatedAppointment = await UpdateAppointmentAndReturnAsync(appointment, seller.id, currentUserId, now);

            // ✅ 2. Prepare other updates
            var otherUpdateTasks = new List<Task>();

            // ✅ Update lead assignment (only if not already assigned)
            if (!lead.assignedTo.Any(x => x.id == seller.id))
            {
                otherUpdateTasks.Add(UpdateLeadAssignmentAsync(lead, seller.id, now));
            }

            // ✅ Update property (only if requested)
            if (request.assignPropertyToSeller && string.IsNullOrEmpty(property.salerId))
            {
                otherUpdateTasks.Add(UpdatePropertyAssignmentAsync(property, seller.id, now));
            }

            // ✅ Execute other updates in parallel
            if (otherUpdateTasks.Any())
            {
                await Task.WhenAll(otherUpdateTasks);
            }

            // ✅ Save all changes
            await _unitOfWork.SaveAsync();

            return updatedAppointment;
        }
        // ✅ MongoDB optimized appointment update
        private async Task<Appointment> UpdateAppointmentAndReturnAsync(Appointment appointment, string sellerId, string currentUserId, DateTime now)
        {
            var filter = Builders<Appointment>.Filter.Eq(x => x.id, appointment.id);
            var update = Builders<Appointment>.Update
                .Set(x => x.salerId, sellerId)
                .Set(x => x.updatedAt, now)
                .Set(x => x.lastModifiedBy, currentUserId)
                .Set(x => x.status, AppointmentStatus.Confirmed)
                .Set(x => x.calendarSyncStatus, CalendarSyncStatus.Pending)
                .Push(x => x.statusHistory, new AppointmentStatusHistory
                {
                    previousStatus = appointment.status,
                    newStatus = AppointmentStatus.Confirmed,
                    changedAt = now,
                    changedBy = currentUserId,
                    reason = "Seller assigned",
                    notes = $"Assigned to seller {sellerId}"
                });

            // ✅ FindOneAndUpdate returns the updated document
            var options = new FindOneAndUpdateOptions<Appointment>
            {
                ReturnDocument = ReturnDocument.After // Return document AFTER update
            };

            var updatedAppointment = await _unitOfWork.Appointments.FindOneAndUpdateAsync(filter, update, options);

            if (updatedAppointment == null)
            {
                throw new InvalidOperationException($"Failed to update appointment {appointment.id}");
            }

            return updatedAppointment;
        }

        // ✅ MongoDB optimized lead update
        private async Task UpdateLeadAssignmentAsync(Lead lead, string sellerId, DateTime now)
        {
            var filter = Builders<Lead>.Filter.Eq(x => x.id, lead.id);
            var update = Builders<Lead>.Update
                .Push(x => x.assignedTo, new AssignedTo { id = sellerId })
                .Set(x => x.updatedAt, now);

            var result = await _unitOfWork.Leads.UpdateOneAsync(filter, update);

            if (result.ModifiedCount == 0)
            {
                throw new InvalidOperationException($"Failed to update lead {lead.id}");
            }
        }

        // ✅ MongoDB optimized property update
        private async Task UpdatePropertyAssignmentAsync(Property property, string sellerId, DateTime now)
        {
            var filter = Builders<Property>.Filter.Eq(x => x.id, property.id);
            var update = Builders<Property>.Update
                .Set(x => x.salerId, sellerId)
                .Set(x => x.updatedAt, now);

            var result = await _unitOfWork.Properties.UpdateOneAsync(filter, update);

            if (result.ModifiedCount == 0)
            {
                throw new InvalidOperationException($"Failed to update property {property.id}");
            }
        }

        // ✅ Non-blocking background tasks
        private async Task<BackgroundTaskResult> TriggerBackgroundTasksAsync(
                                                                              Appointment updatedAppointment,
                                                                              User seller,
                                                                              Lead lead,
                                                                              Property property)
        {
            var result = new BackgroundTaskResult();

            // ✅ Fire and forget background tasks
            var backgroundTask = Task.Run(async () =>
            {
                using var scope = _serviceScopeFactory.CreateScope();
                var scopedServices = scope.ServiceProvider;

                var tasks = new List<Task>
                  {
                      HandleEmailNotificationAsync(scopedServices, updatedAppointment, seller, lead, property),
                      HandleCalendarEventAsync(scopedServices, updatedAppointment, seller, lead ,property)
                  };

                var results = await Task.WhenAll(tasks.Select(async task =>
                {
                    try
                    {
                        await task;
                        return true;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Background task failed");
                        return false;
                    }
                }));

                return new { emailSuccess = results[0], calendarSuccess = results[1] };
            });

            //Don't wait for background tasks, but track their status
            result.calendarStatus = TaskStatus.Pending;
            result.emailStatus = TaskStatus.Pending;

            //Optional: Wait a short time to see if tasks complete quickly
            var completedTask = await Task.WhenAny(backgroundTask, Task.Delay(1000));
            if (completedTask == backgroundTask)
            {
                try
                {
                    var taskResult = await backgroundTask;
                    result.emailStatus = taskResult.emailSuccess ? TaskStatus.Success : TaskStatus.Failed;
                    result.calendarStatus = taskResult.calendarSuccess ? TaskStatus.Success : TaskStatus.Failed;
                }
                catch
                {
                    result.emailStatus = TaskStatus.Failed;
                    result.calendarStatus = TaskStatus.Failed;
                }
            }

            return result;
        }
        // ✅ Separated email handling
        private async Task HandleEmailNotificationAsync(
          IServiceProvider services,
          Appointment appointment,
          User seller,
          Lead lead,
          Property property)
        {
            try
            {
                var emailService = services.GetRequiredService<IEmailService>();

                var emailContent = EmailTemplate.GetAppointmentEmailContent(
                    seller.fullName,
                    lead.name,
                    property.location?.address ?? "N/A",
                    appointment.date);

                await emailService.SendEmailAsync(seller.email, "New Appointment Assigned", emailContent);

                _logger.LogInformation($"Email sent to seller {seller.email} for appointment {appointment.id}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to send email for appointment {appointment.id}");
                throw;
            }
        }

        // ✅ Separated calendar handling
        private async Task HandleCalendarEventAsync(
          IServiceProvider services,
          Appointment updatedAppointment,
          User seller,
          Lead lead,
          Property property)
        {
            try
            {
                var calendarService = services.GetRequiredService<ICalendarIntegrationService>();
                var unitOfWork = services.GetRequiredService<IUnitOfWork>();
                var userService = services.GetRequiredService<IUserService>();

                _logger.LogInformation($"Creating seller calendar event and updating user event for appointment {updatedAppointment.id}");

                // ✅ Step 1: Create calendar event for seller
                _logger.LogInformation($"Creating seller calendar event for {seller.fullName}");
                var sellerEventId = await calendarService.CreateCalendarEventForAppointmentAsync(updatedAppointment, seller.id);

                if (string.IsNullOrEmpty(sellerEventId))
                {
                    _logger.LogError($"Failed to create seller calendar event for appointment {updatedAppointment.id}");
                    throw new InvalidOperationException("Failed to create seller calendar event");
                }

                _logger.LogInformation($"Seller calendar event created: {sellerEventId}");
                bool userEventUpdated = false;
                // ✅ Step 2: Update user's existing calendar event (add seller as attendee)
                if (!string.IsNullOrEmpty(updatedAppointment.userGoogleCalendarEventId))
                {
                    _logger.LogInformation($"Updating user calendar event {updatedAppointment.userGoogleCalendarEventId} with seller attendee");

                    await UpdateUserCalendarEventAsync(calendarService, updatedAppointment);
                    userEventUpdated = true;
                }
                else
                {
                    _logger.LogWarning($"No user calendar event ID found for appointment {updatedAppointment.id}");
                }

                // ✅ Step 4: Update appointment with seller event ID and sync status
                var syncStatus = userEventUpdated ? CalendarSyncStatus.FullySynced : CalendarSyncStatus.PartiallySynced;

                var filter = Builders<Appointment>.Filter.Eq(x => x.id, updatedAppointment.id);
                var update = Builders<Appointment>.Update
                    .Set(x => x.salerGoogleCalendarEventId, sellerEventId)
                    .Set(x => x.calendarSyncStatus, syncStatus)
                    .Set(x => x.updatedAt, DateTime.UtcNow);

                await unitOfWork.Appointments.UpdateOneAsync(filter, update);
                await unitOfWork.SaveAsync();

                _logger.LogInformation($"Calendar sync completed for appointment {updatedAppointment.id}");
                _logger.LogInformation($"   - Seller event: {sellerEventId}");
                _logger.LogInformation($"   - User event updated: {userEventUpdated}");
                _logger.LogInformation($"   - Sync status: {syncStatus}");

                // ✅ Step 5: Send notifications
                //await SendCalendarSyncNotificationsAsync(services, appointment, seller, user, userEventUpdated);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $" Failed to handle calendar events for appointment {updatedAppointment.id}");

                // ✅ Update sync status to failed
                //await UpdateCalendarSyncStatusAsync(services, appointment.id, CalendarSyncStatus.Failed);
                throw;
            }
        }

        // ✅ Separate method để xử lý calendar deletion
        private async Task ProcessCalendarEventDeletionAsync(string appointmentId, string requestedBy)
        {
            try
            {
                _logger.LogInformation($"Starting calendar event deletion for appointment {appointmentId}");

                using var scope = _serviceScopeFactory.CreateScope();
                var scopedUnitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();
                var scopedCalendarService = scope.ServiceProvider.GetRequiredService<ICalendarIntegrationService>();

                var appointment = await scopedUnitOfWork.Appointments.GetByIdAsync(appointmentId);
                if (appointment?.cancellationInfo == null)
                {
                    _logger.LogError($"Appointment {appointmentId} or cancellation info not found");
                    return;
                }

                // ✅ Delete calendar events
                var (userDeleted, salerDeleted) = await scopedCalendarService.DeleteAllCalendarEventsForAppointmentAsync(appointment);

                // ✅ Update CancellationInfo.CalendarDeletion
                appointment.cancellationInfo.calendarDeletion.userEventDeleted = userDeleted;
                appointment.cancellationInfo.calendarDeletion.salerEventDeleted = salerDeleted;
                appointment.cancellationInfo.calendarDeletion.isDeleted = userDeleted && salerDeleted;
                appointment.cancellationInfo.calendarDeletion.deletedAt = DateTime.UtcNow;

                // ✅ Set deletion status
                appointment.cancellationInfo.calendarDeletion.deletionStatus = (userDeleted && salerDeleted) ? "Success" :
                                                                              (userDeleted || salerDeleted) ? "Partial" : "Failed";

                // ✅ Clear event IDs if successfully deleted
                if (userDeleted) appointment.userGoogleCalendarEventId = null;
                if (salerDeleted) appointment.salerGoogleCalendarEventId = null;

                // ✅ Update calendar sync status
                appointment.calendarSyncStatus = (userDeleted && salerDeleted) ? CalendarSyncStatus.Deleted :
                                               (userDeleted || salerDeleted) ? CalendarSyncStatus.PartiallyDeleted :
                                               CalendarSyncStatus.Failed;

                appointment.updatedAt = DateTime.UtcNow;
                appointment.lastModifiedBy = "System";

                await scopedUnitOfWork.Appointments.UpdateAsync(appointmentId, appointment);
                await scopedUnitOfWork.SaveAsync();

                _logger.LogInformation($" Calendar deletion completed for appointment {appointmentId}. Status: {appointment.cancellationInfo.calendarDeletion.deletionStatus}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $" Failed to delete calendar events for appointment {appointmentId}");

                // ✅ Update failure info
                await UpdateCalendarDeletionFailureAsync(appointmentId, ex.Message);
            }
        }

        // ✅ Update failure information
        private async Task UpdateCalendarDeletionFailureAsync(string appointmentId, string failureReason)
        {
            try
            {
                using var scope = _serviceScopeFactory.CreateScope();
                var scopedUnitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();

                var appointment = await scopedUnitOfWork.Appointments.GetByIdAsync(appointmentId);
                if (appointment?.cancellationInfo != null)
                {
                    appointment.cancellationInfo.calendarDeletion.deletionStatus = "Failed";
                    appointment.cancellationInfo.calendarDeletion.failureReason = failureReason;
                    appointment.cancellationInfo.calendarDeletion.retryCount++;
                    appointment.cancellationInfo.calendarDeletion.lastRetryAt = DateTime.UtcNow;
                    appointment.calendarSyncStatus = CalendarSyncStatus.Failed;
                    appointment.updatedAt = DateTime.UtcNow;
                    appointment.lastModifiedBy = "System";

                    await scopedUnitOfWork.Appointments.UpdateAsync(appointmentId, appointment);
                    await scopedUnitOfWork.SaveAsync();
                }
            }
            catch (Exception updateEx)
            {
                _logger.LogError(updateEx, $"❌ Failed to update calendar deletion failure for appointment {appointmentId}");
            }
        }

        // ✅ Helper methods
        private async Task UpdateUserCalendarEventAsync(ICalendarIntegrationService calendarService, Appointment appointment)
        {
            try
            {
                var success = await calendarService.UpdateCalendarEventForAppointmentAsync(
                    appointment,
                    appointment.userId,
                    appointment.userGoogleCalendarEventId);

                if (success)
                {
                    _logger.LogInformation($"User calendar event {appointment.userGoogleCalendarEventId} updated for appointment {appointment.id}");
                }
                else
                {
                    _logger.LogWarning($"Failed to update user calendar event {appointment.userGoogleCalendarEventId} for appointment {appointment.id}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating user calendar event for appointment {appointment.id}");
            }
        }

        private async Task UpdateSalerCalendarEventAsync(ICalendarIntegrationService calendarService, Appointment appointment)
        {
            try
            {
                var success = await calendarService.UpdateCalendarEventForAppointmentAsync(
                    appointment,
                    appointment.salerId,
                    appointment.salerGoogleCalendarEventId);

                if (success)
                {
                    _logger.LogInformation($"Saler calendar event {appointment.salerGoogleCalendarEventId} updated for appointment {appointment.id}");
                }
                else
                {
                    _logger.LogWarning($"Failed to update saler calendar event {appointment.salerGoogleCalendarEventId} for appointment {appointment.id}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating saler calendar event for appointment {appointment.id}");
            }
        }

        /// <summary>
        /// Re-assign seller cho appointment đã có seller
        /// </summary>
        /// <param name="appointmentId">ID của appointment</param>
        /// <param name="request">Request chứa sellerId mới</param>
        /// <returns>ReassignmentResult với thông tin kết quả</returns>
        public async Task<ReassignmentResult> ReassignSellerToAppointmentAsync(
            string appointmentId,
            ReassignSellerRequest request)
        {
            try
            {
                _logger.LogInformation($"Starting seller reassignment for appointment {appointmentId}");

                // ✅ 1. Validation và data fetching
                var validationResult = await ValidateAndFetchDataForReassignmentAsync(appointmentId, request);
                if (!validationResult.isValid)
                {
                    throw new InvalidDataException(validationResult.message);
                }

                var (appointment, newSeller, oldSeller, lead, property) = validationResult.data;

                // ✅ 2. Business rules validation cho reassignment
                await ValidateReassignmentBusinessRulesAsync(appointment, newSeller, oldSeller);

                // ✅ 3. Perform atomic updates với cleanup của seller cũ
                var updatedAppointment = await PerformReassignmentUpdatesAsync(
                    appointment, newSeller, oldSeller, lead, property, request);

                _logger.LogInformation($"Seller reassigned from {oldSeller?.fullName} to {newSeller.fullName} for appointment {appointmentId}");

                // ✅ 4. Background tasks (calendar cleanup + new setup)
                var backgroundResult = await TriggerReassignmentBackgroundTasksAsync(
                    updatedAppointment, newSeller, oldSeller, lead, property);

                return new ReassignmentResult
                {
                    isSuccess = true,
                    appointmentId = appointmentId,
                    oldSellerId = oldSeller?.id,
                    oldSellerName = oldSeller?.fullName,
                    newSellerId = newSeller.id,
                    newSellerName = newSeller.fullName,
                    calendarTaskStatus = backgroundResult.calendarStatus,
                    emailTaskStatus = backgroundResult.emailStatus,
                    message = "Seller reassigned successfully"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to reassign seller for appointment {appointmentId}");
                throw new SystemException($"Failed to reassign seller: {ex.Message}", ex);
            }
        }

        #region Validation Methods for Reassignment
        /// <summary>
        /// Validate và fetch data cho reassignment
        /// </summary>
        private async Task<ValidationResultDTO<(Appointment appointment, User newSeller, User oldSeller, Lead lead, Property property)>>
            ValidateAndFetchDataForReassignmentAsync(string appointmentId, ReassignSellerRequest request)
        {
            try
            {
                _logger.LogInformation($"Validating reassignment data for appointment {appointmentId}");

                // ✅ Parallel queries
                var appointmentTask = _unitOfWork.Appointments.GetByIdAsync(appointmentId);
                var newSellerTask = _unitOfWork.Users.GetByIdAsync(request.newSellerId);

                await Task.WhenAll(appointmentTask, newSellerTask);

                var appointment = appointmentTask.Result;
                var newSeller = newSellerTask.Result;

                // ✅ Basic validation
                if (appointment == null)
                    return ValidationResultDTO<(Appointment, User, User, Lead, Property)>.Fail("Appointment not found");

                if (newSeller == null)
                    return ValidationResultDTO<(Appointment, User, User, Lead, Property)>.Fail("New seller not found");

                if (newSeller.role != UserRole.Saler)
                    return ValidationResultDTO<(Appointment, User, User, Lead, Property)>.Fail("Selected user is not a seller");

                // ✅ Check if appointment has current seller
                if (string.IsNullOrEmpty(appointment.salerId))
                    return ValidationResultDTO<(Appointment, User, User, Lead, Property)>.Fail("Appointment has no assigned seller. Use assignment instead.");

                // ✅ Check if trying to assign same seller
                if (appointment.salerId == request.newSellerId)
                    return ValidationResultDTO<(Appointment, User, User, Lead, Property)>.Fail("Appointment is already assigned to this seller");

                // ✅ Fetch old seller and related entities
                var oldSellerTask = _unitOfWork.Users.GetByIdAsync(appointment.salerId);
                var leadTask = _unitOfWork.Leads.GetByIdAsync(appointment.leadId);
                var propertyTask = _unitOfWork.Properties.GetByIdAsync(appointment.propertyId);

                await Task.WhenAll(oldSellerTask, leadTask, propertyTask);

                var oldSeller = oldSellerTask.Result;
                var lead = leadTask.Result;
                var property = propertyTask.Result;

                if (lead == null)
                    return ValidationResultDTO<(Appointment, User, User, Lead, Property)>.Fail("Lead not found");

                if (property == null)
                    return ValidationResultDTO<(Appointment, User, User, Lead, Property)>.Fail("Property not found");

                // ✅ Old seller có thể null nếu đã bị xóa
                _logger.LogInformation($"Validation successful for reassignment of appointment {appointmentId}");

                return ValidationResultDTO<(Appointment, User, User, Lead, Property)>.Success(
                    (appointment, newSeller, oldSeller, lead, property));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error during reassignment validation for appointment {appointmentId}");
                return ValidationResultDTO<(Appointment, User, User, Lead, Property)>.Fail("Validation failed due to system error");
            }
        }

        /// <summary>
        /// Validate business rules cho reassignment
        /// </summary>
        private async Task ValidateReassignmentBusinessRulesAsync(Appointment appointment, User newSeller, User oldSeller)
        {
            // ✅ Check appointment status
            if (appointment.status == AppointmentStatus.Completed)
                throw new InvalidOperationException("Cannot reassign seller for completed appointment");

            if (appointment.status == AppointmentStatus.Cancelled)
                throw new InvalidOperationException("Cannot reassign seller for cancelled appointment");

            // ✅ Check appointment date
            if (appointment.date <= DateTime.UtcNow)
                throw new InvalidOperationException("Cannot reassign seller for past appointments");

            // ✅ Check new seller availability
            //if (_sellerAvailabilityService != null)
            //{
            //    var isAvailable = await _sellerAvailabilityService.IsAvailableAsync(newSeller.id, appointment.date);
            //    if (!isAvailable)
            //        throw new BusinessException($"New seller {newSeller.fullName} is not available at {appointment.date:yyyy-MM-dd HH:mm}");
            //}

            // ✅ Check new seller workload
            //if (_sellerWorkloadService != null)
            //{
            //    var workload = await _sellerWorkloadService.GetWorkloadAsync(newSeller.id, appointment.date.Date);
            //    if (workload.appointmentCount >= 10)
            //        throw new BusinessException($"New seller {newSeller.fullName} has reached maximum appointments for this day");
            //}

            // ✅ Business rule: Không cho phép reassign quá gần ngày hẹn (ví dụ: 24 tiếng)
            if (appointment.date <= DateTime.UtcNow.AddHours(24))
                throw new InvalidOperationException("Cannot reassign seller within 24 hours of appointment time");
        }
        #endregion

        #region Update Methods for Reassignment

        /// <summary>
        /// Perform atomic updates cho reassignment
        /// </summary>
        private async Task<Appointment> PerformReassignmentUpdatesAsync(
            Appointment appointment,
            User newSeller,
            User oldSeller,
            Lead lead,
            Property property,
            ReassignSellerRequest request)
        {
            var currentUserId = _currentUserService.GetUserId();
            var now = DateTime.UtcNow;

            // ✅ 1. Update appointment với seller mới
            var updatedAppointment = await UpdateAppointmentForReassignmentAsync(
                appointment, newSeller.id, oldSeller?.id, currentUserId, now, request.reason);

            // ✅ 2. Prepare other updates
            var otherUpdateTasks = new List<Task>();

            // ✅ Update lead assignment
            if (request.updateLeadAssignment)
            {
                otherUpdateTasks.Add(UpdateLeadForReassignmentAsync(lead, newSeller.id, oldSeller?.id, now));
            }

            // ✅ Update property assignment nếu cần
            if (request.updatePropertyAssignment && !string.IsNullOrEmpty(oldSeller?.id) && property.salerId == oldSeller.id)
            {
                otherUpdateTasks.Add(UpdatePropertyAssignmentAsync(property, newSeller.id, now));
            }

            // ✅ Execute updates in parallel
            if (otherUpdateTasks.Any())
            {
                await Task.WhenAll(otherUpdateTasks);
            }

            // ✅ Save all changes
            await _unitOfWork.SaveAsync();

            return updatedAppointment;
        }

        /// <summary>
        /// Update appointment cho reassignment
        /// </summary>
        private async Task<Appointment> UpdateAppointmentForReassignmentAsync(
            Appointment appointment,
            string newSellerId,
            string oldSellerId,
            string currentUserId,
            DateTime now,
            string reason)
        {
            var filter = Builders<Appointment>.Filter.Eq(x => x.id, appointment.id);
            var update = Builders<Appointment>.Update
                .Set(x => x.salerId, newSellerId)
                .Set(x => x.updatedAt, now)
                .Set(x => x.lastModifiedBy, currentUserId)
                .Set(x => x.calendarSyncStatus, CalendarSyncStatus.Pending) // Reset sync status
                .Push(x => x.statusHistory, new AppointmentStatusHistory
                {
                    previousStatus = appointment.status,
                    newStatus = appointment.status, // Status không đổi, chỉ đổi seller
                    changedAt = now,
                    changedBy = currentUserId,
                    reason = reason ?? "Seller reassignment",
                    notes = $"Reassigned from seller {oldSellerId} to seller {newSellerId}"
                });

            // ✅ Clear old seller's calendar event IDs
            //if (!string.IsNullOrEmpty(appointment.salerGoogleCalendarEventId))
            //{
            //    update = update.Set(x => x.salerGoogleCalendarEventId, ""); // Clear old seller's event
            //}

            var options = new FindOneAndUpdateOptions<Appointment>
            {
                ReturnDocument = ReturnDocument.After
            };

            var updatedAppointment = await _unitOfWork.Appointments.FindOneAndUpdateAsync(filter, update, options);

            if (updatedAppointment == null)
            {
                throw new InvalidOperationException($"Failed to update appointment {appointment.id}");
            }

            return updatedAppointment;
        }

        /// <summary>
        /// Update lead assignment cho reassignment
        /// </summary>
        private async Task UpdateLeadForReassignmentAsync(Lead lead, string newSellerId, string oldSellerId, DateTime now)
        {
            var filter = Builders<Lead>.Filter.Eq(x => x.id, lead.id);
            var updateBuilder = Builders<Lead>.Update.Set(x => x.updatedAt, now);

            // ✅ Remove old seller if exists
            //if (!string.IsNullOrEmpty(oldSellerId))
            //{
            //    updateBuilder = updateBuilder.PullFilter(x => x.assignedTo, a => a.id == oldSellerId);
            //}

            // ✅ Add new seller if not already assigned
            if (!lead.assignedTo.Any(x => x.id == newSellerId))
            {
                updateBuilder = updateBuilder.Push(x => x.assignedTo, new AssignedTo
                {
                    id = newSellerId,
                    assignedAt = DateOnly.FromDateTime(now)
                });
            }

            await _unitOfWork.Leads.UpdateOneAsync(filter, updateBuilder);
        }

        #endregion

        #region Background Tasks for Reassignment

        /// <summary>
        /// Trigger background tasks cho reassignment
        /// </summary>
        private async Task<BackgroundTaskResult> TriggerReassignmentBackgroundTasksAsync(
            Appointment updatedAppointment,
            User newSeller,
            User oldSeller,
            Lead lead,
            Property property)
        {
            var result = new BackgroundTaskResult();

            // ✅ Fire and forget background tasks
            var backgroundTask = Task.Run(async () =>
            {
                using var scope = _serviceScopeFactory.CreateScope();
                var scopedServices = scope.ServiceProvider;

                var tasks = new List<Task>
              {
                  //HandleReassignmentEmailNotificationAsync(scopedServices, updatedAppointment, newSeller, oldSeller, lead, property),
                  HandleReassignmentCalendarEventAsync(scopedServices, updatedAppointment, newSeller, oldSeller, lead, property)
              };

                var results = await Task.WhenAll(tasks.Select(async task =>
                {
                    try
                    {
                        await task;
                        return true;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Reassignment background task failed");
                        return false;
                    }
                }));

                return new { emailSuccess = results[0], calendarSuccess = results[1] };
            });

            // Track status
            result.calendarStatus = TaskStatus.Pending;
            result.emailStatus = TaskStatus.Pending;

            // Optional: Wait briefly for quick completion
            var completedTask = await Task.WhenAny(backgroundTask, Task.Delay(1000));
            if (completedTask == backgroundTask)
            {
                try
                {
                    var taskResult = await backgroundTask;
                    result.emailStatus = taskResult.emailSuccess ? TaskStatus.Success : TaskStatus.Failed;
                    result.calendarStatus = taskResult.calendarSuccess ? TaskStatus.Success : TaskStatus.Failed;
                }
                catch
                {
                    result.emailStatus = TaskStatus.Failed;
                    result.calendarStatus = TaskStatus.Failed;
                }
            }

            return result;
        }

        /// <summary>
        /// Handle calendar events cho reassignment
        /// </summary>
        private async Task HandleReassignmentCalendarEventAsync(
            IServiceProvider services,
            Appointment updatedAppointment,
            User newSeller,
            User oldSeller,
            Lead lead,
            Property property)
        {
            try
            {
                var calendarService = services.GetRequiredService<ICalendarIntegrationService>();
                var unitOfWork = services.GetRequiredService<IUnitOfWork>();

                _logger.LogInformation($"Handling calendar reassignment for appointment {updatedAppointment.id}");

                // ✅ Step 1: Delete old seller's calendar event nếu có
                if (oldSeller != null && !string.IsNullOrEmpty(updatedAppointment.salerGoogleCalendarEventId))
                {
                    _logger.LogInformation($"Deleting old seller calendar event: {updatedAppointment.salerGoogleCalendarEventId}");

                    try
                    {
                        await calendarService.DeleteCalendarEventForAppointmentAsync(updatedAppointment, oldSeller.id, updatedAppointment.salerGoogleCalendarEventId);
                        _logger.LogInformation($"Old seller calendar event deleted successfully");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, $"Failed to delete old seller calendar event, continuing with new event creation");
                    }
                }

                // ✅ Step 2: Create new seller's calendar event
                _logger.LogInformation($"Creating new seller calendar event for {newSeller.fullName}");
                var newSellerEventId = await calendarService.CreateCalendarEventForAppointmentAsync(updatedAppointment, newSeller.id);

                if (string.IsNullOrEmpty(newSellerEventId))
                {
                    throw new InvalidOperationException("Failed to create new seller calendar event");
                }

                // ✅ Step 3: Update user's calendar event với new seller
                bool userEventUpdated = false;
                if (!string.IsNullOrEmpty(updatedAppointment.userGoogleCalendarEventId))
                {
                    _logger.LogInformation($"Updating user calendar event with new seller attendee");
                    await UpdateUserCalendarEventAsync(calendarService, updatedAppointment);
                    userEventUpdated = true;
                }

                // ✅ Step 4: Update appointment với new event ID
                var syncStatus = userEventUpdated ? CalendarSyncStatus.FullySynced : CalendarSyncStatus.PartiallySynced;

                var filter = Builders<Appointment>.Filter.Eq(x => x.id, updatedAppointment.id);
                var update = Builders<Appointment>.Update
                    .Set(x => x.salerGoogleCalendarEventId, newSellerEventId)
                    .Set(x => x.calendarSyncStatus, syncStatus)
                    .Set(x => x.updatedAt, DateTime.UtcNow);

                await unitOfWork.Appointments.UpdateOneAsync(filter, update);
                await unitOfWork.SaveAsync();

                _logger.LogInformation($"Calendar reassignment completed for appointment {updatedAppointment.id}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to handle calendar reassignment for appointment {updatedAppointment.id}");
                throw;
            }
        }

        /// <summary>
        /// Handle email notifications cho reassignment
        /// </summary>
        //private async Task HandleReassignmentEmailNotificationAsync(
        //    IServiceProvider services,
        //    Appointment updatedAppointment,
        //    User newSeller,
        //    User oldSeller,
        //    Lead lead,
        //    Property property)
        //{
        //    try
        //    {
        //        // ✅ Send notifications to all parties
        //        var emailService = services.GetRequiredService<IEmailService>();

        //        var tasks = new List<Task>();

        //        // ✅ Notify customer về seller change
        //        tasks.Add(emailService.SendSellerReassignmentNotificationToCustomerAsync(
        //            updatedAppointment, newSeller, oldSeller, lead, property));

        //        // ✅ Notify new seller
        //        tasks.Add(emailService.SendAppointmentAssignmentNotificationAsync(
        //            updatedAppointment, newSeller, lead, property));

        //        // ✅ Notify old seller nếu có
        //        if (oldSeller != null)
        //        {
        //            tasks.Add(emailService.SendAppointmentReassignmentNotificationToOldSellerAsync(
        //                updatedAppointment, oldSeller, newSeller, lead, property));
        //        }

        //        await Task.WhenAll(tasks);

        //        _logger.LogInformation($"Reassignment email notifications sent for appointment {updatedAppointment.id}");
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError(ex, $"Failed to send reassignment email notifications for appointment {updatedAppointment.id}");
        //        throw;
        //    }
        //}

        #endregion

        private async Task<AppointmentResponse> ToAppointmentResponse(Appointment appointment)
        {
            User saler = null;
            User user = null;
            Property property = null;

            try
            {
                if (appointment.salerId != null && !string.IsNullOrEmpty(appointment.salerId))
                {
                    saler = await _unitOfWork.Users.GetByIdAsync(appointment.salerId);
                }

                if (appointment.userId != null && !string.IsNullOrEmpty(appointment.userId))
                {
                    user = await _unitOfWork.Users.GetByIdAsync(appointment.userId);
                }

                if (!string.IsNullOrEmpty(appointment.propertyId))
                {
                    property = await _unitOfWork.Properties.GetByIdAsync(appointment.propertyId);
                }

                return new AppointmentResponse
                {
                    id = appointment.id,
                    leadId = appointment.leadId,
                    saler = saler == null ? null : new AppointmentUserResponse
                    {
                        id = saler.id,
                        fullName = saler.fullName,
                        email = saler.email,
                        phoneNumber = saler.phoneNumber
                    },
                    customer = user == null ? null : new AppointmentUserResponse
                    {
                        id = user.id,
                        fullName = user.fullName,
                        email = user.email,
                        phoneNumber = user.phoneNumber
                    },
                    messages = appointment.messages,
                    propertyId = appointment.propertyId,
                    status = appointment.status,
                    date = appointment.date,
                    createdAt = appointment.createdAt,
                    location = property?.location?.address
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"Lỗi khi convert cuộc hẹn: {ex.Message}");
            }
        }
    }
}
