﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using FirebaseAdmin;
using FirebaseAdmin.Messaging;
using Microsoft.AspNetCore.SignalR;
using MongoDB.Driver;
using Services.Implements.HubService;
using Services.Interfaces;
using System.Collections.Concurrent;
using Twilio;
using Twilio.Rest.Api.V2010.Account;

namespace Services.Implements
{
    public class NotificationService : INotificationService
    {
        private readonly IEmailService _emailService;
        private readonly IMongoCollection<BusinessObjects.Models.Notification> _notificationCollection;
        private readonly string _twilioAccountSid;
        private readonly string _twilioAuthToken;
        private readonly string _twilioPhoneNumber;
        private readonly IHubContext<NotificationHub> _hubContext;
        private static ConcurrentDictionary<string, string> SellerConnections = new ConcurrentDictionary<string, string>();
        public NotificationService(
            IEmailService emailService,
            RevoLandDbContext dbContext,
            string twilioAccountSid,
            string twilioAuthToken,
            string twilioPhoneNumber,
            string firebaseCredentialsPath,
            IHubContext<NotificationHub> hubContext
            )
        {
            _emailService = emailService ?? throw new ArgumentNullException(nameof(emailService));
            _notificationCollection = dbContext.Notifications;
            _twilioAccountSid = twilioAccountSid ?? throw new ArgumentNullException(nameof(twilioAccountSid));
            _twilioAuthToken = twilioAuthToken ?? throw new ArgumentNullException(nameof(twilioAuthToken));
            _twilioPhoneNumber = twilioPhoneNumber ?? throw new ArgumentNullException(nameof(twilioPhoneNumber));

            TwilioClient.Init(_twilioAccountSid, _twilioAuthToken);

            if (!string.IsNullOrEmpty(firebaseCredentialsPath) && FirebaseApp.DefaultInstance == null)
            {
                FirebaseApp.Create(new AppOptions
                {
                    Credential = Google.Apis.Auth.OAuth2.GoogleCredential.FromFile(firebaseCredentialsPath)
                });
            }

            _hubContext = hubContext;
        }



        public async Task SendEmailAsync(string email, string subject, string message)
        {
            if (string.IsNullOrEmpty(email)) return;

            try
            {
                await _emailService.SendEmailAsync(email, subject, message);

                await _notificationCollection.InsertOneAsync(new BusinessObjects.Models.Notification
                {
                    userId = email,
                    type = NotificationType.email,
                    content = message,
                    sentAt = DateTime.UtcNow,
                    isDelivered = true
                });
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to send email to {email}: {ex.Message}", ex);
            }
        }

        public async Task SendSmsAsync(string phoneNumber, string message)
        {
            if (string.IsNullOrEmpty(phoneNumber)) return;

            try
            {
                var smsMessage = await MessageResource.CreateAsync(
                    body: message,
                    from: new Twilio.Types.PhoneNumber(_twilioPhoneNumber),
                    to: new Twilio.Types.PhoneNumber(phoneNumber)
                );

                if (smsMessage.ErrorCode != null)
                {
                    throw new Exception($"Failed to send SMS: {smsMessage.ErrorMessage}");
                }

                await _notificationCollection.InsertOneAsync(new BusinessObjects.Models.Notification
                {
                    userId = phoneNumber,
                    type = NotificationType.sms,
                    content = message,
                    sentAt = DateTime.UtcNow,
                    isDelivered = true
                });
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to send SMS to {phoneNumber}: {ex.Message}", ex);
            }
        }

        public async Task SendInAppNotificationAsync(string userId, string message)
        {
            if (string.IsNullOrEmpty(userId)) return;

            try
            {
                var fcmMessage = new FirebaseAdmin.Messaging.Message
                {
                    Notification = new FirebaseAdmin.Messaging.Notification
                    {
                        Title = "QuindLand Notification",
                        Body = message
                    },
                    Topic = $"user_{userId}"
                };

                await FirebaseMessaging.DefaultInstance.SendAsync(fcmMessage);

                await _notificationCollection.InsertOneAsync(new BusinessObjects.Models.Notification
                {
                    userId = userId,
                    type = NotificationType.inApp,
                    content = message,
                    sentAt = DateTime.UtcNow,
                    isDelivered = true
                });
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to send in-app notification to user {userId}: {ex.Message}", ex);
            }
        }

        public async Task SendNotificationAsync(string email, string phoneNumber, string userId, string message)
        {
            var tasks = new List<Task>();

            if (!string.IsNullOrEmpty(email))
            {
                tasks.Add(SendEmailAsync(email, "QuindLand Notification", message));
            }

            if (!string.IsNullOrEmpty(phoneNumber))
            {
                tasks.Add(SendSmsAsync(phoneNumber, message));
            }

            if (!string.IsNullOrEmpty(userId))
            {
                tasks.Add(SendInAppNotificationAsync(userId, message));
            }

            await Task.WhenAll(tasks);
        }


        public async Task SendNotificationToSellerAsync(string sellerId, string message)
        {
            if (SellerConnections.TryGetValue(sellerId, out var connectionId))
            {
                await _hubContext.Clients.Client(connectionId).SendAsync("ReceiveNotification", message);
            }
        }

        public void RegisterSeller(string sellerId, string connectionId)
        {
            SellerConnections[sellerId] = connectionId;
        }

        public void RemoveSeller(string connectionId)
        {
            var sellerId = SellerConnections.FirstOrDefault(x => x.Value == connectionId).Key;
            if (sellerId != null)
            {
                SellerConnections.TryRemove(sellerId, out _);
            }
        }
    }
}
