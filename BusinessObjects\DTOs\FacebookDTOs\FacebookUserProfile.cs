﻿using System.Text.Json.Serialization;

namespace BusinessObjects.DTOs.FacebookDTOs
{
    public class FacebookUserProfile
    {
        [JsonPropertyName("id")]
        public string id { get; set; }

        [JsonPropertyName("first_name")]
        public string firstName { get; set; }

        [<PERSON>son<PERSON>ropertyName("last_name")]
        public string lastName { get; set; }

        [JsonPropertyName("name")]
        public string name { get; set; }

        [JsonPropertyName("profile_pic")]
        public string profilePic { get; set; }
    }
}
