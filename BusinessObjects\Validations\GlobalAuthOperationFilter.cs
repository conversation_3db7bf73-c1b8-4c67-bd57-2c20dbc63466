﻿using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

public class GlobalAuthOperationFilter : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        if (operation.Parameters == null)
            operation.Parameters = new List<OpenApiParameter>();

        // Nếu chưa có Security Requirement thì thêm vào
        if (operation.Security == null)
            operation.Security = new List<OpenApiSecurityRequirement>();

        // Tự động thêm Security Requirement vào tất cả APIs
        operation.Security.Add(new OpenApiSecurityRequirement
        {
            {
                new OpenApiSecurityScheme
                {
                    Reference = new OpenApiReference
                    {
                        Type = ReferenceType.SecurityScheme,
                        Id = "oauth2"  // ✅ Giữ đúng ID bạn đang dùng (oauth2)
                    }
                },
                new string[] {}
            }
        });
    }
}
