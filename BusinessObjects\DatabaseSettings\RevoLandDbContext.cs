using BusinessObjects.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static BusinessObjects.Models.User;
using Microsoft.Extensions.Options;

namespace BusinessObjects.DatabaseSettings
{
    public class RevoLandDbContext 
    {
        private readonly IMongoDatabase _database;

        public RevoLandDbContext(IConfiguration configuration)
        {
            var connectionString = configuration.GetConnectionString("MongoDb");
            var databaseName = configuration["ConnectionStrings:DatabaseName"];

            var client = new MongoClient(connectionString);
            _database = client.GetDatabase(databaseName);

            Contracts = _database.GetCollection<Contract>(nameof(Contracts));
            RentalContractReminders = _database.GetCollection<RentalContractReminder>(nameof(RentalContractReminders));
            RentalContractNotifications = _database.GetCollection<RentalContractNotification>(nameof(RentalContractNotifications));
            Matchings = _database.GetCollection<Matching>("Matchings");
            Swipes = _database.GetCollection<Swipe>("Swipes");
            RoommatePreferences = _database.GetCollection<RoommatePreference>("RoommatePreferences");
        }
        public IMongoCollection<T> GetCollection<T>(string collectionName)
        {
            return _database.GetCollection<T>(collectionName);
        }

        public IMongoCollection<User> Users => _database.GetCollection<User>("Users");
        public IMongoCollection<RefreshToken> RefreshTokens => _database.GetCollection<RefreshToken>("RefreshTokens");
        public IMongoCollection<Contract> Contracts { get; private set; }
        public IMongoCollection<RentalContractReminder> RentalContractReminders { get; private set; }
        public IMongoCollection<RentalContractNotification> RentalContractNotifications { get; private set; }
        public IMongoCollection<Matching> Matchings { get; private set; }
        public IMongoCollection<Swipe> Swipes { get; private set; }
        public IMongoCollection<RoommatePreference> RoommatePreferences { get; private set; }
        public IMongoDatabase GetDatabase() => _database;

        public IMongoCollection<Property> Properties => _database.GetCollection<Property>("Properties");
        public IMongoCollection<Reminder> Reminders => _database.GetCollection<Reminder>("Reminders");
        public IMongoCollection<Notification> Notifications =>_database.GetCollection<Notification>("Notifications");
        public IMongoCollection<AnonymousSession> AnonymousSessions => _database.GetCollection<AnonymousSession>("AnonymousSessions");
        public IMongoCollection<ChatSession> ChatSessions => _database.GetCollection<ChatSession>("ChatSessions");
        public IMongoCollection<Company> Companies => _database.GetCollection<Company>("Companies");
        public IMongoCollection<Team> Teams => _database.GetCollection<Team>("Teams");
        public IMongoCollection<TeamAgentLink> TeamAgentLinks => _database.GetCollection<TeamAgentLink>("TeamAgentLinks");
        public IMongoCollection<Agent> Agents => _database.GetCollection<Agent>("Agents");
		public IMongoCollection<AgentCompanyLink> CompanyAgentLinks => _database.GetCollection<AgentCompanyLink>("CompanyAgentLinks");
		public IMongoCollection<RefreshCompanyToken> RefreshCompanyTokens => _database.GetCollection<RefreshCompanyToken>("RefreshCompanyTokens");
        public IMongoCollection<AgentActivityLog> AgentActivityLogs => _database.GetCollection<AgentActivityLog>("AgentActivityLogs");
	}
}