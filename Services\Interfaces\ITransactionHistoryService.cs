﻿using BusinessObjects.DTOs.TransactionHistoryDTO;
using BusinessObjects.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Services.Interface
{
    public interface ITransactionHistoryService
    {
        Task<IEnumerable<TransactionHistoryCreateResponse>> GetAllTransactionHistoriesAsync();
        Task<TransactionHistoryCreateResponse> GetTransactionHistoryByIdAsync(string id);
        Task<TransactionHistoryCreateResponse> AddTransactionHistoryAsync(TransactionHistoryCreateRequest transactionHistory);
        Task<TransactionHistoryUpdateResponse> UpdateTransactionHistoryAsync(string id, TransactionHistoryUpdateRequest transactionHistory);
        Task<bool> DeleteTransactionHistoryAsync(string id);
    }
}
