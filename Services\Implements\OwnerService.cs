﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.OwnerDTOs;
using BusinessObjects.Models;
using MongoDB.Bson;
using Repositories.Implements;
using Repositories.Interfaces;
using Services.Interface;
using Services.Interfaces;
using Services.Tools;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Services.Implements
{
    public class OwnerService : IOwnerService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly FirebaseService _firebaseService;
        private readonly IUserService _userService;
        private readonly UserPasswordHasher _userPasswordHasher;

        public OwnerService(IUnitOfWork unitOfWork, FirebaseService firebaseService, IUserService userService, UserPasswordHasher userPasswordHasher)
        {
            _unitOfWork = unitOfWork;
            _firebaseService = firebaseService;
            _userService = userService;
            _userPasswordHasher = userPasswordHasher;
        }

        public async Task<IEnumerable<OwnerCreateResponse>> GetAllOwnersAsync()
        {
            var owners = await _unitOfWork.Owners.GetAllAsync();
            var rsList = new List<OwnerCreateResponse>();
            foreach (var owner in owners)
            {
                rsList.Add(ToResponseOwner(owner));
            }
            return rsList;
        }

        public async Task<OwnerCreateResponse> GetOwnerByIdAsync(string id)
        {
            var owner = await _unitOfWork.Owners.GetByIdAsync(id); 
            if(owner == null)
            {
                throw new InvalidDataException("Owner's not found");
            }
            return ToResponseOwner(owner);
        }


        private OwnerCreateResponse ToResponseOwner (Owner owner)
        {
            var response = new OwnerCreateResponse
            {
                address = owner.address,
                createdAt = owner.createdAt,
                email = owner.email,
                gender = owner.gender.HasValue ? owner.gender.Value.ToString() : Gender.Other.ToString(),
                idVerification = owner.idVerification,
                name = owner.name,
                nationality = owner.nationality,
                phone = owner.phone,
                updatedAt = owner.updatedAt,
                userId = owner.userId
            };
            return response;
        }

        public async Task<OwnerCreateResponse> AddOwnerAsync(OwnerCreatedRequest owner)
        {
            Owner rs = CreateToOwner(owner);

            var idVerificationFile = owner.idVerificationFile;
            string idVerificationUrl = "";
            using (var stream = idVerificationFile.OpenReadStream())
            {
                idVerificationUrl = await _firebaseService.UploadIdVerificationAsync(stream, Guid.NewGuid() + Path.GetExtension(idVerificationFile.FileName));
            }
            rs.idVerification = idVerificationUrl;

            // create user account for owner 

            var existPhoneUser = await _userService.FindUserByPhonenumberAsync(owner.phone);

            if(existPhoneUser != null)
            {
                rs.userId = existPhoneUser.id;
                await _unitOfWork.Owners.AddAsync(rs);
                await _unitOfWork.SaveAsync();
                return ToResponseOwner(rs);
            }
            else
            {
                Random random = new Random();
                string userName = owner.name.Replace(" ", "") + random.Next(1, 1000);

                var userConvert = new UserDTO
                {
                    email = owner.email,
                    fullName = owner.name,
                    joinedAt = DateTime.Now,
                    phoneNumber = owner.phone,
                    role = UserRole.User,
                    status = Status.Invisible,
                    userName = userName,
                    about = "This is about me.",
                    password = "@Revoland123"
                };

                var password = _userPasswordHasher.HashPassword(userConvert, userConvert.password);

                var user = new User
                {
                    email = owner.email,
                    fullName = owner.name,
                    joinedAt = DateTime.Now,
                    phoneNumber = owner.phone,
                    role = UserRole.User,
                    status = Status.Invisible,
                    userName = userName,
                    about = "This is about me.",
                    password = password
                };

                var createdUser = await _unitOfWork.Users.AddAsync(user);

                rs.userId = createdUser.id;
                var createdOwner = await _unitOfWork.Owners.AddAsync(rs);
                await _unitOfWork.SaveAsync();
                return ToResponseOwner(rs);
            }
           
        }

        private Owner CreateToOwner(OwnerCreatedRequest owner)
        {
            var owner_convert = new Owner
            {
                createdAt = DateTime.UtcNow,
                address = owner.address,
                email = owner.email,
                name = owner.name,
                nationality = owner.nationality,
                phone = owner.phone,
                updatedAt = DateTime.UtcNow
            };
            if(owner.gender == null)
            {
                owner.gender = Gender.Other;
            }
            owner_convert.gender = owner.gender;
            return owner_convert;
        }

        public async Task<OwnerCreateResponse> UpdateOwnerAsync(string id, OwnerUpdateRequest request)
        {
            var owner = await _unitOfWork.Owners.GetByIdAsync(id);
            if (owner == null)
            {
                throw new InvalidDataException("Owner's not found");
            }

            if (!string.IsNullOrWhiteSpace(request.name))
            {
                owner.name = request.name;
            }

            if (!string.IsNullOrWhiteSpace(request.phone))
            {
                owner.phone = request.phone;
            }

            if (!string.IsNullOrWhiteSpace(request.email))
            {
                owner.email = request.email;
            }

            if (!string.IsNullOrWhiteSpace(request.address))
            {
                owner.address = request.address;
            }

            if (request.gender.HasValue)
            {
                owner.gender = request.gender.Value;
            }

            if (!string.IsNullOrWhiteSpace(request.nationality))
            {
                owner.nationality = request.nationality;
            }

            if (request.idVerificationFile != null)
            {
                var idVerificationFile = request.idVerificationFile;
                string idVerificationUrl = "";
                using (var stream = idVerificationFile.OpenReadStream())
                {
                    idVerificationUrl = await _firebaseService.UploadIdVerificationAsync(stream, Guid.NewGuid() + Path.GetExtension(idVerificationFile.FileName));
                }
                owner.idVerification = idVerificationUrl;

            }

            await _unitOfWork.Owners.UpdateAsync(id, owner);
            await _unitOfWork.SaveAsync();
            return ToResponseOwner(owner);
        }



        public async Task<bool> DeleteOwnerAsync(string id)
        {
            var owner = await _unitOfWork.Owners.GetByIdAsync(id);
            if (owner == null)
            {
                throw new InvalidDataException("Owner's not found");
            }
            var properties = await _unitOfWork.Properties.GetByIdAsync(id);
            await _unitOfWork.Owners.DeleteAsync(id);
            await _unitOfWork.SaveAsync();
            return true;
        }
        public async Task<OwnerCreateResponse> GetOwnerByPhoneAsync(string phone)
        {
            var owner = await _unitOfWork.Owners.GetOwnerByPhoneAsync(phone);
            if(owner == null)
            {
                throw new InvalidDataException($"No owner's matched {phone}");
            }
            return ToResponseOwner(owner);
        }
        
    }
}
