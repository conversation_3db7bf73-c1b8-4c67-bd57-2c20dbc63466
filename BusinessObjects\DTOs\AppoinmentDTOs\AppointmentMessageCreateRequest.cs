﻿using BusinessObjects.Models;
using BusinessObjects.Validations;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.DTOs.AppoinmentDTOs
{
    public class AppointmentMessageCreateRequest
    {

        [Required]
        public string content { get; set; }
        public DateTime createdAt { get; set; } = DateTime.Now;
    }
}
