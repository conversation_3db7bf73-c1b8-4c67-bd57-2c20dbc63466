﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.RoommatePreferenceDTOs;
using BusinessObjects.DTOs.UserDTOs;
using BusinessObjects.Models;
using BusinessObjects.QueryObject;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Services.Helpers;
using Services.Implements;
using Services.Interfaces;

namespace Controllers.Controllers
{

    [Route("api/users")]
    [ApiController]
    public class UserController : ControllerBase
    {

        private readonly IUserService _userService;
        private readonly ISearchService _searchService;
        private readonly FirebaseService _firebaseService;
        private readonly IRoommatePreferenceService _roommatePreferenceService;

        public UserController(IUserService userService, FirebaseService firebaseService, ISearchService searchService, IRoommatePreferenceService roommatePreferenceService)
        {
            _userService = userService;
            _firebaseService = firebaseService;
            _searchService = searchService;
            _roommatePreferenceService = roommatePreferenceService;
        }

        [HttpGet("seller")]
        public async Task<IActionResult> GetAllSellers([FromQuery] QuerySeller query)
        {
            try
            {
                var result = await _userService.GetSellersAsync(query);
                return Ok(CreateResponse(200, true, "Thành công", result));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(400, false, "Thất bại: " + ex.Message));
            }
        }

        private ApiResponse CreateResponse(int code, bool status, string message, object data = null)
        {
            return new ApiResponse { code = code, status = status, message = message, data = data };
        }
        [Authorize]
        [HttpPut("update-profile")]
        public async Task<IActionResult> EditUserAccount([FromForm] UserUpdateRequest user, IFormFile? avatarFile)
        {
            var currentUserId = User.Claims.FirstOrDefault(c => c.Type == "UserID")?.Value;
            if (string.IsNullOrEmpty(currentUserId))
            {
                return BadRequest(CreateResponse(400, false, "Người dùng chưa được xác thực"));
            }

            var result = await _userService.UpdateUserProfileAsync(currentUserId, user, avatarFile);
            return StatusCode(result.statusCode, CreateResponse(result.statusCode, result.success, result.message));
        }


        [HttpGet("profile")]
        [Authorize]
        public async Task<IActionResult> GetUserInformation()
        {
            try
            {
                var currentUserId = User.Claims.FirstOrDefault(c => c.Type == "UserID")?.Value;
                if (string.IsNullOrEmpty(currentUserId))
                {
                    return BadRequest(CreateResponse(400, false, "Người dùng chưa được xác thực"));
                }

                var user = await _userService.GetUserProfileAsync(currentUserId);
                if (user == null)
                {
                    return NotFound(CreateResponse(404, false, "Không tìm thấy người dùng"));
                }

                return Ok(CreateResponse(200, true, "Thành công", user));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(400, false, "Thất bại: " + ex.Message));
            }
        }

        [Authorize]
        [HttpGet("me/roommate-info")]
        public async Task<IActionResult> GetUserRoomateInfo()
        {
			try
			{
				var currentUserId = User.Claims.FirstOrDefault(c => c.Type == "UserID")?.Value;
				if (string.IsNullOrEmpty(currentUserId))
				{
					return BadRequest(CreateResponse(400, false, "Người dùng chưa được xác thực"));
				}

				var user = await _userService.GetUserRoomateInfoAsync(currentUserId);
				if (user == null)
				{
					return NotFound(CreateResponse(404, false, "Không tìm thấy người dùng"));
				}

				return Ok(CreateResponse(200, true, "Thành công", user));
			}
			catch (Exception ex)
			{
				return BadRequest(CreateResponse(400, false, "Thất bại: " + ex.Message));
			}
		}

		[Authorize]
		[HttpPut("me/roommate-info")]
		public async Task<IActionResult> UpdateUserRoomateInfo([FromBody] UserUpdateRoomateInfoRequest request)
		{
			try
			{
				var currentUserId = User.Claims.FirstOrDefault(c => c.Type == "UserID")?.Value;
				if (string.IsNullOrEmpty(currentUserId))
				{
					return BadRequest(CreateResponse(400, false, "Người dùng chưa được xác thực"));
				}

				var user = await _userService.UpdateUserRoomateInfoAsync(currentUserId, request);
				if (user == null)
				{
					return NotFound(CreateResponse(404, false, "Không tìm thấy người dùng"));
				}

				return Ok(CreateResponse(200, true, "Thành công", user));
			}
			catch (KeyNotFoundException knfEx)
			{
				return NotFound(CreateResponse(404, false, knfEx.Message));
			}
			catch (Exception ex)
			{
				return StatusCode(500, CreateResponse(500, false, "Lỗi hệ thống: " + ex.Message));
			}
		}

        [Authorize]
        [HttpPatch("me/roommate/toggle")]
        public async Task<IActionResult> ToggleRoommate([FromBody] ToggleRoommateRequest request)
        {
            try
            {
                var currentUserId = User.Claims.FirstOrDefault(c => c.Type == "UserID")?.Value;
                if (string.IsNullOrEmpty(currentUserId))
                {
                    return BadRequest(CreateResponse(400, false, "Người dùng chưa được xác thực"));
                }

                var result = await _userService.ToggleRoommateAsync(currentUserId, request.isActive);
                return StatusCode(result.statusCode, CreateResponse(result.statusCode, result.success, result.message));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateResponse(500, false, "Lỗi hệ thống: " + ex.Message));
            }
        }

        [Authorize]
        [HttpGet("me/roommate-preference")]
        public async Task<IActionResult> GetRoommatePreference()
        {
            try
            {
                var currentUserId = User.Claims.FirstOrDefault(c => c.Type == "UserID")?.Value;
                if (string.IsNullOrEmpty(currentUserId))
                {
                    return BadRequest(CreateResponse(400, false, "Người dùng chưa được xác thực"));
                }

                var preference = await _roommatePreferenceService.GetRoommatePreferenceByUserIdAsync(currentUserId);

                if (preference == null)
                {
                    return Ok(CreateResponse(
                        code: 404,
                        status: false,
                        message: "Không tìm thấy tiêu chí bạn ở ghép"
                    ));
                }

                return Ok(CreateResponse(
                    code: 200,
                    status: true,
                    message: "Lấy tiêu chí bạn ở ghép thành công",
                    data: preference
                ));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateResponse(500, false, "Lỗi hệ thống: " + ex.Message));
            }
        }

        [Authorize]
        [HttpPut("me/roommate-preference")]
        public async Task<IActionResult> UpdateRoommatePreference([FromBody] RoommatePreferenceUpdateRequest request)
        {
            try
            {
                var currentUserId = User.Claims.FirstOrDefault(c => c.Type == "UserID")?.Value;
                if (string.IsNullOrEmpty(currentUserId))
                {
                    return BadRequest(CreateResponse(400, false, "Người dùng chưa được xác thực"));
                }

				var updatedPreference = await _roommatePreferenceService.UpdateRoommatePreferenceAsync(currentUserId, request);

				return Ok(CreateResponse(
					code: 200,
					status: true,
					message: "Cập nhật tiêu chí bạn ở ghép thành công",
					data: updatedPreference
				));
			}
			catch (Exception ex)
			{
				return StatusCode(500, CreateResponse(500, false, "Lỗi hệ thống: " + ex.Message));
			}
		}

		[Authorize]
        [HttpGet("role-matching")]
		public async Task<IActionResult> GetRoleMatching()
		{
			try
			{
				var list = EnumHelper.GetEnumList<RoleMatching>();
				return Ok(CreateResponse(200, true, "Thành công", list));
			}
			catch (Exception ex)
			{
				return StatusCode(500, CreateResponse(500, false, "Lỗi hệ thống: " + ex.Message));
			}
		}

		[Authorize]
		[HttpGet("sleep-habit")]
		public async Task<IActionResult> GetSleepHabit()
		{
			try
			{
				var list = EnumHelper.GetEnumList<SleepHabit>();
				return Ok(CreateResponse(200, true, "Thành công", list));
			}
			catch (Exception ex)
			{
				return StatusCode(500, CreateResponse(500, false, "Lỗi hệ thống: " + ex.Message));
			}
		}

		[Authorize]
		[HttpGet("lifestyle")]
		public async Task<IActionResult> GetLifestyle()
		{
			try
			{
				var list = EnumHelper.GetEnumList<Lifestyle>();
				return Ok(CreateResponse(200, true, "Thành công", list));
			}
			catch (Exception ex)
			{
				return StatusCode(500, CreateResponse(500, false, "Lỗi hệ thống: " + ex.Message));
			}
		}

		[Authorize]
		[HttpGet("occupation-type")]
		public async Task<IActionResult> GetOccupationType()
		{
			try
			{
				var list = EnumHelper.GetEnumList<OccupationType>();
				return Ok(CreateResponse(200, true, "Thành công", list));
			}
			catch (Exception ex)
			{
				return StatusCode(500, CreateResponse(500, false, "Lỗi hệ thống: " + ex.Message));
			}
		}

		[Authorize]
		[HttpGet("interest-type")]
		public async Task<IActionResult> GetInterestType()
		{
			try
			{
				var list = EnumHelper.GetEnumList<InterestType>();
				return Ok(CreateResponse(200, true, "Thành công", list));
			}
			catch (Exception ex)
			{
				return StatusCode(500, CreateResponse(500, false, "Lỗi hệ thống: " + ex.Message));
			}
		}
	}
}

