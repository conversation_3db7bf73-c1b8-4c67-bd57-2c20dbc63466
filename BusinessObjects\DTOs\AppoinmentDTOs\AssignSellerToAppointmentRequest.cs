﻿using BusinessObjects.Validations;
using System.ComponentModel.DataAnnotations;

namespace BusinessObjects.DTOs.AppoinmentDTOs
{
    public class AssignSellerToAppointmentRequest
    {
        [ValidObjectId]
        [Required]
        public string sellerId { get; set; }
        public bool assignPropertyToSeller { get; set; } = false; // ✅ Explicit choice

        public string? notes { get; set; }


    }
}
