﻿using BusinessObjects.Models;
using System.ComponentModel.DataAnnotations;

namespace BusinessObjects.DTOs.AppoinmentDTOs
{
    public class CancelAppointmentRequest
    {
        /// <summary>
        /// Reason for cancelling the appointment
        /// </summary>
        [MaxLength(500, ErrorMessage = "Cancellation reason must not exceed 500 characters")]
        public string? cancellationReason { get; set; }

        /// <summary>
        /// Type of cancellation (optional - will be auto-determined based on user role)
        /// </summary>
        public CancellationType? cancellationType { get; set; }

        /// <summary>
        /// Additional notes about the cancellation
        /// </summary>
        [MaxLength(1000, ErrorMessage = "Notes must not exceed 1000 characters")]
        public string? notes { get; set; }

        /// <summary>
        /// Related support ticket ID (if applicable)
        /// </summary>
        [MaxLength(50, ErrorMessage = "Related ticket ID must not exceed 50 characters")]
        public string? relatedTicketId { get; set; }
    }

    // ✅ Success Response Model
    public class CancelAppointmentResponse
    {
        public bool success { get; set; }
        public string message { get; set; }
        public string appointmentId { get; set; }
        public DateTime cancelledAt { get; set; }
        public string calendarEventDeletionStatus { get; set; } // "Processing", "Completed", "Failed"
        public DateTime timestamp { get; set; }
    }

    // ✅ Cancellation Details Response
    public class CancellationDetailsResponse
    {
        public string appointmentId { get; set; }
        public bool isCancelled { get; set; }
        public string? message { get; set; }
        public CancellationInfoDto? cancellationInfo { get; set; }
    }
    // ✅ DTOs for response
    public class CancellationInfoDto
    {
        public string reason { get; set; }
        public DateTime cancelledAt { get; set; }
        public string cancelledBy { get; set; }
        public string type { get; set; }
        public string? notes { get; set; }
        public bool isAutomatic { get; set; }
        public string? relatedTicketId { get; set; }
        public CalendarDeletionInfoDto calendarDeletion { get; set; }
    }
    public class CalendarDeletionInfoDto
    {
        public bool isDeleted { get; set; }
        public DateTime? deletedAt { get; set; }
        public bool userEventDeleted { get; set; }
        public bool salerEventDeleted { get; set; }
        public string? deletionStatus { get; set; }
        public string? failureReason { get; set; }
        public int retryCount { get; set; }
    }

}
