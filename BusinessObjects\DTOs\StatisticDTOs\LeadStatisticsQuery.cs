﻿using BusinessObjects.Models;

namespace BusinessObjects.DTOs.StatisticDTOs
{
    public class LeadsStatisticsQuery
    {
        // Date range filters
        public DateTime? dateFrom { get; set; }
        public DateTime? dateTo { get; set; }

        // Quick date filters (boolean shortcuts)
        public bool today { get; set; } = false;
        public bool yesterday { get; set; } = false;
        public bool thisWeek { get; set; } = false;
        public bool thisMonth { get; set; } = false;
        public bool last7Days { get; set; } = false;
        public bool last30Days { get; set; } = false;

        // Lead-specific filters
        public LeadSource? source { get; set; }
        public LeadScore? score { get; set; }
        public string? assignedTo { get; set; }



    }
}
