﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.OwnerDTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Services.Interface;

namespace API.Controllers
{
    [Route("api/owners")]
    [ApiController]
    public class OwnerController : ControllerBase
    {
        private readonly IOwnerService _ownerService;

        public OwnerController(IOwnerService ownerService)
        {
            _ownerService = ownerService;
        }

        private ApiResponse CreateResponse(int code, bool status, string message, object data = null)
        {
            return new ApiResponse { code = code, status = status, message = message, data = data };
        }
        [Authorize]
        [HttpGet]
        public async Task<ActionResult<List<OwnerCreateResponse>>> GetAll()
        {
            var owners = await _ownerService.GetAllOwnersAsync();
            return Ok(CreateResponse(200, true, "Fetched all owners successfully", owners));
        }
        [Authorize]
        [HttpGet("{id}")]
        public async Task<ActionResult<OwnerCreateResponse>> GetById(string id)
        {
            try
            {
                var owner = await _ownerService.GetOwnerByIdAsync(id);
                return Ok(CreateResponse(200, true, "Fetched owner successfully", owner));
            }
            catch (InvalidDataException ex)
            {
                return NotFound(CreateResponse(404, false, ex.Message));
            }
        }
        [Authorize]
        [HttpPost]
        public async Task<ActionResult<OwnerCreateResponse>> AddOwner([FromForm] OwnerCreatedRequest owner)
        {
            try
            {
                var newOwner = await _ownerService.AddOwnerAsync(owner);
                return Ok(CreateResponse(201, true, "Owner created successfully", newOwner));
            }
            catch (InvalidDataException ex)
            {
                return NotFound(CreateResponse(404, false, ex.Message));
            }
        }

        [Authorize]
        [HttpPut("{id}")]
        public async Task<ActionResult<OwnerCreateResponse>> Update(string id, [FromForm] OwnerUpdateRequest updatedOwner)
        {
            try
            {
                var result = await _ownerService.UpdateOwnerAsync(id, updatedOwner);
                return Ok(CreateResponse(200, true, "Owner updated successfully", result));
            }
            catch (InvalidDataException ex)
            {
                return NotFound(CreateResponse(404, false, ex.Message));
            }
            catch (InvalidOperationException ex)
            {
                return Conflict(CreateResponse(409, false, ex.Message));
            }

        }
        [Authorize]
        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(string id)
        {
            try
            {
                var result = await _ownerService.DeleteOwnerAsync(id);
                return Ok(CreateResponse(200, true, "Owner deleted successfully"));
            }
            catch (InvalidDataException ex)
            {
                return NotFound(CreateResponse(404, false, ex.Message));
            }
            catch (InvalidOperationException ex)
            {
                return Conflict(CreateResponse(409, false, ex.Message));
            }
        }
        [Authorize]
        [HttpGet("phone")]
        public async Task<ActionResult<OwnerCreateResponse>> GetOwnerByPhone(string phone)
        {
            try
            {
                var owner = await _ownerService.GetOwnerByPhoneAsync(phone);
                if (owner == null)
                    return NotFound(CreateResponse(404, false, "Owner not found"));
                return Ok(CreateResponse(200, true, "Owner found", owner));
            }
            catch (InvalidDataException ex)
            {
                return NotFound(CreateResponse(404, false, ex.Message));
            }
            catch (InvalidOperationException ex)
            {
                return Conflict(CreateResponse(409, false, ex.Message));
            }
        }
    }
}
