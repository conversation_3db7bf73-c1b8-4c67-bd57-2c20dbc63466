﻿using MongoDB.Bson;

namespace BusinessObjects.Extensions
{
    public static class ValidationExtensions
    {
        public static void ValidateObjectId(this string id, string paramName)
        {
            if (string.IsNullOrEmpty(id))
                throw new ArgumentException($"{paramName} cannot be null or empty.", paramName);

            if (!ObjectId.TryParse(id, out _))
                throw new ArgumentException($"Invalid ObjectId format: {id}", paramName);
        }

        public static ObjectId ToObjectId(this string id, string paramName = "id")
        {
            id.ValidateObjectId(paramName);
            return ObjectId.Parse(id);
        }
    }
}
