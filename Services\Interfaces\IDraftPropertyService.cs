﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.DraftPropertyDTOs;

namespace Services.Interfaces
{
    public interface IDraftPropertyService
    {
        Task AddDraftProperty(CreateDraftPropertyRequest request, string userId);
        Task<PagedResult<DraftPropertyResponse>> GetDraftPropertiesByUserAsync(string userId, string role, int pageSize, int currentPage);
        Task DeleteByIdAsync(string userId, string propertyId);
        Task<DraftPropertyResponse> GetDraftPropertyByIdAsync(string userId, string propertyId);

    }
}
