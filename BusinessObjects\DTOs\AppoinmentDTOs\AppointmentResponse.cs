﻿using BusinessObjects.Models;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.DTOs.AppoinmentDTOs
{
    public class AppointmentResponse
    {
        public string id { get; set; }
        public string leadId { get; set; }
        public AppointmentUserResponse saler { get; set; }
        public AppointmentUserResponse customer { get; set; }
        public List<AppointmentMessage> messages { get; set; } = new List<AppointmentMessage>();
        public string propertyId { get; set; }
        public string location { get; set; }
        public AppointmentStatus status { get; set; }
        public DateTime date { get; set; }
        public DateTime createdAt { get; set; } = DateTime.UtcNow;
    }

    public class AppointmentUserResponse
    {
        public string id { get; set; }
        public string fullName { get; set; }
        public string email { get; set; }
        public string phoneNumber { get; set; }
    }
}
