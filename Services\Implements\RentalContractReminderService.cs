using BusinessObjects.Models;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using MongoDB.Driver;
using Repositories.Interfaces;
using Services.Interface;
using Services.Interfaces;
using Services.Tools;

namespace Services.Implements
{
    public class RentalContractReminderService : IRentalContractReminderService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IRentalContractNotificationService _notificationService;
        private readonly ILogger<RentalContractReminderService> _logger;
        private readonly IEmailService _emailService;
        private readonly IUserService _userService;
        private readonly ITenantService _tenantService;
        private readonly IPropertyService _propertyService;

        public RentalContractReminderService(
            IUnitOfWork unitOfWork,
            IRentalContractNotificationService notificationService,
            ILogger<RentalContractReminderService> logger,
            IEmailService emailService,
            IUserService userService,
            ITenantService tenantService,
            IPropertyService propertyService)
        {
            _unitOfWork = unitOfWork;
            _notificationService = notificationService;
            _logger = logger;
            _emailService = emailService;
            _userService = userService;
            _tenantService = tenantService;
            _propertyService = propertyService;
        }

        public async Task CheckExpiringContractsAsync(int daysBeforeExpiry)
        {
            var now = DateTime.UtcNow;
            var expiryDate = now.AddDays(daysBeforeExpiry);

            var expiringContracts = await _unitOfWork.Contracts.GetExpiringContractsAsync(daysBeforeExpiry);
            
            foreach (var contract in expiringContracts)
            {
                var daysLeft = (contract.contractEndDate - now).Value.Days;
                
                // Lấy tên của property
                string propertyName = "property";
                try
                {
                    var property = await _propertyService.GetPropertyByIdAsync(contract.propertyId, null, null);
                    if (property != null)
                    {
                        propertyName = property.name;
                    }
                }
                catch { }

                string message = $"Your rental contract for {propertyName} is expiring in {daysLeft} days. Please take action.";

                // Check if notification for this timeframe has already been sent
                bool is90Days = daysBeforeExpiry == 90 && !contract.sentNoti90Days;
                bool is60Days = daysBeforeExpiry == 60 && !contract.sentNoti60Days;
                bool is30Days = daysBeforeExpiry == 30 && !contract.sentNoti30Days;

                // Use the same SentNoti properties for email notifications to avoid duplicate sending
                bool isEmail90Days = daysBeforeExpiry == 90 && !contract.sentNoti90Days;
                bool isEmail60Days = daysBeforeExpiry == 60 && !contract.sentNoti60Days;
                bool isEmail30Days = daysBeforeExpiry == 30 && !contract.sentNoti30Days;

                if (is90Days || is60Days || is30Days)
                {
                    await CreateReminderAsync(contract, contract.tenantId, message, now.AddDays(1));
                    if (!string.IsNullOrEmpty(contract.salerId))
                    {
                        await CreateReminderAsync(contract, contract.salerId, message, now.AddDays(1));
                    }

                    if (is90Days)
                        contract.sentNoti90Days = true;
                    else if (is60Days)
                        contract.sentNoti60Days = true;
                    else if (is30Days)
                        contract.sentNoti30Days = true;

                    contract.updatedAt = DateTime.UtcNow;
                    await _unitOfWork.Contracts.UpdateAsync(contract.id.ToString(), contract);
                }

                if (isEmail90Days || isEmail60Days || isEmail30Days)
                {
                    // Get tenant and saler information for email
                    var tenant = await _tenantService.GetTenantByIdAsync(contract.tenantId);
                    if (tenant != null && !string.IsNullOrEmpty(tenant.email))
                    {
                        string tenantName = "Tenant";
                        string tenantEmailContent = EmailTemplate.GetLeaseRenewalReminderEmail(tenantName, contract.contractEndDate.Value, daysLeft, contract.id.ToString(), "https://your-web-app-url.com");
                        await _emailService.SendEmailAsync(tenant.email, "Rental Contract Expiry Reminder", tenantEmailContent);
                    }

                    if (!string.IsNullOrEmpty(contract.salerId))
                    {
                        var saler = await _userService.FindUserByIdAsync(contract.salerId);
                        if (saler != null && !string.IsNullOrEmpty(saler.email))
                        {
                            string salerEmailContent = EmailTemplate.GetLeaseRenewalReminderEmail(saler.fullName, contract.contractEndDate.Value, daysLeft, contract.id.ToString(), "https://your-web-app-url.com");
                            await _emailService.SendEmailAsync(saler.email, "Rental Contract Expiry Reminder", salerEmailContent);
                        }
                    }

                    // No need to update SentNoti properties again as they are already updated above
                    contract.updatedAt = DateTime.UtcNow;
                    await _unitOfWork.Contracts.UpdateAsync(contract.id.ToString(), contract);
                }
            }
        }

        public async Task CreateReminderAsync(Contract contract, string userId, string message, DateTime remindAt)
        {
            // Kiểm tra nếu userId là tenantId, tìm UserId tương ứng dựa trên email
            string finalUserId = userId;
            if (contract.tenantId == userId)
            {
                var tenant = await _tenantService.GetTenantByIdAsync(userId);
                if (tenant != null && !string.IsNullOrEmpty(tenant.email))
                {
                    var user = await _userService.FindUserByEmailAsync(tenant.email);
                    if (user != null)
                    {
                        finalUserId = user.id;
                    }
                }
            }

            var reminder = new RentalContractReminder
            {
                id = ObjectId.GenerateNewId().ToString(),
                userId = finalUserId,
                contractId = contract.id.ToString(),
                title = "Rental Contract Expiry Reminder",
                message = message,
                expiringDate = contract.contractEndDate.Value,
            };
            await _unitOfWork.RentalContractReminder.CreateReminderAsync(reminder);
        }

        public async Task<List<RentalContractReminder>> GetRemindersToNotifyAsync()
        {
            var reminders = await _unitOfWork.RentalContractReminder.GetRemindersToNotifyAsync();
            return reminders;
        }

        public async Task SendReminderAsync(RentalContractReminder reminder)
        {
            await _notificationService.SendNotificationAsync(reminder.userId, reminder.message, "RentalContractReminder");
            await _unitOfWork.RentalContractReminder.SendReminderAsync(reminder);
        }

        public async Task<List<RentalContractReminder>> GetRemindersByUserIdAsync(string userId)
        {
            var reminders = await _unitOfWork.RentalContractReminder.GetRemindersByUserIdAsync(userId);
            return reminders;
        }

        public async Task CheckRentDueRemindersAsync(int daysBeforeDue)
        {
            var currentDate = DateTime.UtcNow;
            var reminderDate = currentDate.AddDays(daysBeforeDue);

            var contracts = await _unitOfWork.Contracts.GetAllAsync();
            var dueContracts = contracts
                .Where(c => !c.isDeleted && c.rentDueDate.Value.Date >= currentDate.Date && c.rentDueDate.Value.Date <= reminderDate.Date && !c.sentRentReminder)
                .ToList();

            foreach (var contract in dueContracts)
            {
                var tenant = await _tenantService.GetTenantByIdAsync(contract.tenantId);
                if (tenant != null && !string.IsNullOrEmpty(tenant.email))
                {
                    var emailSubject = "Nhắc nhở đóng tiền thuê nhà";
                    var emailBody = EmailTemplate.GetRentDueReminderEmail(tenant.name ?? "Tenant", contract.rentDueDate.Value);

                    await _emailService.SendEmailAsync(tenant.email, emailSubject, emailBody);

                    // Cập nhật trường SentRentReminder để tránh gửi email trùng lặp
                    contract.sentRentReminder = true;
                    contract.updatedAt = DateTime.UtcNow;
                    await _unitOfWork.Contracts.UpdateAsync(contract.id.ToString(), contract);
                }
            }
        }

        public async Task ResetRentRemindersAsync()
        {
            var currentDate = DateTime.UtcNow;
            var contracts = await _unitOfWork.Contracts.GetAllAsync();
            var contractsToReset = contracts
                .Where(c => !c.isDeleted && (c.rentDueDate.Value.Date < currentDate.Date || c.isRentPaid))
                .ToList();

            foreach (var contract in contractsToReset)
            {
                contract.sentRentReminder = false;
                contract.isRentPaid = false; // Đặt lại trạng thái thanh toán cho chu kỳ tiếp theo
                contract.updatedAt = DateTime.UtcNow;
                await _unitOfWork.Contracts.UpdateAsync(contract.id.ToString(), contract);
            }
        }
    }
}