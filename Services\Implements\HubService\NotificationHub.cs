﻿using Microsoft.AspNetCore.SignalR;
using Services.Interfaces;

namespace Services.Implements.HubService
{
    public class NotificationHub : Hub
    {
        private readonly INotificationService _notificationService;

        public NotificationHub(INotificationService notificationService)
        {
            _notificationService = notificationService;
        }

        public async Task RegisterSeller(string sellerId)
        {
            _notificationService.RegisterSeller(sellerId, Context.ConnectionId);
            await Task.CompletedTask;
        }

        public override async Task OnDisconnectedAsync(Exception exception)
        {
            _notificationService.RemoveSeller(Context.ConnectionId);
            await base.OnDisconnectedAsync(exception);
        }
    }



}
