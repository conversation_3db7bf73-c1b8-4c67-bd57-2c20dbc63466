using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.Settings
{
    public class ESmsSettings
    {
        public string ApiKey { get; set; }
        public string SecretKey { get; set; }
        public string SendSmsEndpoint { get; set; } = "http://rest.esms.vn/MainService.svc/json/SendMultipleMessage_V4_post_json/";
        public string Brandname { get; set; }
        public int SmsType { get; set; } = 2; // 2: Tin CSKH
        public bool UseSandbox { get; set; } = false;
        public bool UseFixedCostSms { get; set; } = false;
    }
} 