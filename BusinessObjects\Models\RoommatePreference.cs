﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace BusinessObjects.Models
{
	public class RoommatePreference
	{
		[BsonId]
		[BsonRepresentation(BsonType.ObjectId)]
		public string id { get; set; } = ObjectId.GenerateNewId().ToString();

		public string userId { get; set; }

		[BsonRepresentation(BsonType.String)]
		public Gender preferredGender { get; set; }
		public int? ageMin { get; set; }
		public int? ageMax { get; set; }
		public bool? allowPets { get; set; }
		public bool? allowSmoking { get; set; }

		[BsonRepresentation(BsonType.String)]
		public List<OccupationType>? occupation { get; set; }
		[BsonRepresentation(BsonType.String)]
		public List<Lifestyle>? preferredLifestyles { get; set; }
        public List<InterestType>? interests { get; set; }
        public DateTime updatedAt { get; set; } = DateTime.UtcNow;
	}
}
