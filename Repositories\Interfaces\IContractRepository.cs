using BusinessObjects.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Repositories.Interfaces
{
    public interface IContractRepository : IGenericRepository<Contract>
    {
        Task<IEnumerable<Contract>> GetActiveContractsAsync();
        Task<IEnumerable<Contract>> GetExpiringContractsAsync(int daysToExpire);
        Task<List<Contract>> GetExpiringContractsAsync(DateTime expirationDate);
        Task<Contract> AddRenewalLogAsync(string contractId, RenewalLog renewalLog);
        Task<Contract> SoftDeleteAsync(string id);
        Task<IEnumerable<Contract>> GetDeletedContractsAsync();
        Task<Contract> RestoreContractAsync(string id);
    }
} 