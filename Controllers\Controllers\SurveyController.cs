using Microsoft.AspNetCore.Mvc;
using Services.Interface;
using BusinessObjects.DTOs;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.IdentityModel.JsonWebTokens;

[Route("api/surveys")]
[ApiController]
public class SurveyController : ControllerBase
{
    private readonly ISurveyService _surveyService;

    public SurveyController(ISurveyService surveyService)
    {
        _surveyService = surveyService;
    }

    private ApiResponse CreateResponse(int code, bool status, string message, object data = null)
    {
        return new ApiResponse { code = code, status = status, message = message, data = data };
    }

    [HttpPost]
    public async Task<IActionResult> AddSurvey([FromForm] SurveyCreateRequest surveyRequest)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(CreateResponse(400, false, "Invalid survey data", ModelState));
        }

        var createdSurvey = await _surveyService.AddSurveyAsync(surveyRequest);
        return Ok(CreateResponse(201, true, "Survey submitted successfully", createdSurvey));
    }

    [Authorize]
    [HttpGet]
    public async Task<IActionResult> GetAllSurveys()
    {
        var surveys = await _surveyService.GetAllSurveysAsync();
        return Ok(CreateResponse(200, true, "Fetched surveys successfully", surveys));
    }

    [Authorize]
    [HttpGet("{id}")]
    public async Task<IActionResult> GetSurveyById(string id)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(CreateResponse(400, false, "Invalid request", ModelState));
        }

        var survey = await _surveyService.GetSurveyByIdAsync(id);
        if (survey == null)
        {
            return NotFound(CreateResponse(404, false, "Survey not found"));
        }

        return Ok(CreateResponse(200, true, "Survey retrieved successfully", survey));
    }

    [Authorize]
    [HttpPost("{id}/convert-to-lead")]
    public async Task<IActionResult> ConvertToLead(string id)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(CreateResponse(400, false, "Invalid request", ModelState));
        }

        var adminId = User.FindFirst("UserID")?.Value;
        if (string.IsNullOrEmpty(adminId))
        {
            return Unauthorized(CreateResponse(401, false, "Unable to identify admin user."));
        }

        var updatedSurvey = await _surveyService.ConvertToLeadAsync(id, adminId);
        if (updatedSurvey == null)
        {
            return NotFound(CreateResponse(404, false, "Survey not found"));
        }

        return Ok(CreateResponse(200, true, "Survey converted to lead successfully", updatedSurvey));
    }
} 