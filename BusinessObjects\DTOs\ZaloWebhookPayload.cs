﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace BusinessObjects.DTOs
{
    public class ZaloWebhookPayload
    {
        [JsonPropertyName("app_id")]
        public string? appId { get; set; }

        [JsonPropertyName("event_name")]
        public string? eventName { get; set; }

        [JsonPropertyName("sender")]
        public ZaloSender? sender { get; set; }

        [JsonPropertyName("recipient")]
        public ZaloRecipient? recipient { get; set; }

        [JsonPropertyName("message")]
        public ZaloMessage? message { get; set; }

        [JsonPropertyName("timestamp")]
        public string? timestamp { get; set; }
    }

    public class ZaloSender
    {
        [JsonPropertyName("id")]
        public string id { get; set; }
    }

    public class ZaloRecipient
    {
        [JsonPropertyName("id")]
        public string id { get; set; }
    }

    public class ZaloMessage
    {
        [JsonPropertyName("text")]
        public string? text { get; set; }

        [JsonPropertyName("msg_id")]
        public string? msgId { get; set; }

        [JsonPropertyName("attachments")]
        public List<ZaloAttachment>? attachments { get; set; }
    }

    public class ZaloAttachment
    {
        [JsonPropertyName("type")]
        public string? type { get; set; } 

        [JsonPropertyName("payload")]
        public Dictionary<string, object>? payload { get; set; }
    }

}
