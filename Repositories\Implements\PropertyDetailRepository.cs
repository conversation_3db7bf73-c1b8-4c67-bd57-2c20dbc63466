﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Repositories.Implements
{
    public class PropertyDetailRepository : GenericRepository<PropertyDetail>, IPropertyDetailRepository
    {
        private readonly RevoLandDbContext _context;
        public PropertyDetailRepository(RevoLandDbContext context) : base(context, "Property_details")
        {
        }
    }
}
