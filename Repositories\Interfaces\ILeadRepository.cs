﻿using BusinessObjects.Models;

namespace Repositories.Interfaces
{
    public interface ILeadRepository : IGenericRepository<Lead>
    {
        Task<Lead?> GetLeadByEmailAsync(string email);
        Task<Lead?> GetLeadByPhoneAsync(string phone);
        Task<List<Lead>> GetLeadsByScoreAsync(LeadScore score);
        Task<List<Lead>> GetLeadsBySalerIdAsync(string salerId);
        Task<Lead> GetLeadByUserIdAsync(string userId);
        Task<Boolean> CheckExistedLeadForSeller(string sellerId, string phone);
        IQueryable<Lead> GetAll();
    }
}
