﻿using BusinessObjects.DTOs;
using BusinessObjects.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NPOI.SS.Formula.Functions;
using Services.Interfaces;
using Swashbuckle.AspNetCore.Annotations;
using System.ComponentModel;

namespace Controllers.Controllers
{
    [Route("api/chat-session/")]
    [ApiController]
    public class ChatController : ControllerBase
    {
        private readonly IChatService _chatService;

        public ChatController(IChatService chatService)
        {
            _chatService = chatService;
        }

        private ApiResponse CreateResponse(int code, bool status, string message, object data = null)
        {
            return new ApiResponse
            {
                code = code,
                status = status,
                message = message,
                data = data
            };
        }
 
        [HttpPost("start")]
        [AllowAnonymous]
        public async Task<IActionResult> StartChatSession() 
        {
            try
            {
                var userId = User.Claims.FirstOrDefault()?.Value;
                var chatSession = await _chatService.StartChatSessionAsync(userId);

                if (chatSession.status == "anonymous")
                {
                    Response.Cookies.Append(
                        "AnonymousSessionId",
                        chatSession.anonymousSessionId,
                        new CookieOptions
                        {
                            HttpOnly = true,
                            Secure = true,
                            SameSite = SameSiteMode.Strict,
                            Expires = DateTimeOffset.UtcNow.AddDays(7)
                        }
                    );
                }
                return Ok(CreateResponse(201, true, "Start chat session successfully", chatSession));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateResponse(500, false, "An error occurred while starting chat session", ex.Message));
            }
        }

        [HttpPost("send")]
        [AllowAnonymous]
        public async Task<IActionResult> SendMessage([FromQuery] string sessionId, [FromBody] String content)
        {
            try
            {
                var senderId = User.Claims.FirstOrDefault()?.Value;

                var senderType = User.IsInRole("Admin") ? SenderType.Admin :
                                User.IsInRole("Saler") ? SenderType.Saler : SenderType.User;


                await _chatService.SendMessageAsync(sessionId, senderId, content, senderType);
                return Ok(CreateResponse(201, true, "Send message successfully", content));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateResponse(500, false, "An error occurred while sending message", ex.Message));
            }
        }

        [HttpGet("session")]
        [AllowAnonymous]
        public async Task<IActionResult> GetChatSession([FromQuery] string sessionId)
        {
            try
            {
                var chatSession = await _chatService.GetChatSessionAsync(sessionId);
                if (chatSession == null)
                    return NotFound();
                return Ok(CreateResponse(201, true, " Get chat session successfully", chatSession));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateResponse(500, false, "An error occurred while Send message", ex.Message));
            }
        }

        [HttpGet("user-sessions")]
        [Authorize]
        public async Task<IActionResult> GetUserChatSessions()
        {
            try
            {
                var currentUserId = User.Claims.FirstOrDefault()?.Value;
                if (string.IsNullOrEmpty(currentUserId))
                {
                    return BadRequest(CreateResponse(401, false, "User ID not found in token"));
                }

                var sessions = await _chatService.GetUserChatSessionsAsync(currentUserId);
                return Ok(CreateResponse(200, true, "Get user chat sessions successfully", sessions));
            }
            catch (ArgumentException ex)
            {
                return BadRequest(CreateResponse(400, false, ex.Message));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateResponse(500, false, "An error occurred while getting user chat sessions", ex.Message));
            }
        }

        [HttpGet("all-sessions")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> GetAllChatSessions()
        {
            try
            {
                var sessions = await _chatService.GetAllChatSessionsAsync();
                return Ok(CreateResponse(200, true, "Get all chat sessions successfully", sessions));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateResponse(500, false, "An error occurred while getting all chat sessions", ex.Message));
            }
        }
    }
}
