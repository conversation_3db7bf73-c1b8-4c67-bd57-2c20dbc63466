﻿namespace BusinessObjects.DTOs.AppoinmentDTOs
{
    public class ReassignSellerRequest
    {
        public string newSellerId { get; set; }
        public bool updateLeadAssignment { get; set; } = true; // Whether to update lead assignment to new seller
        public bool updatePropertyAssignment { get; set; } = true; // Whether to update property assignment to new seller

        public string reason { get; set; } = string.Empty; // Reason for reassignment, for audit trail and status history
    }
}
