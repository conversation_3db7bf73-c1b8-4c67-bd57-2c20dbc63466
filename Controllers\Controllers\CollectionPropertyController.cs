﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.CollectionDTOs;
using BusinessObjects.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using MongoDB.Bson;
using Services.Implements;
using Services.Interfaces;

namespace Controllers.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class CollectionPropertyController : ControllerBase
    {
        private readonly ICollectionPropertyService _collectionPropertyService;

        public CollectionPropertyController(ICollectionPropertyService collectionPropertyService)
        {
            _collectionPropertyService = collectionPropertyService ?? throw new ArgumentNullException(nameof(collectionPropertyService));
        }
        private ApiResponse CreateResponse(int code, bool status, string message, object data = null)
        {
            return new ApiResponse { code = code, status = status, message = message, data = data };
        }
        /// <summary>
        /// Create a new collection for current user
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("")]
        [Authorize(Roles = "User,Admin")]
        public async Task<IActionResult> CreateCollection([FromBody] CollectionCreateRequest request)
        {
            try
            {
                var collection = await _collectionPropertyService.CreateCollectionAsync(request);
                return Ok(CreateResponse(201, true, "Thành công", collection));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(500, false, ex.Message));
            }
        }

        /// <summary>
        /// Get all collections in database
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Authorize(Roles = "User,Admin")]
        public async Task<IActionResult> GetAllCollections()
        {
            try
            {
                var collections = await _collectionPropertyService.GetAllCollectionsAsync();
                return Ok(CreateResponse(200, true, "Lấy danh sách thành công", collections));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(500, false, ex.Message));
            }
        }
        /// <summary>
        /// Get Collection by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>

        [HttpGet("id/{id}")]
        [Authorize(Roles = "User,Admin")]
        public async Task<IActionResult> GetCollectionById(string id)
        {
            try
            {
                var collection = await _collectionPropertyService.GetCollectionByIdAsync(id);
                if (collection == null)
                {
                    return NotFound(CreateResponse(404, false, "Không tìm thấy Collection"));
                }
                return Ok(CreateResponse(200, true, "Lấy Collection thành công", collection));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(500, false, ex.Message));
            }
        }
        /// <summary>
        /// Get Collection by user id
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetAllByUser")]
        [Authorize(Roles = "User,Admin")]
        public async Task<IActionResult> GetCollectionByUserId()
        {
            try
            {
                var collection = await _collectionPropertyService.GetCollectionByUserIdAsync();
                if (collection == null)
                {
                    return NotFound(CreateResponse(404, false, "Không tìm thấy Collection"));
                }
                return Ok(CreateResponse(200, true, "Lấy Collection thành công", collection));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(500, false, ex.Message));
            }
        }
        /// <summary>
        /// Delete a collection by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete("{id}")]
        [Authorize(Roles = "User,Admin")]
        public async Task<IActionResult> DeleteCollection(string id)
        {
            try
            {
                var collection = await _collectionPropertyService.DeleteCollectionAsync(id);
                return Ok(CreateResponse(200, true, "Xoá Collection thành công", collection));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(500, false, ex.Message));
            }
        }
        /// <summary>
        /// Update a collection by id
        /// </summary>
        /// <param name="id"></param>
        /// <param name="updateRequest"></param>
        /// <returns></returns>
        [HttpPut("{id}")]
        [Authorize(Roles = "User,Admin")]
        public async Task<IActionResult> UpdateCollection(string id, [FromBody] CollectionUpdateRequest updateRequest)
        {
            try
            {
               var collection = await _collectionPropertyService.UpdateCollectionAsync(id, updateRequest);
                return Ok(CreateResponse(200, true, "Cập nhật Collection thành công", collection));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(500, false, ex.Message));
            }
        }
        /// <summary>
        /// Add a property to a collection
        /// </summary>
        /// <param name="collectionId"></param>
        /// <param name="propertyId"></param>
        /// <returns></returns>
        [HttpPost("add-property")]
        [Authorize(Roles = "User,Admin")]
        public async Task<IActionResult> AddPropertyToCollection([FromQuery] string collectionId, [FromQuery] string propertyId)
        {
            try
            {
                var updatedCollection = await _collectionPropertyService.AddPropertyToCollection(collectionId, propertyId);
                return Ok(CreateResponse(200, true, "Thêm thuộc tính vào Collection thành công", updatedCollection));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(500, false, ex.Message));
            }
        }

        /// <summary>
        /// Remove a property from a collection
        /// </summary>
        /// <param name="collectionId"></param>
        /// <param name="property"></param>
        /// <returns></returns>
        [HttpDelete("remove-property")]
        [Authorize(Roles = "User,Admin")]
        public async Task<IActionResult> RemovePropertyFromCollection([FromQuery] string collectionId, [FromQuery] string property)
        {
            try
            {
                var updatedCollection = await _collectionPropertyService.RemovePropertyFromCollection(collectionId, property);
                return Ok(CreateResponse(200, true, "Xoá thuộc tính khỏi Collection thành công", updatedCollection));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(500, false, ex.Message));
            }
        }

    }
}
