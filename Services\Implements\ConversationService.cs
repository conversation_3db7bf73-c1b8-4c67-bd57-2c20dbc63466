﻿using BusinessObjects.DTOs.ConversationDTOs;
using BusinessObjects.Models;
using BusinessObjects.QueryObject;
using Microsoft.AspNetCore.Http;
using Repositories.Interfaces;
using Services.Interfaces;
using System.Security.Claims;

namespace Services.Implements
{
    public class ConversationService : IConversationService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public ConversationService(IUnitOfWork unitOfWork, IHttpContextAccessor httpContextAccessor)
        {
            _unitOfWork = unitOfWork;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task<List<GroupedConversationsResponse>> GetAllConversationsAsync(QueryConversation query)
        {
            var conversations = await _unitOfWork.Conversations.GetAllConversationsAsync(query);
            var groupedConversations = new Dictionary<string, List<ConversationResponse>>();

            foreach (var conversation in conversations)
            {
                PlatformUser platformUser = null;

                if (conversation.platform == "zalo" || conversation.platform == "messenger")
                {
                    var customerProfile = await _unitOfWork.CustomerProfiles.GetByIdAsync(conversation.userId);
                    platformUser = customerProfile != null
                        ? new PlatformUser
                        {
                            id = customerProfile.id.ToString(),
                            name = customerProfile.displayName,
                            avatarUrl = customerProfile.avatarUrl

                        }
                        : null;
                }
                else if (conversation.platform == "system")
                {
                    var user = await _unitOfWork.Users.GetByIdAsync(conversation.userId);
                    platformUser = user != null
                        ? new PlatformUser
                        {
                            id = user.id.ToString(),
                            name = user.fullName
                        }
                        : null;
                }

                var response = new ConversationResponse
                {
                    id = conversation.id.ToString(),
                    lastMessage = conversation.lastMessage,
                    lastUpdated = conversation.lastUpdated,
                    platform = conversation.platform,
                    platformUser = platformUser
                };

                if (!groupedConversations.ContainsKey(conversation.platform))
                {
                    groupedConversations[conversation.platform] = new List<ConversationResponse>();
                }

                groupedConversations[conversation.platform].Add(response);
            }

            return groupedConversations.Select(kv => new GroupedConversationsResponse
            {
                platform = kv.Key,
                conversations = kv.Value
            }).ToList();
        }

        public async Task<ConversationResponse> GetConversationByUserIdAsync()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            var userId = httpContext?.User?.FindFirstValue("UserID");
            var conversation = await _unitOfWork.Conversations.GetByUserIdAsync(userId);
            if (conversation == null)
            {
                return null;
            }
            var user = await _unitOfWork.Users.GetByIdAsync(userId);
            var platformUser = user != null
                ? new PlatformUser
                {
                    id = user.id.ToString(),
                    name = user.fullName
                }
                : null;
            var conversationResponse = new ConversationResponse
            {
                id = conversation.id.ToString(),
                lastMessage = conversation.lastMessage,
                lastUpdated = conversation.lastUpdated,
                platform = conversation.platform,
                platformUser = platformUser,
                pinMessages = conversation.pinMessages
            };
            return conversationResponse;
        }

        public async Task<List<ConversationResponse>> GetConversationByCurrentSellerAsync(string sellerId, QueryConversation query)
        {
            var foundSeller = await _unitOfWork.Users.GetByIdAsync(sellerId) ?? throw new Exception("Không tìm thấy seller");
            var conversations = await _unitOfWork.Conversations.GetConversationsByCurrentSellerAsync(sellerId, query);
            var conversationResponse = new List<ConversationResponse>();
            foreach (var conversation in conversations)
            {
                PlatformUser user = new PlatformUser();
                var foundUser = await _unitOfWork.Users.GetByIdAsync(conversation.userId) ?? throw new Exception("Không tìm thấy người dùng");
                user.name = foundUser.fullName;
                user.avatarUrl = foundUser.avatar;
                user.id = foundUser.id.ToString();
                var convo = new ConversationResponse
                {
                    id = conversation.id.ToString(),
                    pinMessages = conversation.pinMessages,
                    platform = conversation.platform,
                    lastMessage = conversation.lastMessage,
                    lastUpdated = conversation.lastUpdated,
                    platformUser = user,
                };
                conversationResponse.Add(convo);
            }
            return conversationResponse;
        }


        public async Task<Conversation> GetConversationByIdAsync(string id)
        {
            try
            {
                var conversation = await _unitOfWork.Conversations.GetByIdAsync(id);
                return conversation ?? throw new InvalidDataException("Không tìm thấy cuộc trò chuyện");
            }
            catch (Exception ex)
            {
                throw new Exception($"Lỗi khi lấy cuộc trò chuyện {ex.Message}");
            }
        }

        public async Task<Conversation> CreateConversationAsync(string userId, string sellerId, string content)
        {
            var newConversation = new Conversation
            {
                userId = userId,
                sellerId = sellerId,
                lastMessage = content,
                lastUpdated = DateTime.UtcNow,
                platform = "system",

            };
            newConversation = await _unitOfWork.Conversations.AddAsync(newConversation);
            await _unitOfWork.SaveAsync();
            return newConversation;
        }

        public async Task<Conversation> GetConversationByUserIdAndSellerIdAsync(string userId, string sellerId)
        {
            var conversation = await _unitOfWork.Conversations.GetByUserIdIdAndSellerIdAsync(userId, sellerId);
            return conversation;
        }
    }
}
