﻿using BusinessObjects.Models;
using System.ComponentModel.DataAnnotations;

namespace BusinessObjects.QueryObject
{
    public class QueryProperty
    {
        public string? searchTerm { get; set; }

        [Range(1, int.MaxValue, ErrorMessage = "Page number must be greater than or equal to 1.")]
        public int pageNumber { get; set; } = 1;

        [Range(1, int.MaxValue, ErrorMessage = "Page size must be greater than or equal to 1.")]
        public int pageSize { get; set; } = 10;

        public bool isDescending { get; set; } = false;

        // Basic filters
        public List<PropertyStatus>? status { get; set; } = new();
        public List<PropertyTransactionType>? transactionType { get; set; } = new();
        public List<PropertyType>? type { get; set; } = new();
        public bool? isFeatured { get; set; }
        public bool? isVerified { get; set; }

        /// <summary>
        /// Tọa độ latitude của vị trí cần tìm properties gần đó
        /// </summary>
        [Range(-90, 90, ErrorMessage = "Latitude phải trong khoảng -90 đến 90")]
        public double? latitude { get; set; }

        /// <summary>
        /// Tọa độ longitude của vị trí cần tìm properties gần đó
        /// </summary>
        [Range(-180, 180, ErrorMessage = "Longitude phải trong khoảng -180 đến 180")]
        public double? longitude { get; set; }

        /// <summary>
        /// Bán kính tìm kiếm tối đa (km). Mặc định 10km
        /// </summary>
        [Range(0.1, 50, ErrorMessage = "Bán kính phải từ 0.1km đến 50km")]
        public double maxRadius { get; set; } = 10.0;


        // Property detail filters
        public List<PropDetailBoolFilter>? propertyDetailFilters { get; set; }
        public List<AmenityFilter>? amenityFilters { get; set; }

        // Bedroom filters
        public int? minBedrooms { get; set; }
        public int? maxBedrooms { get; set; }

        // Bathroom filters
        public int? bathrooms { get; set; }
        public int? minBathrooms { get; set; }
        public int? maxBathrooms { get; set; }

        // Living room filters
        public int? livingRooms { get; set; }
        public int? minLivingRooms { get; set; }
        public int? maxLivingRooms { get; set; }

        // Kitchen filters
        public int? kitchens { get; set; }
        public int? minKitchens { get; set; }
        public int? maxKitchens { get; set; }

        // Land area filters
        public double? minLandArea { get; set; }
        public double? maxLandArea { get; set; }

        // Land width filters
        public double? landWidth { get; set; }
        public double? minLandWidth { get; set; }
        public double? maxLandWidth { get; set; }

        // Land length filters
        public double? landLength { get; set; }
        public double? minLandLength { get; set; }
        public double? maxLandLength { get; set; }

        // Building area filters
        public double? buildingArea { get; set; }
        public double? minBuildingArea { get; set; }
        public double? maxBuildingArea { get; set; }

        // Number of floors filters
        public int? numberOfFloors { get; set; }
        public int? minNumberOfFloors { get; set; }
        public int? maxNumberOfFloors { get; set; }

        // Floor number filters
        public int? floorNumber { get; set; }
        public int? minFloorNumber { get; set; }
        public int? maxFloorNumber { get; set; }
        public List<ApartmentOrientation>? apartmentOrientation { get; set; }


        // Price filters
        public double? minPrice { get; set; }
        public double? maxPrice { get; set; }

        // Sorting
        public PropertyEnumSortBy sortBy { get; set; } = PropertyEnumSortBy.name;

        public bool HasLocationCoordinates => latitude.HasValue && longitude.HasValue;
    }



    public enum PropDetailBoolFilter
    {
        hasBasement,
        furnished
    }

    public enum AmenityFilter
    {
        Parking,
        Elevator,
        SwimmingPool,
        Gym,
        SecuritySystem,
        AirConditioning,
        Balcony,
        Garden,
        Playground,
        BackupGenerator
    }

    public enum PropertyEnumSortBy
    {
        name,
        price,
        createdAt,
        updatedAt,
        distance
    }
}
