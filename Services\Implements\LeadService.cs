﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.LeadDTOs;
using BusinessObjects.Models;
using BusinessObjects.QueryObject;
using Microsoft.AspNetCore.Http;
using MongoDB.Driver;
using Repositories.Interfaces;
using Services.Interfaces;
using System.Security.Claims;

namespace Services.Implements
{
    public class LeadService : ILeadService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IUserRepository _userRepository;

        public LeadService(IUnitOfWork unitOfWork, IHttpContextAccessor httpContextAccessor, IUserRepository userRepository)
        {
            _unitOfWork = unitOfWork;
            _httpContextAccessor = httpContextAccessor;
            _userRepository = userRepository;
        }

        public async Task<LeadResponse?> GetLeadByIdAsync(string id)
        {
            var userId = _httpContextAccessor.HttpContext?.User?.FindFirstValue("UserID");
            var role = _httpContextAccessor.HttpContext?.User?.FindFirst(ClaimTypes.Role)?.Value;
            var lead = await _unitOfWork.Leads.GetByIdAsync(id) ?? throw new Exception("Lead not found");

            if ((lead.assignedTo == null || lead.assignedTo.Count == 0) && !role.Equals(UserRole.Admin))
            {
                throw new Exception("You do not have permission to access this lead");
            }
            var response = new LeadResponse();
            if (role == "Admin")
            {
                response.address = lead.address;
                response.name = lead.name;
                response.email = lead.email;
                response.score = lead.score;
                response.phone = lead.phone;
                response.id = lead.id;
                response.source = lead.source;
                var sellers = new List<LeadSeller>();

                if (lead.assignedTo != null && lead.assignedTo.Count != 0)
                {
                    foreach (var assignedUser in lead.assignedTo)
                    {
                        var user = await _userRepository.GetByIdAsync(assignedUser.id);
                        if (user != null)
                        {
                            sellers.Add(new LeadSeller
                            {
                                id = user.id,
                                name = user.fullName,
                                email = user.email,
                                phone = user.phoneNumber,
                                avatar = user.avatar,
                                about = user.about,
                                birthdate = user.birthdate
                            });
                        }
                    }
                }
                response.assignedTo = sellers;
                return response;
            }


            response.email = lead.email;
            response.score = lead.score;
            response.phone = lead.phone;
            response.id = lead.id;
            response.address = lead.address;
            response.name = lead.name;
            //source


            return response;
        }

        public async Task<List<Lead>> GetLeadsByScoreAsync(LeadScore score)
        {
            return await _unitOfWork.Leads.GetLeadsByScoreAsync(score);
        }

        public async Task AddLeadAsync(LeadCreateRequest request)
        {
            var role = _httpContextAccessor.HttpContext?.User?.FindFirst(ClaimTypes.Role)?.Value;
            if (role == "Admin")
            {
                var newLead = new Lead
                {
                    name = request.name,
                    email = request.email,
                    address = request.address,
                    phone = request.phone,
                    source = LeadSource.Admin,
                    score = request.score ?? null,
                    createdAt = DateTime.Now,
                    updatedAt = DateTime.Now,
                };
                await _unitOfWork.Leads.AddAsync(newLead);
            }
            else
            {
                var userId = _httpContextAccessor.HttpContext?.User?.FindFirstValue("UserID");
                var isExisted = await _unitOfWork.Leads.CheckExistedLeadForSeller(userId, request.phone);
                if (isExisted)
                {
                    throw new Exception($"This lead with phone number {request.phone} is in your data");
                }
                var newLead = new Lead
                {
                    name = request.name,
                    email = request.email,
                    address = request.address,
                    phone = request.phone,
                    source = LeadSource.ExternalSeller,
                    score = request.score,
                    assignedTo = new List<AssignedTo> { new AssignedTo() { id = userId } },
                    createdAt = DateTime.Now,
                    updatedAt = DateTime.Now,
                };
                await _unitOfWork.Leads.AddAsync(newLead);
            }
        }



        public async Task UpdateLeadAsync(string id, LeadUpdateRequest updatedLead)
        {
            var sellerId = _httpContextAccessor.HttpContext?.User?.FindFirstValue("UserID");
            var role = _httpContextAccessor.HttpContext?.User?.FindFirst(ClaimTypes.Role)?.Value;
            var foundLead = await _unitOfWork.Leads.GetByIdAsync(id);
            if (foundLead == null)
            {
                throw new Exception("Lead not found");
            }

            if (role == "Admin")
            {

            }
            else if (role == "Saler")
            {
                if (foundLead.assignedTo[0].ToString() != sellerId)
                {
                    throw new Exception("Something wrong happens");
                }
            }
            else
            {
                throw new Exception("Something wrong happens");
            }

            foundLead.name = updatedLead.name ?? foundLead.name;
            foundLead.email = updatedLead.email ?? foundLead.email;
            foundLead.phone = updatedLead.phone ?? foundLead.phone;
            foundLead.address = updatedLead.address ?? foundLead.address;
            await _unitOfWork.Leads.UpdateAsync(id, foundLead);
        }

        public async Task<Lead?> GetLeadByEmailAsync(string email)
        {
            return await _unitOfWork.Leads.GetLeadByEmailAsync(email);
        }

        public async Task<Lead?> GetLeadByPhoneAsync(string phone)
        {
            return await _unitOfWork.Leads.GetLeadByPhoneAsync(phone);
        }

        public async Task<Lead> DeleteLeadAsync(string id)
        {
            await GetLeadByIdAsync(id);
            return await _unitOfWork.Leads.DeleteAsync(id);
        }

        public async Task<Boolean> CheckExistedLeadByEmail(string email)
        {
            var lead = await _unitOfWork.Leads.GetLeadByEmailAsync(email);
            return lead != null;
        }

        public async Task AddFavoritePropertyAsync(AddFavoritePropertyRequest request)
        {
            var userId = _httpContextAccessor.HttpContext?.User?.FindFirstValue("UserID");
            var lead = await _unitOfWork.Leads.GetLeadByUserIdAsync(userId);

            if (lead == null)
            {
                var user = await _unitOfWork.Users.GetByIdAsync(userId);
                var newLead = new Lead
                {
                    name = user.fullName,
                    phone = user.phoneNumber,
                    email = user.email,
                    favoriteProperties = new List<AddFavoritePropertyRequest> { request },
                    userId = userId
                };
                await _unitOfWork.Leads.AddAsync(newLead);
                return;
            }

            if (lead.favoriteProperties.Any(fp => fp.propertyId == request.propertyId))
            {
                throw new Exception("This property is already in the favorites list.");
            }

            lead.favoriteProperties.Add(request);
            lead.updatedAt = DateTime.UtcNow;
            await _unitOfWork.Leads.UpdateAsync(lead.id, lead);
        }
        public async Task RemoveFavoritePropertyAsync(string propertyId)
        {
            var userId = _httpContextAccessor.HttpContext?.User?.FindFirstValue("UserID");

            var lead = await _unitOfWork.Leads.GetLeadByUserIdAsync(userId);

            var favoriteToRemove = lead.favoriteProperties
                .FirstOrDefault(fp => fp.propertyId == propertyId);

            if (favoriteToRemove == null)
            {
                throw new Exception("This property is not in the favorites list.");
            }

            lead.favoriteProperties.Remove(favoriteToRemove);
            lead.updatedAt = DateTime.UtcNow;
        }

        public async Task UnassignLeadToSellerAsync(string leadId, string sellerId)
        {
            // Validate input parameters
            if (string.IsNullOrEmpty(leadId))
                throw new ArgumentException("Lead ID cannot be null or empty", nameof(leadId));

            if (string.IsNullOrEmpty(sellerId))
                throw new ArgumentException("Seller ID cannot be null or empty", nameof(sellerId));

            // Get lead and seller concurrently
            var leadTask = _unitOfWork.Leads.GetByIdAsync(leadId);
            var sellerTask = _userRepository.GetByIdAsync(sellerId);

            await Task.WhenAll(leadTask, sellerTask);

            var lead = await leadTask;
            var seller = await sellerTask;

            // Validate seller
            if (seller == null)
                throw new InvalidDataException($"Seller with ID {sellerId} not found");

            if (seller.role != UserRole.Saler)
                throw new InvalidOperationException($"User {sellerId} is not a valid seller");

            // Validate lead
            if (lead == null)
                throw new InvalidDataException($"Lead with ID {leadId} not found");

            if (lead.assignedTo == null || lead.assignedTo.Count == 0)
                throw new InvalidOperationException("This lead does not have any assignee");

            // Check if seller is assigned to this lead
            var assignedSeller = lead.assignedTo.FirstOrDefault(a => a.id == sellerId) ?? throw new InvalidOperationException($"Lead {leadId} is not assigned to seller {sellerId}");

            // Remove the assignment
            lead.assignedTo.Remove(assignedSeller);
            lead.updatedAt = DateTime.UtcNow;

            // Update the lead
            await _unitOfWork.Leads.UpdateAsync(lead.id, lead);


        }
        public async Task<PagedResult<LeadResponse>> GetAllLeadsByCurrentUserAsync(QueryLead queryLead)
        {
            var currentUserId = _httpContextAccessor.HttpContext?.User?.FindFirstValue("UserID");
            var role = _httpContextAccessor.HttpContext?.User?.FindFirst(ClaimTypes.Role)?.Value;
            if (string.IsNullOrEmpty(currentUserId))
                throw new InvalidDataException("Current user ID not found.");

            if (role == UserRole.Admin.ToString())
                return await GetAllLeadsAsync(queryLead);
            else if (role == UserRole.Saler.ToString())
                return await GetLeadsBySalerIdAsync(currentUserId, queryLead);
            else
                throw new UnauthorizedAccessException("You do not have permission to access leads.");
        }
        public async Task<PagedResult<LeadResponse>> GetAllLeadsAsync(QueryLead query)
        {
            // Lấy tổng số leads trước khi filter (để tính totalCount chính xác)
            var totalCountBeforeFilter = await _unitOfWork.Leads.CountAsync();

            var leadsQuery = _unitOfWork.Leads.GetAll();

            // Apply filters tại database level
            if (!string.IsNullOrEmpty(query.searchTerm))
            {
                var search = query.searchTerm.ToLower();
                leadsQuery = leadsQuery.Where(l =>
                    (l.name != null && l.name.ToLower().Contains(search)) ||
                    (l.phone != null && l.phone.ToLower().Contains(search)) ||
                    (l.email != null && l.email.ToLower().Contains(search)) ||
                    (l.address != null && l.address.ToLower().Contains(search)));
            }

            if (query.score.HasValue)
            {
                leadsQuery = leadsQuery.Where(l => l.score == query.score.Value);
            }

            // Get total count after filters
            var totalCountAfterFilter = leadsQuery.Count();

            // Apply sorting and pagination
            leadsQuery = query.isDescending
                ? leadsQuery.OrderByDescending(l => l.name)
                : leadsQuery.OrderBy(l => l.name);

            var paginatedLeads = leadsQuery
                .Skip((query.pageNumber - 1) * query.pageSize)
                .Take(query.pageSize)
                .ToList();

            var leadResponses = await BuildLeadResponsesAsync(paginatedLeads);

            return new PagedResult<LeadResponse>
            {
                data = leadResponses,
                totalCount = totalCountAfterFilter, // Sử dụng count sau khi filter
                currentPage = query.pageNumber,
                pageSize = query.pageSize
            };
        }

        public async Task<PagedResult<LeadResponse>> GetLeadsBySalerIdAsync(string userId, QueryLead queryLead)
        {
            // Lấy leads của saler
            var leads = await _unitOfWork.Leads.GetLeadsBySalerIdAsync(userId);

            // Build responses
            var leadResponses = await BuildLeadResponsesAsync(leads);

            // Apply filters và sorting
            var query = leadResponses.AsQueryable();
            query = ApplyFiltersAndSorting(query, queryLead);

            // Get total count after filters
            var totalCount = query.Count();

            // Apply pagination
            var paginatedResults = query
                .Skip((queryLead.pageNumber - 1) * queryLead.pageSize)
                .Take(queryLead.pageSize)
                .ToList();

            return new PagedResult<LeadResponse>
            {
                data = paginatedResults,
                totalCount = totalCount,
                currentPage = queryLead.pageNumber,
                pageSize = queryLead.pageSize
            };
        }

        private async Task<List<LeadResponse>> BuildLeadResponsesAsync(IEnumerable<Lead> leads)
        {
            var leadResponses = new List<LeadResponse>();

            // Lấy tất cả user IDs cần thiết
            var allUserIds = leads
                .Where(l => l.assignedTo != null)
                .SelectMany(l => l.assignedTo.Select(a => a.id))
                .Distinct()
                .ToList();

            // Lấy tất cả users một lần thay vì query từng cái
            var users = new Dictionary<string, User>();
            if (allUserIds.Any())
            {
                var userList = await _userRepository.GetUsersByIdsAsync(allUserIds);
                users = userList.ToDictionary(u => u.id, u => u);
            }

            foreach (var lead in leads)
            {
                var sellers = new List<LeadSeller>();

                if (lead.assignedTo?.Any() == true)
                {
                    foreach (var assignedUser in lead.assignedTo)
                    {
                        if (users.TryGetValue(assignedUser.id, out var user))
                        {
                            sellers.Add(new LeadSeller
                            {
                                id = user.id,
                                name = user.fullName,
                                email = user.email,
                                phone = user.phoneNumber,
                                avatar = user.avatar,
                                about = user.about,
                                birthdate = user.birthdate
                            });
                        }
                    }
                }

                leadResponses.Add(new LeadResponse
                {
                    id = lead.id,
                    name = lead.name,
                    email = lead.email,
                    phone = lead.phone,
                    address = lead.address,
                    source = lead.source,
                    score = lead.score,
                    assignedTo = sellers
                });
            }

            return leadResponses;
        }

        private IQueryable<LeadResponse> ApplyFiltersAndSorting(IQueryable<LeadResponse> query, QueryLead queryLead)
        {
            // Apply search filter
            if (!string.IsNullOrEmpty(queryLead.searchTerm))
            {
                var search = queryLead.searchTerm.ToLower();
                query = query.Where(l =>
                    (l.name != null && l.name.ToLower().Contains(search)) ||
                    (l.phone != null && l.phone.ToLower().Contains(search)) ||
                    (l.email != null && l.email.ToLower().Contains(search)) ||
                    (l.address != null && l.address.ToLower().Contains(search)));
            }

            // Apply score filter
            if (queryLead.score.HasValue)
            {
                query = query.Where(l => l.score == queryLead.score.Value);
            }

            // Apply sorting
            query = queryLead.isDescending
                ? query.OrderByDescending(l => l.name)
                : query.OrderBy(l => l.name);

            return query;
        }
        public async Task UpdateScoreByLeadId(string leadId, LeadScore score)
        {
            var lead = await _unitOfWork.Leads.GetByIdAsync(leadId);
            if (lead == null)
            {
                throw new Exception("Lead not found");
            }
            lead.score = score;
            lead.updatedAt = DateTime.UtcNow;
            await _unitOfWork.Leads.UpdateAsync(lead.id, lead);
        }

        public async Task AssignLeadToSellerAsync(string leadId, string sellerId)
        {
            // Validate input parameters
            if (string.IsNullOrEmpty(leadId))
                throw new ArgumentException("Lead ID cannot be null or empty", nameof(leadId));

            if (string.IsNullOrEmpty(sellerId))
                throw new ArgumentException("Seller ID cannot be null or empty", nameof(sellerId));

            // Get lead and seller concurrently
            var leadTask = _unitOfWork.Leads.GetByIdAsync(leadId);
            var sellerTask = _userRepository.GetByIdAsync(sellerId);

            await Task.WhenAll(leadTask, sellerTask);

            var lead = await leadTask;
            var seller = await sellerTask;

            // Validate seller
            if (seller == null)
                throw new InvalidDataException($"Seller with ID {sellerId} not found");

            if (seller.role != UserRole.Saler)
                throw new InvalidOperationException($"User {sellerId} is not a valid seller");

            // Validate lead
            if (lead == null)
                throw new InvalidDataException($"Lead with ID {leadId} not found");

            // Initialize assignedTo if null
            if (lead.assignedTo == null)
            {
                lead.assignedTo = new List<AssignedTo>();
            }

            // Check if seller is already assigned
            var existingAssignment = lead.assignedTo.FirstOrDefault(a => a.id == sellerId);
            if (existingAssignment != null)
            {
                throw new InvalidDataException($"Seller {sellerId} is already assigned to lead {leadId}");
            }

            // Add new assignment
            var newAssignment = new AssignedTo
            {
                id = sellerId,
                assignedAt = DateOnly.FromDateTime(DateTime.UtcNow)
            };

            lead.assignedTo.Add(newAssignment);
            lead.updatedAt = DateTime.UtcNow;

            // Update the lead
            await _unitOfWork.Leads.UpdateAsync(lead.id, lead);
        }
    }
}
