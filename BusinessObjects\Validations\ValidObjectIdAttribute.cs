﻿using MongoDB.Bson;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.Validations
{
    public class ValidObjectIdAttribute : ValidationAttribute
    {
        public override bool IsValid(object? value)
        {
            if (value == null) return true;

            if (value is string stringValue)
            {
                return ObjectId.TryParse(stringValue, out _);
            }

            return false;
        }
    }
}
