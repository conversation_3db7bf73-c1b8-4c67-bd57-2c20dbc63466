﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using MongoDB.Driver;
using Repositories.Interfaces;


namespace Repositories.Implements
{
    public class DraftPropertyRepository : GenericRepository<DraftProperty>, IDraftPropertyRepository
    {
        private readonly IMongoCollection<DraftProperty> _draftProperties;

        public DraftPropertyRepository(RevoLandDbContext context) : base(context, "DraftProperties")
        {
            _draftProperties = context.GetCollection<DraftProperty>("DraftProperties");
        }

        public async Task<(int total, List<DraftProperty>)> GetDraftPropertiesByUserAsync(string userId, string role, int pageSize, int currentPage)
        {
            var filter = Builders<DraftProperty>.Filter.Eq(role == UserRole.Saler.ToString() ? "salerId" : "ownerId", userId);
            var skip = (currentPage - 1) * pageSize;
            var draftProperties = await _draftProperties.Find(filter)
                .Skip(skip)
                .Limit(pageSize)
                .ToListAsync();
            var total = await _draftProperties.CountDocumentsAsync(filter);
            return ((int)total, draftProperties);
        }

    }
}
