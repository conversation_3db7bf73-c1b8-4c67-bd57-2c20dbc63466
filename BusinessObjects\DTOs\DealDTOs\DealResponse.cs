﻿namespace BusinessObjects.DTOs.DealDTOs
{
    public class DealResponse
    {
        public required string id { get; set; }
        public string? title { get; set; }
        public string? description { get; set; }

        public List<DealPropertyResponse>? properties { get; set; }

        public List<DealNoteResponse>? notes { get; set; }

        public string? status { get; set; }

        public decimal position { get; set; }
        public DateTime createdAt { get; set; }
        public DateTime updatedAt { get; set; }

        public string? priority { get; set; }

        public string? salesRepId { get; set; }

        public required UserResponse saler { get; set; }

        public string? leadId { get; set; }
        public LeadDTOs.LeadResponse? customer { get; set; }
    }
    public class DealPropertyResponse
    {
        public required string id { get; set; }
        public DateTime createdAt { get; set; }
        public DateTime updatedAt { get; set; }
    }
}
