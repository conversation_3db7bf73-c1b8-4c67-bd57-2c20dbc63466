using BusinessObjects.DTOs;
using System.Threading.Tasks;

namespace Services.Interface
{
    public interface ISurveyService
    {
        Task<SurveyResponse> AddSurveyAsync(SurveyCreateRequest surveyRequest);
        Task<SurveyResponse> GetSurveyByIdAsync(string id);
        Task<List<SurveyResponse>> GetAllSurveysAsync();
        Task<SurveyResponse> ConvertToLeadAsync(string surveyId, string adminId);
    }
} 