﻿namespace BusinessObjects.DTOs.ChatMessageDTOs
{
    public class ChatMessageResponse
    {
        public string id { get; set; }
        public string senderId { get; set; }
        public string direction { get; set; }
        public string content { get; set; }
        public DateTime createdAt { get; set; }
        public Boolean isDeleted { get; set; }
        public string replyToMessageId { get; set; }
        public List<ChatMessageAttachments> attachments { get; set; }
    }
    public class ChatMessageAttachments
    {
        public string type { get; set; }
        public string url { get; set; }
        public string fileName { get; set; }

    }
}
