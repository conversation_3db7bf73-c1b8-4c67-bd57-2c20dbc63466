using BusinessObjects.DTOs;
using BusinessObjects.Models;
using Microsoft.AspNetCore.Http;

namespace Services.Interfaces
{
    public interface IContractService
    {
        Task<PagedResult<ContractDTO>> GetAllContractsAsync(int pageNumber, int pageSize, string? propertyId = null, string? ownerId = null, string? tenantId = null, string? buyerId = null, string? salerId = null, ContractType? contractType = null);
        Task<PagedResult<ContractDTO>> GetAllContractForSalerAsync(int pageNumber, int pageSize, string? propertyId = null, string? ownerId = null, string? tenantId = null, string? buyerId = null, ContractType? contractType = null);
        Task<ContractDTO> GetContractByIdAsync(string id);
        Task<PagedResult<ContractDTO>> GetActiveContractsAsync(int pageNumber, int pageSize);
        Task<PagedResult<ContractDTO>> GetExpiringContractsAsync(int daysToExpire, int pageNumber, int pageSize);
        Task<PagedResult<ContractDTO>> GetDeletedContractsAsync(int pageNumber, int pageSize);
        Task<ContractDTO> CreateRentalContractAsync(CreateRentalContractDTO createContractDTO);
        Task<ContractDTO> CreateSaleContractAsync(CreateSaleContractDTO createSaleContractDTO);
        Task<ContractDTO> UpdateRentalContractAsync(string id, UpdateRentalContractDTO updateRentalContractDTO);
        Task<ContractDTO> UpdateSaleContractAsync(string id, UpdateSaleContractDTO updateSaleContractDTO);
		Task<ContractDTO> UpdateContractPdfAsync(string id, IFormFile pdfFile);
        Task<ContractDTO> RenewContractAsync(string id, RenewContractDTO renewContractDTO);
        Task<ContractDTO> RestoreContractAsync(string id);
        Task<bool> DeleteContractAsync(string id);
        Task<ContractDTO> SoftDeleteContractAsync(string id);
       
        Task<string> CreatePayRentalContraclRequestAsync(string id);
        Task<(bool Success, string Message)> ProcessIpnAsync(IQueryCollection query);
        Task<bool> SendContractByEmailAsync(string email, string contractLink);
    }
}