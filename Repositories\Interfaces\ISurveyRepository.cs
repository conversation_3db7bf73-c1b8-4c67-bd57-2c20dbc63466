using BusinessObjects.Models;
using System.Threading.Tasks;

namespace Repositories.Interfaces
{
    public interface ISurveyRepository
    {
        Task<Survey> AddSurveyAsync(Survey survey);
        Task<Survey> GetSurveyByIdAsync(string id);
        Task<List<Survey>> GetAllSurveysAsync();
        Task<Survey> UpdateSurveyAsync(string id, Survey survey);
        Task<bool> DeleteSurveyAsync(string id);
        Task<Survey> ConvertToLeadAsync(string id, bool isConverted);
    }
} 