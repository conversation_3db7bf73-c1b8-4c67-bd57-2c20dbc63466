using BusinessObjects.DTOs;
using BusinessObjects.Models;
using Repositories.Interfaces;
using Services.Interface;

namespace Services.Implements
{
    public class SurveyService : ISurveyService
    {
        private readonly IUnitOfWork _unitOfWork;

        public SurveyService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<SurveyResponse> AddSurveyAsync(SurveyCreateRequest surveyRequest)
        {
            var survey = new Survey
            {
                fullName = surveyRequest.fullName,
                email = surveyRequest.email,
                phoneNumber = surveyRequest.phoneNumber,
                propertyTypeInterest = surveyRequest.propertyTypeInterest,
                budgetCategory = surveyRequest.budgetCategory,
                locationPreference = surveyRequest.locationPreference,
                additionalComments = surveyRequest.additionalComments
            };

            var createdSurvey = await _unitOfWork.Surveys.AddSurveyAsync(survey);
            await _unitOfWork.SaveAsync();

            return MapToResponse(createdSurvey);
        }

        public async Task<SurveyResponse> GetSurveyByIdAsync(string id)
        {
            var survey = await _unitOfWork.Surveys.GetSurveyByIdAsync(id);
            return survey != null ? MapToResponse(survey) : null;
        }

        public async Task<List<SurveyResponse>> GetAllSurveysAsync()
        {
            var surveys = await _unitOfWork.Surveys.GetAllSurveysAsync();
            return surveys.Select(s => MapToResponse(s)).ToList();
        }

        public async Task<SurveyResponse> ConvertToLeadAsync(string surveyId, string adminId)
        {
            var survey = await _unitOfWork.Surveys.GetSurveyByIdAsync(surveyId);
            if (survey == null)
            {
                return null;
            }

            var updatedSurvey = await _unitOfWork.Surveys.ConvertToLeadAsync(surveyId, true);
            await _unitOfWork.SaveAsync();

            var lead = new Lead
            {
                name = survey.fullName,
                email = survey.email,
                phone = survey.phoneNumber,
                source = LeadSource.Admin,
                score = LeadScore.Warm,
                createdAt = DateTime.UtcNow,
                updatedAt = DateTime.UtcNow,
                assignedTo = new List<AssignedTo> { new AssignedTo() { id = adminId } }
            };
            await _unitOfWork.Leads.AddAsync(lead);
            await _unitOfWork.SaveAsync();

            return MapToResponse(updatedSurvey);
        }

        private SurveyResponse MapToResponse(Survey survey)
        {
            return new SurveyResponse
            {
                id = survey.id,
                fullName = survey.fullName,
                email = survey.email,
                phoneNumber = survey.phoneNumber,
                propertyTypeInterest = survey.propertyTypeInterest,
                budgetCategory = survey.budgetCategory,
                locationPreference = survey.locationPreference,
                additionalComments = survey.additionalComments,
                createdAt = survey.createdAt,
                isConvertedToLead = survey.isConvertedToLead
            };
        }
    }
}