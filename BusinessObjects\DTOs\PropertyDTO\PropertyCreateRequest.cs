﻿using BusinessObjects.Models;
using System.ComponentModel.DataAnnotations;

public class PropertyCreateRequest
{
    [StringLength(255, ErrorMessage = "Property title cannot exceed 255 characters.")]
    public string? title { get; set; }

    [StringLength(255, ErrorMessage = "Property name cannot exceed 255 characters.")]
    public string? name { get; set; }

    public string? description { get; set; }

    [Range(0, int.MaxValue, ErrorMessage = "Property transaction type must be equal or greater than 0.")]
    public PropertyTransactionType? transactionType { get; set; }

    [Range(0, int.MaxValue, ErrorMessage = "Property type must be equal or greater than 0.")]
    public PropertyType? type { get; set; }

    [Range(0, int.MaxValue, ErrorMessage = "Property status must be equal or greater than 0.")]
    public PropertyStatus? status { get; set; }

    public string? adminNote { get; set; }

    public string? code { get; set; }

    public string? ownerId { get; set; }

    public LocationCreateRequest? location { get; set; }

    public PropertyDetail? propertyDetails { get; set; }

    public PriceDetailCreateRequest? priceDetails { get; set; }

    public Amenity? amenities { get; set; }

    public List<string>? images { get; set; }

    public int? yearBuilt { get; set; }

    public List<string>? floorPlans { get; set; }
    public PropertyVideo? video { get; set; }

    public List<string>? legalDocuments { get; set; }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        if (!IsValidYearBuilt(yearBuilt))
        {
            yield return new ValidationResult(
                $"Năm xây dựng phải từ năm 1800 tới năm {DateTime.Now.Year}.",
                new[] { nameof(yearBuilt) }
            );
        }
    }

    private bool IsValidYearBuilt(int? year)
    {
        int currentYear = DateTime.Now.Year;
        return year.HasValue && year.Value >= 1800 && year.Value <= currentYear;
    }

}

public class LocationCreateRequest
{
    [Required(ErrorMessage = "Address is required.")]
    public string address { get; set; }

    [Required(ErrorMessage = "City is required.")]
    public string city { get; set; }

    [Required(ErrorMessage = "District is required.")]
    public string district { get; set; }

    [Required(ErrorMessage = "Ward is required.")]
    public string ward { get; set; }

    public double? latitude { get; set; }

    public double? longitude { get; set; }
}

public class PriceDetailCreateRequest
{
    [Range(0, double.MaxValue)]
    public double? salePrice { get; set; }

    [Range(0, double.MaxValue)]
    public double? rentalPrice { get; set; }

    [Range(0, double.MaxValue)]
    public double? pricePerSquareMeter { get; set; }

    public string? currency { get; set; }

    [Range(0, double.MaxValue, ErrorMessage = "Deposit amount must be greater than 0.")]
    public double? depositAmount { get; set; }

    [Range(0, double.MaxValue, ErrorMessage = "Maintenance fee must be greater than 0.")]
    public double? maintenanceFee { get; set; }

    public List<string> paymentMethods { get; set; }
}

