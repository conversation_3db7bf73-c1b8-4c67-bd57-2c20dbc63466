﻿using BusinessObjects.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Services.Interfaces
{
	public interface IAgentActivitylogService
	{
		Task AddActivityLogAsync(AgentActivityLog activityLog);
		Task<List<AgentActivityLog>> GetActivityLogsByAgentIdAsync(string agentId);
		Task<IEnumerable<AgentActivityLog>> GetAllActivityLogsAsync();
	}
}
