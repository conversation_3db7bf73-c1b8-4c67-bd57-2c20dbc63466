﻿using BusinessObjects.Models;
using Microsoft.AspNetCore.Http;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System.ComponentModel.DataAnnotations;

namespace BusinessObjects.DTOs.CompanyDTOs
{
    public class CompanyUpdateRequest
    {
        [Required]
        public string name { get; set; }

        [Required]
        [EmailAddress]
        public string? email { get; set; }
        [Required]
        public string password { get; set; }

        [Phone]
        [Required]
        [BsonElement("phone")]
        public string? phone { get; set; }
        public string? address { get; set; }

        [BsonRepresentation(BsonType.String)]
        public Industry? industry { get; set; }
        public string? taxId { get; set; }
        public string? licenseNumber { get; set; }
        public string? website { get; set; }
    }
}
