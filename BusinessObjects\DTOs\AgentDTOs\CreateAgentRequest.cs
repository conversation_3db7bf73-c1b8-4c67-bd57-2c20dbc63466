﻿using BusinessObjects.Models;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.DTOs.AgentDTOs
{
	public class CreateAgentRequest
	{

		[Required(ErrorMessage = "Full name is required")]
		public string fullName { get; set; }

		[Required(ErrorMessage = "Email is required")]
		[EmailAddress]
		public string email { get; set; }

		[Required(ErrorMessage = "Phone is required")]
		[Phone]
		public string phone { get; set; }

		public string? avatar { get; set; }

		[BsonRepresentation(BsonType.String)]
		public Gender? gender { get; set; }

		public string? bio { get; set; }
		public DateTime? joinedAt { get; set; }
	}
}
