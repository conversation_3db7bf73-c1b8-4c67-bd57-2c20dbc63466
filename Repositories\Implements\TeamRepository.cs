﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using MongoDB.Driver;
using Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Repositories.Implements
{
	public class TeamRepository : GenericRepository<Team>, ITeamRepository
	{
		private readonly IMongoCollection<Team> _mongoCollection;
		private readonly IMongoCollection<TeamAgentLink> _mongoCollectionTeamAgentLinks;
		public TeamRepository(RevoLandDbContext context) : base(context, "Teams")
		{
			_mongoCollectionTeamAgentLinks = context.GetCollection<TeamAgentLink>("TeamAgentLinks");
			_mongoCollection = context.GetCollection<Team>("Teams");
		}

		public async Task DeleteTeamAgentLinkByTeamIdAsync(string teamId)
		{
			var filter = Builders<TeamAgentLink>.Filter.Eq(t => t.teamId, teamId);
			//var links = await _mongoCollectionTeamAgentLinks.Find(filter).ToListAsync();
			await _mongoCollectionTeamAgentLinks.DeleteManyAsync(filter);
		}

		public async Task<Team> GetTeamByNameAsync(string name)
		{
			var filter = Builders<Team>.Filter.Eq(u => u.name, name);
			return await _mongoCollection.Find(filter).FirstOrDefaultAsync();
		}


	}
}
