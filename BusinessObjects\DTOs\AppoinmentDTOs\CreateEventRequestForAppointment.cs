﻿using System.ComponentModel.DataAnnotations;
using static BusinessObjects.DTOs.CalendarDTOs.GoogleCalendar;

namespace BusinessObjects.DTOs.AppoinmentDTOs
{
    public class CreateEventRequestForAppointment
    {
        [Required]
        [StringLength(2048, ErrorMessage = "Access token không hợp lệ")]
        public string accessToken { get; set; }

        [StringLength(255, ErrorMessage = "Tiêu đề không được vượt quá 255 ký tự")]
        public string title { get; set; }

        [StringLength(1000, ErrorMessage = "Mô tả không được vượt quá 1000 ký tự")]
        public string description { get; set; }

        [StringLength(255, ErrorMessage = "Địa điểm không được vượt quá 255 ký tự")]
        public string location { get; set; }
        public DateTime startTime { get; set; }
        public DateTime endTime { get; set; }

        [Required]
        [StringLength(255, ErrorMessage = "Calendar ID không hợp lệ")]
        public string calendarId { get; set; }

        // Timezone (mặc định là Asia/Ho_Chi_Minh)
        public string timeZone { get; set; } = "Asia/Ho_Chi_Minh";

        // Có phải là sự kiện cả ngày không
        public bool isAllDay { get; set; } = false;

        // Danh sách email người tham gia
        //public List<string> attendees { get; set; } = new List<string>();

        // Màu sắc của event (1-11)
        public int? colorId { get; set; }

        // Có gửi email thông báo không
        public bool sendNotifications { get; set; } = true;

        // Reminder settings
        public List<EventReminder> reminders { get; set; } = new List<EventReminder>();

        // Recurrence rules (cho event lặp lại)
        //public List<string> recurrenceRules { get; set; } = new List<string>();
    }
}
