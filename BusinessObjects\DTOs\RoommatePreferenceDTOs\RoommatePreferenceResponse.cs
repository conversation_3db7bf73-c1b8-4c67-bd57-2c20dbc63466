using BusinessObjects.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.DTOs.RoommatePreferenceDTOs
{
    public class RoommatePreferenceResponse
    {
        public string Id { get; set; }
        public string userId { get; set; }
        public Gender preferredGender { get; set; }
        public int? ageMin { get; set; }
        public int? ageMax { get; set; }
        public bool? allowPets { get; set; }
        public bool? allowSmoking { get; set; }
        public List<OccupationType>? occupation { get; set; }
        public List<Lifestyle>? preferredLifestyles { get; set; }
        public List<InterestType>? interests { get; set; }
        public DateTime updatedAt { get; set; }
    }
} 