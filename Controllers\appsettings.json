{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"MongoDb": "mongodb://localhost:27017/", "DatabaseName": "QuindLand", "Redis": "localhost:6379"}, "KeySetting": {"SecretKey": "bmVSKJ6k3F9HcbxH91LfcFOQm5UC2wz2zrAeMzzERzJeNAgoiybmVSKJ6k3F9HcbxH91LfcFOQm5UC2wz2zrAeMzzERzJeNAgoiy", "MaxFileSize": *********, "UploadsPath": "_Uploads"}, "Jwt": {"Key": "DhftOS5uphK3vmCJQrexST1RsyjZBjXWRgJMFPU4", "Issuer": "https://localhost:5094/", "Audience": "https://localhost:5094/"}, "Firebase": {"ServiceAccountKeyPath": "convoshub-firebase-adminsdk-26wec-27613c5b06.json"}, "EmailSettings": {"SmtpServer": "smtp.gmail.com", "SmtpPort": 587, "SmtpUser": "<EMAIL>", "SmtpPass": "nwxkmxbjbyrnszks", "ResetPassURL": "http://localhost:3000"}, "Twilio": {"AccountSid": "**********************************", "AuthToken": "50bb7750721d5305994b6096cd2d1eca", "PhoneNumber": "+***********"}, "ESmsSettings": {"ApiKey": "97621CB009EE161CCC8B4BB173091A", "SecretKey": "3C0F7D3D187E6F129B133B6310471D", "Brandname": "RevoLand", "SmsType": 2, "UseSandbox": false, "UseFixedCostSms": false}, "LeaseCheckSettings": {"DaysBeforeExpiration": 30, "SendEmailNotifications": true, "SendSmsNotifications": true, "CreateInAppNotifications": true, "CronExpression": "0 0 8 * * *"}, "VerifyToken": "McEeIAdzXfSSUfZRWKryeOCMOZ6jp57jKhSAYtQpGafrp0xCLy5xt8xuWVc8iyek", "Facebook": {"AppId": "***************", "AppSecret": "YOUR_APP_SECRET", "PageAccessToken": "EAARSPKnHMzEBOwzpZAZC95TnkUkgzEnejHrWrzVOTndCf6Cwr7dm6s7cC8e0NNAZABC2vCQZBOEOvZBIfdH19e8Vao4zCRsxsXhQyn2V4ZBGPk1zjJPaEUog31ZBZAfUEtmGhaMGWGVPCGVxw4naFSOuFcdXAkw85X4Y5EoXVolb4u1qUNNbvOxQyZAZBOzQhtDVoelgZDZD"}, "Zalo": {"AppId": "1721521501103372745", "AppSecret": "vYGUFE4eHO3F735U4BCr"}, "GoogleAuth": {"ClientId": "193885030874-l87mth3lfg1eorv3fdv6q9n3q59s2f2h.apps.googleusercontent.com", "ClientSecret": "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"}, "GoogleAuthCalendar": {"ClientId": "694923148550-********************************.apps.googleusercontent.com", "ClientSecret": "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", "RedirectUri": "https://localhost:7155/api/oauth-calendar/auth/google/callback"}, "Sms": {"ApiKey": "", "SecretKey": ""}, "Vnpay": {"TmnCode": "QNL8REGN", "HashSecret": "JJYF8FT4SJ77TROD4S0IUYWX0ALPD0PJ", "BaseUrl": "https://sandbox.vnpayment.vn/paymentv2/vpcpay.html", "ReturnUrl": "http://localhost:3000/vnpay_return"}, "Momo": {"AccessKey": "F8BBA842ECF85", "SecretKey": "K951B6PE1waDMi640xX08PD3vg6EkVlz", "RequestType": "captureWallet", "PartnerCode": "MOMO", "MomoApiUrl": "https://test-payment.momo.vn/gw_payment/transactionProcessor", "ReturnUrl": "https://localhost:5001/Home/PaymentCallBack"}}