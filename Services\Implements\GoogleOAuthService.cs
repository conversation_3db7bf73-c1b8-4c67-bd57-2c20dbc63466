﻿// Services/Implements/GoogleOAuthService.cs
using Google.Apis.Auth;
using Google.Apis.Auth.OAuth2;
using Google.Apis.Auth.OAuth2.Flows;
using Google.Apis.Auth.OAuth2.Responses;
using Google.Apis.Calendar.v3;
using Google.Apis.Oauth2.v2;
using Google.Apis.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Services.Interfaces;
using static BusinessObjects.DTOs.GoogleDTOs.GoogleUserDTOs;

namespace Services.Implements
{
    public class GoogleOAuthService : IGoogleOAuthService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<GoogleOAuthService> _logger;
        private readonly string _clientId;
        private readonly string _clientSecret;
        private readonly string _redirectUri;
        private const string GoogleOAuthApiBaseUrl = "https://accounts.google.com/o/oauth2/v2/auth";

        private static readonly string[] CalendarScopes = new[]
        {
            // ✅ WRITE permissions - c<PERSON> thể tạo, sửa, xóa
            "https://www.googleapis.com/auth/calendar",                    // Full calendar access
            "https://www.googleapis.com/auth/calendar.events",             // Calendar events write access
    
            // ✅ READ permissions - để đọc thông tin (optional, vì write đã bao gồm read)
            "https://www.googleapis.com/auth/calendar.readonly",           // Read calendar info
            "https://www.googleapis.com/auth/calendar.events.readonly",    // Read events info
    
            // ✅ Additional useful scopes
            "https://www.googleapis.com/auth/calendar.calendarlist",       // Manage calendar list
            "https://www.googleapis.com/auth/calendar.calendarlist.readonly" // Read calendar list
        };


        public GoogleOAuthService(
            IConfiguration configuration,
            ILogger<GoogleOAuthService> logger)
        {
            _configuration = configuration;
            _logger = logger;
            _clientId = _configuration["GoogleAuthCalendar:ClientId"]!;
            _clientSecret = _configuration["GoogleAuthCalendar:ClientSecret"]!;
            _redirectUri = _configuration["GoogleAuthCalendar:RedirectUri"]!;
        }

        public string GenerateCalendarAuthUrl(string state, string userId)
        {
            try
            {
                // ✅ Manual URL building - works with all versions
                var scopes = string.Join(" ", CalendarScopes);
                var stateValue = $"{state}|{userId}";

                var authUrl = "https://accounts.google.com/o/oauth2/v2/auth?" +
                             $"client_id={Uri.EscapeDataString(_clientId)}&" +
                             $"redirect_uri={Uri.EscapeDataString(_redirectUri)}&" +
                             $"scope={Uri.EscapeDataString(scopes)}&" +
                             $"response_type=code&" +
                             $"access_type=offline&" +
                             $"prompt=consent&" + // ✅ Dùng prompt=consent thay vì approval_prompt=force
                             $"include_granted_scopes=true&" +
                             $"state={Uri.EscapeDataString(stateValue)}";

                _logger.LogInformation($"Generated calendar auth URL for user {userId}");
                return authUrl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating calendar auth URL");
                throw;
            }
        }

        public async Task<TokenResponse> GetTokensFromCodeAsync(string code)
        {
            try
            {
                var flow = new GoogleAuthorizationCodeFlow(new GoogleAuthorizationCodeFlow.Initializer
                {
                    ClientSecrets = new ClientSecrets
                    {
                        ClientId = _clientId,
                        ClientSecret = _clientSecret
                    },
                    Scopes = CalendarScopes
                });

                var tokenResponse = await flow.ExchangeCodeForTokenAsync(
                    userId: "user",
                    code: code,
                    redirectUri: _redirectUri,
                    CancellationToken.None);

                _logger.LogInformation("Successfully exchanged code for tokens");
                return tokenResponse;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exchanging code for tokens");
                throw new InvalidOperationException($"Failed to exchange code for tokens: {ex.Message}");
            }
        }

        public async Task<TokenResponse> RefreshAccessTokenAsync(string refreshToken)
        {
            try
            {
                var flow = new GoogleAuthorizationCodeFlow(new GoogleAuthorizationCodeFlow.Initializer
                {
                    ClientSecrets = new ClientSecrets
                    {
                        ClientId = _clientId,
                        ClientSecret = _clientSecret
                    },
                    Scopes = CalendarScopes
                });

                var tokenResponse = await flow.RefreshTokenAsync(
                    userId: "user",
                    refreshToken: refreshToken,
                    CancellationToken.None);

                _logger.LogInformation("Successfully refreshed access token");
                return tokenResponse;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing access token");
                throw new InvalidOperationException($"Failed to refresh access token: {ex.Message}");
            }
        }

        //public async Task<List<CalendarInfo>> GetUserCalendarsAsync(string userId)
        //{
        //    try
        //    {
        //        var credentials = await _credentialService.GetActiveCredentialsAsync(userId);
        //        if (credentials == null || string.IsNullOrEmpty(credentials.accessToken))
        //        {
        //            throw new UnauthorizedAccessException("CALENDAR_AUTH_REQUIRED");
        //        }

        //        // Check if token is expired and refresh if needed
        //        if (credentials.tokenExpiry.HasValue && credentials.tokenExpiry < DateTime.UtcNow)
        //        {
        //            if (!string.IsNullOrEmpty(credentials.refreshToken))
        //            {
        //                try
        //                {
        //                    var refreshedTokens = await RefreshAccessTokenAsync(credentials.refreshToken);
        //                    var newExpiry = DateTime.UtcNow.AddSeconds(refreshedTokens.ExpiresInSeconds ?? 3600);

        //                    await _credentialService.RefreshTokenAsync(userId, refreshedTokens.AccessToken, newExpiry);
        //                    credentials.accessToken = refreshedTokens.AccessToken;
        //                }
        //                catch (Exception ex)
        //                {
        //                    _logger.LogWarning(ex, $"Failed to refresh token for user {userId}");
        //                    throw new UnauthorizedAccessException("CALENDAR_AUTH_REQUIRED");
        //                }
        //            }
        //            else
        //            {
        //                throw new UnauthorizedAccessException("CALENDAR_AUTH_REQUIRED");
        //            }
        //        }

        //        var googleCredential = GoogleCredential.FromAccessToken(credentials.accessToken);

        //        var service = new CalendarService(new BaseClientService.Initializer()
        //        {
        //            HttpClientInitializer = googleCredential,
        //            ApplicationName = "Calendar API App"
        //        });

        //        var request = service.CalendarList.List();
        //        request.MaxResults = 50;
        //        request.ShowHidden = false;

        //        var response = await request.ExecuteAsync();

        //        var calendars = response.Items?.Select(item => new CalendarInfo
        //        {
        //            id = item.Id,
        //            summary = item.Summary ?? "",
        //            description = item.Description,
        //            timeZone = item.TimeZone,
        //            primary = item.Primary ?? false,
        //            accessRole = item.AccessRole ?? "",
        //            backgroundColor = item.BackgroundColor,
        //            foregroundColor = item.ForegroundColor
        //        }).ToList() ?? new List<CalendarInfo>();

        //        await _credentialService.RefreshTokenAsync(userId, credentials.accessToken, credentials.tokenExpiry);

        //        _logger.LogInformation($"Successfully retrieved {calendars.Count} calendars for user {userId}");
        //        return calendars;
        //    }
        //    catch (Google.GoogleApiException ex) when (ex.HttpStatusCode == System.Net.HttpStatusCode.Unauthorized)
        //    {
        //        _logger.LogWarning($"Unauthorized access to calendar for user {userId}");
        //        throw new UnauthorizedAccessException("CALENDAR_AUTH_REQUIRED");
        //    }
        //    catch (UnauthorizedAccessException)
        //    {
        //        throw;
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError(ex, $"Error fetching calendars for user {userId}");
        //        throw new InvalidOperationException($"Failed to fetch calendars: {ex.Message}");
        //    }
        //}

        public async Task<bool> ValidateTokenAsync(string accessToken)
        {
            try
            {
                var credential = GoogleCredential.FromAccessToken(accessToken);
                var service = new CalendarService(new BaseClientService.Initializer()
                {
                    HttpClientInitializer = credential,
                    ApplicationName = "Calendar API App"
                });

                var request = service.CalendarList.List();
                request.MaxResults = 1;
                await request.ExecuteAsync();

                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<GoogleJsonWebSignature.Payload> VerifyIdTokenAsync(string idToken)
        {
            try
            {
                var settings = new GoogleJsonWebSignature.ValidationSettings()
                {
                    Audience = new[] { _clientId }
                };

                var payload = await GoogleJsonWebSignature.ValidateAsync(idToken, settings);
                return payload;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying Google ID token");
                throw new InvalidOperationException($"Invalid Google ID token: {ex.Message}");
            }
        }

        /// <summary>
        /// Lấy thông tin người dùng từ Google bằng Google APIs library
        /// </summary>
        /// <param name="accessToken">Access token từ Google OAuth</param>
        /// <returns>Thông tin người dùng Google hoặc null nếu thất bại</returns>
        public async Task<GoogleUserInfo?> GetUserInfoWithGoogleApiAsync(string accessToken)
        {
            try
            {
                // ✅ Validate input
                if (string.IsNullOrEmpty(accessToken))
                {
                    _logger.LogWarning("GetUserInfoWithGoogleApiAsync called with empty access token");
                    return null;
                }

                _logger.LogInformation("Getting user info using Google APIs library");

                // ✅ Create credential from access token
                var credential = GoogleCredential.FromAccessToken(accessToken);

                // ✅ Create OAuth2 service
                var oauth2Service = new Oauth2Service(new BaseClientService.Initializer()
                {
                    HttpClientInitializer = credential,
                    ApplicationName = _configuration["Google:ApplicationName"] ?? "YourAppName"
                });

                // ✅ Get user info
                var userInfoRequest = oauth2Service.Userinfo.Get();
                var userInfo = await userInfoRequest.ExecuteAsync();

                if (userInfo == null)
                {
                    _logger.LogError("Google APIs returned null user info");
                    return null;
                }

                // ✅ Map to our domain model
                var result = new GoogleUserInfo
                {
                    id = userInfo.Id,
                    email = userInfo.Email,
                    verifiedEmail = userInfo.VerifiedEmail ?? false,
                    name = userInfo.Name,
                    givenName = userInfo.GivenName,
                    familyName = userInfo.FamilyName,
                    picture = userInfo.Picture,
                    locale = userInfo.Locale,
                    hd = userInfo.Hd
                };

                _logger.LogInformation($"Successfully retrieved user info for: {result.email} (ID: {result.id})");
                return result;
            }
            catch (Google.GoogleApiException ex)
            {
                _logger.LogError(ex, $"Google API error while getting user info: {ex.Message}");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error while getting user info using Google APIs");
                return null;
            }
        }



    }
}
