﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.DTOs;
using BusinessObjects.Models;
using BusinessObjects.QueryObject;
using MongoDB.Driver;
using Repositories.Interfaces;

namespace Repositories.Implements
{
    public class UserRepository : GenericRepository<User>, IUserRepository
    {
        private readonly IMongoCollection<User> _users;

        public UserRepository(RevoLandDbContext context) : base(context, "Users")
        {
            _users = context.Users;
        }

        public async Task<User> GetUserByUsernameAsync(string username)
        {
            var filter = Builders<User>.Filter.Eq(u => u.userName, username);
            return await _users.Find(filter).FirstOrDefaultAsync();
        }

        public async Task<User> GetUserByEmailAsync(string email)
        {
            var filter = Builders<User>.Filter.Eq(u => u.email, email);
            return await _users.Find(filter).FirstOrDefaultAsync();
        }

        public async Task<User> GetUserByPhonenumberAsync(string phonenumber)
        {
            var filter = Builders<User>.Filter.Eq(u => u.phoneNumber, phonenumber);
            return await _users.Find(filter).FirstOrDefaultAsync();
        }

        public async Task<List<User>> GetByIdsAsync(List<string> userIds)
        {
            var filter = Builders<User>.Filter.In(u => u.id, userIds);
            return await _users.Find(filter).ToListAsync();
        }

        public async Task<List<string>> GetAdminUserIdsAsync()
        {
            var filter = Builders<User>.Filter.Eq(u => u.role, UserRole.Admin);

            // Chỉ lấy trường _id (userId)
            var projection = Builders<User>.Projection.Include(u => u.id);

            var result = await _users
                .Find(filter)
                .Project<User>(projection)
                .ToListAsync();

            return result.Select(u => u.id).ToList();
        }
        public async Task<List<User>> GetUsersByIdsAsync(List<string> userIds)
        {
            if (!userIds?.Any() == true)
                return new List<User>();

            var filter = Builders<User>.Filter.In(u => u.id, userIds);
            return await _users.Find(filter).ToListAsync();
        }

        public async Task<PagedResult<User>> GetSellersAsync(QuerySeller query)
        {
            var filter = Builders<User>.Filter.Eq(u => u.role, UserRole.Saler);

            if (!string.IsNullOrEmpty(query.searchTerm))
            {
                var searchFilter = Builders<User>.Filter.Or(
                    Builders<User>.Filter.Regex(u => u.fullName, new MongoDB.Bson.BsonRegularExpression(query.searchTerm, "i")),
                    Builders<User>.Filter.Regex(u => u.email, new MongoDB.Bson.BsonRegularExpression(query.searchTerm, "i"))
                );
                filter = Builders<User>.Filter.And(filter, searchFilter);
            }

            var totalCount = await _users.CountDocumentsAsync(filter);
            var totalPages = (int)Math.Ceiling((double)totalCount / query.pageSize);

            var users = await _users.Find(filter)
                .Skip((query.pageNumber - 1) * query.pageSize)
                .Limit(query.pageSize)
                .ToListAsync();

            return new PagedResult<User>
            {
                data = users,
                totalCount = (int)totalCount,
                totalPages = totalPages,
                currentPage = query.pageNumber,
                pageSize = query.pageSize
            };
        }
    }
}
