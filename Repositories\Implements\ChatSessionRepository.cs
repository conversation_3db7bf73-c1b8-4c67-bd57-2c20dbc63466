﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using MongoDB.Driver;
using Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Repositories.Implements
{
    public class ChatSessionRepository : GenericRepository<ChatSession>, IChatSessionRepository
    {
        private readonly IMongoCollection<ChatSession> _chatSessions;
        private readonly IMongoCollection<AnonymousSession> _anonymousSessions;

        public ChatSessionRepository(RevoLandDbContext context)
            : base(context, "ChatSessions")
        {
            _chatSessions = context.ChatSessions;
            _anonymousSessions = context.GetCollection<AnonymousSession>("AnonymousSessions");
        }

        public async Task AddMessageAsync(string sessionId, Message message)
        {
            var filter = Builders<ChatSession>.Filter.Eq(cs => cs.sessionId, sessionId);
            var update = Builders<ChatSession>.Update
                .Push(cs => cs.messages, message)
                .Set(cs => cs.updatedAt, DateTime.UtcNow);

            await _chatSessions.UpdateOneAsync(filter, update);
        }

        public async Task<List<ChatSession>> GetByUserIdAsync(string userId)
        {
            return await _chatSessions.Find(cs => cs.userId == userId).ToListAsync();
        }

        public async Task<List<ChatSession>> GetAllChatSessionsAsync()
        {
            return await _chatSessions.Find(_ => true).ToListAsync();
        }

        public async Task<ChatSession> GetByAnonymousSessionIdAsync(string anonymousSessionId)
        {
            return await _chatSessions.Find(cs => cs.anonymousSessionId == anonymousSessionId).FirstOrDefaultAsync();
        }

    }
}
