﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using MongoDB.Driver;
using Repositories.Interfaces;


namespace Repositories.Implements
{
    public class PropertyViewHistoryRepository : GenericRepository<PropertyViewHistory>, IPropertyViewHistoryRepository
    {
        private readonly IMongoCollection<PropertyViewHistory> _propertyViewHistory;
        public PropertyViewHistoryRepository(RevoLandDbContext context) : base(context, "PropertyViewHistories")
        {
            _propertyViewHistory = context.GetCollection<PropertyViewHistory>("PropertyViewHistories");
        }

        public async Task<List<PropertyViewHistory>> GetByUserIdAsync(string userId)
        {
            var filter = Builders<PropertyViewHistory>.Filter.Eq(p => p.userId, userId);
            return await _propertyViewHistory.Find(filter).ToListAsync();
        }

        public async Task<PropertyViewHistory> GetByUserIdAndPropertyId(string userId, string propertyId)
        {
            var filter = Builders<PropertyViewHistory>.Filter.And(
                Builders<PropertyViewHistory>.Filter.Eq(p => p.userId, userId),
                Builders<PropertyViewHistory>.Filter.Eq(p => p.propertyId, propertyId)
            );
            return await _propertyViewHistory.Find(filter).FirstOrDefaultAsync();
        }
    }
}
