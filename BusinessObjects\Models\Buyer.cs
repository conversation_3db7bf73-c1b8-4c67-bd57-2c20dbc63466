﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System.ComponentModel.DataAnnotations;

namespace BusinessObjects.Models
{
	public class Buyer
	{
		[BsonId]
		[BsonRepresentation(BsonType.ObjectId)]
		[BsonElement("buyerId")]
		public string id { get; set; } = ObjectId.GenerateNewId().ToString();

		[Required]
		[BsonElement("name")]
		public string? name { get; set; }

		[Required]
		[BsonElement("phone")]
		public string? phone { get; set; }

		[EmailAddress]
		[BsonElement("email")]
		public string? email { get; set; }
		[Required]
		[BsonElement("address")]
		public string? address { get; set; }
		[Required]
		[BsonElement("idVerification")]
		public List<string>? idVerifications { get; set; }
		[Required]
		[BsonElement("bankCode")]
		public string? bankCode { get; set; }
		[Required]
		[BsonElement("bankName")]
		public string? bankName { get; set; }
		[Required]
		[BsonElement("taxCode")]
		public string? taxCode { get; set; }
		[BsonElement("faxCode")]
		public string? faxCode { get; set; }
		[BsonElement("createdAt")]
		public DateTime createdAt { get; set; }
		[BsonElement("updatedAt")]
		public DateTime updatedAt { get; set; }
	}
}
