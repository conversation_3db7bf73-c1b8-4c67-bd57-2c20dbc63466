﻿using Google.Apis.Oauth2.v2;
using Google.Apis.Services;

namespace Controllers.Extensions
{
    public static class GoogleOAuthServiceExtensions
    {
        public static IServiceCollection AddGoogleOAuthService(this IServiceCollection services)
        {
            // ✅ Add required dependencies
            services.AddHttpClient();

            // ✅ Add Google APIs OAuth2 service
            services.AddSingleton<Oauth2Service>(provider =>
            {
                var configuration = provider.GetRequiredService<IConfiguration>();
                return new Oauth2Service(new BaseClientService.Initializer()
                {
                    ApplicationName = configuration["Google:RevoLand"] ?? "RevoLand"
                });
            });

            return services;
        }
    }
}
