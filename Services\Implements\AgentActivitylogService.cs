﻿using BusinessObjects.Models;
using Repositories.Interfaces;
using Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Services.Implements
{
	public class AgentActivitylogService : IAgentActivitylogService
	{
		private readonly IAgentActivityLogRepository _agentActivityLogRepository;
		private readonly IUnitOfWork _unitOfWork;

		public AgentActivitylogService(IAgentActivityLogRepository agentActivityLogRepository, IUnitOfWork unitOfWork)
		{
			_agentActivityLogRepository = agentActivityLogRepository;
			_unitOfWork = unitOfWork;
		}

		public async Task AddActivityLogAsync(AgentActivityLog activityLog)
		{
			await _unitOfWork.AgentActivityLogs.AddAsync(activityLog);
		}

		public Task<List<AgentActivityLog>> GetActivityLogsByAgentIdAsync(string agentId)
		{
			var activityLogs = _agentActivityLogRepository.GetActivityLogsByAgentIdAsync(agentId);
			return activityLogs;
		}

		public async Task<IEnumerable<AgentActivityLog>> GetAllActivityLogsAsync()
		{
			var allActivityLogs = await _unitOfWork.AgentActivityLogs.GetAllAsync();
			return allActivityLogs;
		}
	}
}
