﻿using BusinessObjects.DTOs.CompanyDTOs;
using BusinessObjects.Models;
using BusinessObjects.Settings;
using Microsoft.AspNetCore.Http;

namespace Services.Interfaces
{
	public interface ICompanyService
	{
		Task<TokenSetting> LoginAsync(CompanyLogin request);
		Task CompanyRegisterAsync(CompanyRegister model, string ownerId, IFormFile logoUrl);
		Task VerifyCompanyOTPAsync(string otp, string verifyKey);
		Task RequestCompanyOTPAsync(string verifyKey);
		Task InviteAgentAsync(string email, string companyId, int validHours, AgentCompanyRole role);
		Task ReInviteAgentAsync(string email, string companyId);
		Task AcceptInviteAsync(string token);
		Task<Company> FindCompanyByEmailAsync(string email);
		Task<Company> FindCompanyByNameAsync(string name);
		Task<Company> FindCompanyByPhonenumberAsync(string phonenumber);
		Task<Company> FindCompanyByIdAsync(string id);
		Task<List<Company>> FindCompanyByIdsAsync(List<string> id);
		Task<IEnumerable<Company>> GetAllCompanyAsync();
		Task<Company> UpdateCompanyAsync(string id, CompanyUpdateRequest request, IFormFile logoUrl);
		Task UpdateCompanyStatusAsync(string id, bool status);
		Task SaveCompanyAsync(Company company);
	}
}
