﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using Repositories.Interfaces;
using System.Linq.Expressions;

namespace Repositories.Implements
{
    public class DealRepository : GenericRepository<Deal>, IDealRepository
    {

        private readonly IMongoCollection<Deal> _deals;
        public DealRepository(RevoLandDbContext context) : base(context, "Deals")
        {
            _deals = context.GetCollection<Deal>("Deals");
        }

        // <summary>
        /// Updates only the status field of a deal.
        /// </summary>
        /// <param name="id">The ID of the deal to update.</param>
        /// <param name="status">The new deal status.</param>
        /// <returns>The updated deal.</returns>
        public async Task<Deal> UpdateStatusAsync(string id, Deal.DealStatus status)
        {
            if (!ObjectId.TryParse(id, out ObjectId objectId))
            {
                throw new ArgumentException("Invalid ObjectId format", nameof(id));
            }

            var filter = Builders<Deal>.Filter.Eq("_id", objectId);
            var update = Builders<Deal>.Update
                            .Set(d => d.status, status)
                            .Set(d => d.updatedAt, DateTime.UtcNow);

            // Execute the update operation
            await _deals.UpdateOneAsync(filter, update);

            // Retrieve the updated deal and return it
            return await _deals.Find(filter).FirstOrDefaultAsync();
        }
        public async Task<List<Deal>> GetDealsByFilterAsync(FilterDefinition<Deal> filter, SortDefinition<Deal> sort, int skip, int limit)
        {
            return await _deals.Find(filter)
                               .Sort(sort)
                               .Skip(skip)
                               .Limit(limit)
                               .ToListAsync();
        }

        public async Task<long> CountDealsWithFilterAsync(FilterDefinition<Deal> filter)
        {
            return await _deals.CountDocumentsAsync(filter);
        }

        public async Task UpdateManyAsync(Expression<Func<Deal, bool>> filter, UpdateDefinition<Deal> update)
        {
            await _deals.UpdateManyAsync(filter, update);
        }

        public async Task<List<Deal>> GetManyAsync(
            Expression<Func<Deal, bool>> filter,
            Func<IQueryable<Deal>, IOrderedQueryable<Deal>> orderBy = null)
        {
            var query = _deals.AsQueryable().Where(filter);

            if (orderBy != null)
            {
                query = orderBy(query);
            }

            return await query.ToListAsync();
        }

        public async Task<IEnumerable<TResult>> AggregateAsync<TResult>(BsonDocument[] pipeline)
        {
            try
            {
                var options = new AggregateOptions
                {
                    AllowDiskUse = true,
                    MaxTime = TimeSpan.FromMinutes(5)
                };

                var cursor = await _deals.AggregateAsync<TResult>(pipeline, options);
                return await cursor.ToListAsync();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<IEnumerable<TResult>> AggregateAsync<TResult>(PipelineDefinition<Deal, TResult> pipeline)
        {
            try
            {
                var options = new AggregateOptions
                {
                    AllowDiskUse = true,
                    MaxTime = TimeSpan.FromMinutes(5)
                };

                var cursor = await _deals.AggregateAsync(pipeline, options);
                return await cursor.ToListAsync();
            }
            catch (Exception ex)
            {
                throw;
            }
        }
    }
}
