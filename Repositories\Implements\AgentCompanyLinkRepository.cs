﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using MongoDB.Driver;
using Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Repositories.Implements
{
	public class AgentCompanyLinkRepository : GenericRepository<AgentCompanyLink>, IAgentCompanyLinkRepository
	{
		private readonly IMongoCollection<AgentCompanyLink> _mongoCollection;
		public AgentCompanyLinkRepository(RevoLandDbContext context) : base(context, "AgentCompanyLinks")
		{
			_mongoCollection = context.GetCollection<AgentCompanyLink>("AgentCompanyLinks");
		}
		public Task<AgentCompanyLink> ExistsAsync(string agentId, string companyId)
		{
			var filter = Builders<AgentCompanyLink>.Filter.And(
				Builders<AgentCompanyLink>.Filter.Eq(link => link.agentId, agentId),
				Builders<AgentCompanyLink>.Filter.Eq(link => link.companyId, companyId)
			);
			return _mongoCollection.Find(filter).FirstOrDefaultAsync();
		}
	}
}
