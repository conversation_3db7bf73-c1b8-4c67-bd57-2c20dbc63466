﻿using static BusinessObjects.DTOs.CalendarDTOs.GoogleCalendar;

namespace Services.Interfaces
{
    public interface ICustomCalendarService
    {
        Task<CreateCalendarResponse> CreateCalendarAsync(CreateCalendarRequest request);
        Task<string> CreateCalendarAsyncReturnId(CreateCalendarRequest request);
        Task<CreateEventResponse> CreateEventAsync(CreateEventRequest request);
        //Task<CreateEventResponse> CreateEventAsync(Appointment appointment, CreateEventRequestForAppointment request);
        /// <summary>
        /// Get all calendars using provided access token
        /// </summary>
        /// <param name="accessToken">Google Calendar access token</param>
        /// <returns>List of calendars</returns>
        Task<List<CalendarInfo>> GetCalendarsAsync(string accessToken);
        /// <summary>
        /// Get events from calendar using access token
        /// </summary>
        /// <param name="request">Event query parameters including access token</param>
        /// <returns>List of calendar events</returns>
        Task<List<EventInfo>> GetEventsAsync(GetEventsRequest request);
        Task<CreateEventResponse> UpdateEventAsync(string calendarId, string eventId, UpdateEventRequest request);
        Task<DeleteEventResponse> DeleteEventAsync(string calendarId, string eventId, string accessToken);
        Task<EventInfo> GetEventByIdAsync(string accessToken, string calendarId, string eventId);


        Task<string> CreateUserCalendarAsync(string accessToken, string userId);
        Task<bool> CalendarExistsAsync(string accessToken, string calendarId);
        Task<string> GetOrCreateUserCalendarAsync(string accessToken, string userId, string calendarId);
        Task<List<CalendarInfo>> GetUserCalendarsAsync(string accessToken);
        Task<bool> DeleteCalendarAsync(string accessToken, string calendarId);

        //Task<bool> UpdateCalendarEventAttendeesAsync(string eventId, string organizerId, List<string> attendeeEmails);
        //Task<bool> AddAttendeeToCalendarEventAsync(string eventId, string organizerId, string attendeeEmail, string attendeeName);
        //Task<bool> RemoveAttendeeFromCalendarEventAsync(string eventId, string organizerId, string attendeeEmail);

    }
}
