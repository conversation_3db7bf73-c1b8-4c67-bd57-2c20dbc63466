﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.ChatMessageDTOs;
using BusinessObjects.DTOs.FacebookDTOs;
using BusinessObjects.QueryObject;

namespace Services.Interfaces
{
    public interface IChatMessageService
    {
        Task HandleIncomingZaloMessageAsync(ZaloWebhookPayload payload);
        Task SendFromCrmToZaloAsync(string customerProfileId, string text, string conversationId);
        Task HandleIncomingMessengerMessageAsync(MessengerWebhookPayload payload);
        Task SendFromCrmToSystemAsync(ChatMessageCreateRequest request);
        Task SendFromCrmToMessengerAsync(string customerProfileId, string text, string conversationId);
        Task<(bool isNewConversation, string userId, string conversationId)> HandleIncomingSystemMessageAsync(ChatMessageCreateRequest request);
        Task<List<ChatMessageResponse>> GetChatMessagesByConversationIdAsync(string conversationId, QueryChatMessage query);
        Task DeleteByIdAsync(string id);

        Task PinMessageAsync(string messageId, string conversationId);
        Task UnpinMessageAsync(string messageId, string conversationId);
        Task CreateMessageAsync(string senderId, ChatMessageCreateRequest request);
    }
}
