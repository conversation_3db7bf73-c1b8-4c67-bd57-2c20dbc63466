﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.TeamDTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Services.Interfaces;

namespace Controllers.Controllers
{
	[Route("api/team")]
	[ApiController]
	public class TeamController : ControllerBase
	{
		private readonly ITeamService _teamService;
		public TeamController(ITeamService teamService)
		{
			_teamService = teamService;
		}

		private ApiResponse CreateResponse(int code, bool status, string message, object data = null)
		{
			return new ApiResponse { code = code, status = status, message = message, data = data };
		}

		[HttpPost]
		[Authorize]
		public async Task<IActionResult> CreateTeam([FromForm] TeamCreateRequest request)
		{
			var companyId = User.Claims.FirstOrDefault(c => c.Type == "CompanyID")?.Value;
			if (string.IsNullOrEmpty(companyId))
			{
				return BadRequest(CreateResponse(400, false, "CompanyID not found in token."));
			}
			try
			{
				var result = await _teamService.CreateTeamAsync(request, companyId);
				return Ok(CreateResponse(201, true, "Create team successfully", result));
			}
			catch (Exception ex)
			{
				return BadRequest(CreateResponse(500, false, ex.Message));
			}
		}

		[HttpPut("{id}")]
		[Authorize]
		public async Task<IActionResult> UpdateTeam([FromForm] TeamUpdateRequest request,string id)
		{
			try
			{
				var result = await _teamService.UpdateTeamAsync(request, id);
				return Ok(CreateResponse(200, true, "Update team successfully", result));
			}
			catch (Exception ex)
			{
				return BadRequest(CreateResponse(500, false, ex.Message));
			}
		}

		[HttpDelete("{id}")]
		[Authorize]
		public async Task<IActionResult> DeleteTeam(string id)
		{
			try
			{
				await _teamService.DeleteTeamAsync(id);
				return Ok(CreateResponse(200, true, "Delete team successfully"));
			}
			catch (Exception ex)
			{
				return BadRequest(CreateResponse(500, false, ex.Message));
			}
		}

		[HttpGet]
		[Authorize]
		public async Task<IActionResult> GetAllTeams()
		{
			try
			{
				var teams = await _teamService.GetAll();
				return Ok(CreateResponse(200, true, "Fetch all teams successfully", teams));
			}
			catch (Exception ex)
			{
				return BadRequest(CreateResponse(500, false, ex.Message));
			}
		}

		[HttpGet("{id}")]
		[Authorize]
		public async Task<IActionResult> GetTeamById(string id)
		{
			try
			{
				var team = await _teamService.GetTeamByIdAsync(id);
				if (team == null)
				{
					return NotFound(CreateResponse(404, false, "Team not found"));
				}
				return Ok(CreateResponse(200, true, "Fetch team successfully", team));
			}
			catch (Exception ex)
			{
				return BadRequest(CreateResponse(500, false, ex.Message));
			}
		}
	}
}
