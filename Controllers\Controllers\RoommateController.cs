﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.SwipeDTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Services.Interfaces;

namespace Controllers.Controllers
{
    [Route("api/roommate")]
    [ApiController]
    public class RoommateController : ControllerBase
    {
        private readonly ISwipeService _swipeService;
        private readonly IMatchingService _matchingService;

        public RoommateController(ISwipeService swipeService, IMatchingService matchingService)
        {
            _swipeService = swipeService;
            _matchingService = matchingService;
        }

        private ApiResponse CreateResponse(int code, bool status, string message, object data = null)
        {
            return new ApiResponse { code = code, status = status, message = message, data = data };
        }

        [Authorize]
        [HttpPost("swipe")]
        public async Task<IActionResult> Swipe([FromBody] SwipeRequest request)
        {
            try
            {
                var currentUserId = User.Claims.FirstOrDefault(c => c.Type == "UserID")?.Value;
                var result = await _swipeService.HandleSwipeAsync(currentUserId, request);

                if (result.isMatched)
                {
                    return Ok(CreateResponse(
                        code: 200,
                        status: true,
                        message: "It's a match! ",
                        data: new
                        {
                            matchedUserId = result.matchedUserId,
                            matchedAt = result.matchedAt
                        }
                    ));
                }

                return Ok(CreateResponse(
                    code: 200,
                    status: true,
                    message: "Swipe recorded"
                ));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateResponse(500, false, $"Đã xảy ra lỗi: {ex.Message}"));
            }
        }

        [Authorize]
        [HttpGet("matches")]
        public async Task<IActionResult> GetMatches()
        {
            try
            {
                var currentUserId = User.Claims.FirstOrDefault(c => c.Type == "UserID")?.Value;

                if (string.IsNullOrEmpty(currentUserId))
                {
                    return Unauthorized(CreateResponse(401, false, "Unauthorized"));
                }

                var result = await _matchingService.GetMatchesForUserAsync(currentUserId);

                return Ok(CreateResponse(
                    code: 200,
                    status: true,
                    message: "Danh sách ghép đôi hiện tại",
                    data: result
                ));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateResponse(500, false, $"Đã xảy ra lỗi: {ex.Message}"));
            }
        }

        [Authorize]
        [HttpGet("suggestions")]
        public async Task<IActionResult> GetRoommateSuggestions([FromQuery] int page = 1, [FromQuery] int pageSize = 20)
        {
            try
            {
                var currentUserId = User.Claims.FirstOrDefault(c => c.Type == "UserID")?.Value;

                if (string.IsNullOrEmpty(currentUserId))
                {
                    return Unauthorized(CreateResponse(401, false, "Unauthorized"));
                }

                var suggestions = await _matchingService.GetRoommateSuggestionsAsync(currentUserId, page, pageSize);

                return Ok(CreateResponse(
                    code: 200,
                    status: true,
                    message: "Danh sách gợi ý ghép đôi",
                    data: suggestions
                ));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateResponse(500, false, $"Đã xảy ra lỗi: {ex.Message}"));
            }
        }

        [Authorize]
        [HttpDelete("matches/{matchId}")]
        public async Task<IActionResult> Unmatch(string matchId)
        {
            try
            {
                var currentUserId = User.Claims.FirstOrDefault(c => c.Type == "UserID")?.Value;

                if (string.IsNullOrEmpty(currentUserId))
                    return Unauthorized(CreateResponse(401, false, "Unauthorized"));

                var result = await _matchingService.UnmatchAsync(matchId, currentUserId);

                if (!result)
                    return NotFound(CreateResponse(404, false, "Không tìm thấy hoặc không có quyền hủy ghép đôi"));

                return StatusCode(200, CreateResponse(200, true, "Hủy ghép đôi thành công"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateResponse(500, false, $"Đã xảy ra lỗi: {ex.Message}"));
            }
        }
    }
}
