﻿using BusinessObjects.DTOs.SwipeDTOs;
using BusinessObjects.Models;
using Repositories.Interfaces;
using Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Services.Implements
{
    public class SwipeService : ISwipeService
    {
        private readonly IUnitOfWork _unitOfWork;
        public SwipeService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;   
        }
        public async Task<SwipeResult> HandleSwipeAsync(string fromUserId, SwipeRequest request)
        {
            var swipe = new Swipe
            {
                fromUserId = fromUserId,
                toUserId = request.toUserId,
                isLike = request.isLike,
                swipedAt = DateTime.UtcNow
            };
            await _unitOfWork.Swipes.AddAsync(swipe);

            if (request.isLike)
            {
                var reverse = await _unitOfWork.Swipes.GetSwipeAsync(request.toUserId,fromUserId);
                if(reverse?.isLike == true) {
                    var match = new Matching
                    {
                        userAId = fromUserId,
                        userBId = request.toUserId,
                        matchedAt = DateTime.UtcNow,
                        isActive = true
                    };
                    await _unitOfWork.Matchings.AddAsync(match);
                    await _unitOfWork.SaveAsync();
                    return new SwipeResult
                    {
                        isMatched = true,
                        matchedUserId = request.toUserId,
                        matchedAt = DateTime.UtcNow,
                    };
                }
            }
            return new SwipeResult
            {
                isMatched = false
            };
        }
    }
}
