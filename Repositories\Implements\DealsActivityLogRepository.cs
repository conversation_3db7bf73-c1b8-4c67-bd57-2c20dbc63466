﻿using BusinessObjects.Models;
using MongoDB.Driver;
using Repositories.Interfaces;

namespace Repositories.Implements
{
    public class DealsActivityLogRepository : GenericRepository<DealsActivityLog>, IDealsActivityLogRepository
    {
        private readonly IMongoCollection<DealsActivityLog> _dealsActivityLogs;
        public DealsActivityLogRepository(BusinessObjects.DatabaseSettings.RevoLandDbContext context) : base(context, "DealsActivityLogs")
        {
            _dealsActivityLogs = context.GetCollection<DealsActivityLog>("DealsActivityLogs");
        }


        public async Task<long> CountDealsActivityLogByFilterAsync(FilterDefinition<DealsActivityLog> filter)
        {
            return await _dealsActivityLogs.CountDocumentsAsync(filter);
        }

        public async Task<List<DealsActivityLog>> GetDealsActivityLogByFilterAsync(
            FilterDefinition<DealsActivityLog> filter,
            SortDefinition<DealsActivityLog> sort,
            int skip,
            int limit)
        {
            return await _dealsActivityLogs.Find(filter).Sort(sort).Skip(skip).Limit(limit).ToListAsync();
        }
    }

}
