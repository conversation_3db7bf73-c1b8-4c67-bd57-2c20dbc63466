﻿using BusinessObjects.Models;
using Microsoft.AspNetCore.Http;

namespace Services.Interfaces
{
    public interface IAttachmentService
    {
        Task<List<Attachment>> UploadAttachmentsAsync(List<IFormFile> files);
        Task<List<Attachment>> UploadFloorPlanAsync(List<IFormFile> files);

        Task<Attachment> UploadPropertyVideoAsync(IFormFile file);
        Task<List<Attachment>> UploadPropertyImagesAsync(List<IFormFile> files);

        Task<List<Attachment>> UploadPropertyLegalDocumentAsync(List<IFormFile> files);
    }
}
