﻿using BusinessObjects.Models;
using BusinessObjects.QueryObject;
using Microsoft.AspNetCore.Http;
using Repositories.Interfaces;
using Services.Interfaces;
using System.Security.Claims;

namespace Services.Implements
{
    public class SearchService : ISearchService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IHttpContextAccessor _httpContextAccessor;


        public async Task<Search> GetSearchAsync(string id)
        {
            return await _unitOfWork.Search.GetByIdAsync(id);
        }
        public SearchService(IUnitOfWork unitOfWork, IHttpContextAccessor httpContextAccessor)
        {
            _unitOfWork = unitOfWork;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task AddSearchAsync(QueryProperty searchCreateRequest)
        {
            var httpContext = _httpContextAccessor.HttpContext;
            var userId = httpContext?.User?.FindFirstValue("UserID");

            if (!string.IsNullOrEmpty(userId))
            {
                var search = new Search
                {
                    searchTerm = searchCreateRequest.searchTerm,
                    userId = userId,
                    amenityFilters = searchCreateRequest.amenityFilters,
                    apartmentOrientation = searchCreateRequest.apartmentOrientation,
                    bathrooms = searchCreateRequest.bathrooms,
                    bedrooms = searchCreateRequest.minBedrooms,
                    buildingArea = searchCreateRequest.minBuildingArea,
                    floorNumber = searchCreateRequest.floorNumber,
                    isDescending = searchCreateRequest.isDescending,
                    kitchens = searchCreateRequest.kitchens,
                    landArea = searchCreateRequest.minLandArea,
                    landLength = searchCreateRequest.landLength,
                    landWidth = searchCreateRequest.landWidth,
                    livingRooms = searchCreateRequest.minLivingRooms,
                    numberOfFloors = searchCreateRequest.numberOfFloors,
                    propertyDetailFilters = searchCreateRequest.propertyDetailFilters,
                    sortBy = searchCreateRequest.sortBy,
                    status = searchCreateRequest.status,
                    type = searchCreateRequest.type,
                };
                await _unitOfWork.Search.AddAsync(search);
            }
            else
            {
                // Không có token => lưu vào cookie (list search)
                const string cookieKey = "anonymous_search";

                var existingSearchesJson = httpContext?.Request.Cookies[cookieKey];
                var searches = new List<QueryProperty>();

                if (!string.IsNullOrEmpty(existingSearchesJson))
                {
                    try
                    {
                        searches = System.Text.Json.JsonSerializer.Deserialize<List<QueryProperty>>(existingSearchesJson)
                                   ?? new List<QueryProperty>();
                    }
                    catch
                    {
                        // Nếu lỗi đọc cookie => reset list mới
                        searches = new List<QueryProperty>();
                    }
                }

                // Thêm search mới vào danh sách
                searches.Add(searchCreateRequest);

                if (searches.Count > 5)
                {
                    searches = searches.Skip(searches.Count - 5).ToList();
                }

                // Serialize lại list => lưu vào cookie
                var updatedSearchesJson = System.Text.Json.JsonSerializer.Serialize(searches);

                httpContext?.Response.Cookies.Append(cookieKey, updatedSearchesJson, new CookieOptions
                {
                    Expires = DateTimeOffset.UtcNow.AddDays(7),
                    HttpOnly = false,
                    Secure = true,
                    SameSite = SameSiteMode.Lax
                });
            }
        }


    }
}
