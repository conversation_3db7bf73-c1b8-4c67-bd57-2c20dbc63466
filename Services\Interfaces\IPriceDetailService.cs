﻿using BusinessObjects.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Services.Interface
{
    public interface IPriceDetailService
    {
        Task<IEnumerable<PriceDetail>> GetAllPriceDetailsAsync();
        Task<PriceDetail> GetPriceDetailByIdAsync(string id);
        Task<PriceDetail> AddPriceDetailAsync(PriceDetail priceDetail);
        Task<PriceDetail> UpdatePriceDetailAsync(string id, PriceDetail priceDetail);
        Task<bool> DeletePriceDetailAsync(string id);
    }
}
