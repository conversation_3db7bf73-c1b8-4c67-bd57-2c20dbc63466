﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System.ComponentModel.DataAnnotations;


namespace BusinessObjects.Models
{
    public class CollectionProperty
    {

        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public ObjectId id { get; set; }

        [BsonElement("collectionProperties")]
        public List<string> listProperties { get; set; } = new List<string>();

        [Required]
        public string? collectionName { get; set; }
        [Required]
        [BsonElement("collectionDescription")]
        public string description { get; set; }
        [BsonElement("collectionImage")]
        public List<string> collectionImage { get; set; } = new List<string>();

        public string userId { get; set; }

    }
}
