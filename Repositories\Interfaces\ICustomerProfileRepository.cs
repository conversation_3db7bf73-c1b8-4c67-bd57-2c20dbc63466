﻿using BusinessObjects.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Repositories.Interfaces
{
    public interface ICustomerProfileRepository : IGenericRepository<CustomerProfile>
    {
        Task<CustomerProfile?> GetByPlatformUserIdAsync(string platform, string platformUserId);
        Task UpdateLastInteraction(string id);
    }
}
