﻿using BusinessObjects.DTOs.MatchDTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Services.Interfaces
{
    public interface IMatchingService
    {
        Task<List<MatchResponse>> GetMatchesForUserAsync(string userId);
        Task<bool> UnmatchAsync(string matchId, string userId);
        Task<List<MatchResponse>> GetRoommateSuggestionsAsync(string userId, int page = 1, int pageSize = 20);
    }
}
