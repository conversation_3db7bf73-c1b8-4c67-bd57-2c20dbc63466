﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.BuyerDTOs;
using BusinessObjects.Models;
using BusinessObjects.QueryObject;
using FirebaseAdmin.Auth.Multitenancy;
using Repositories.Interfaces;
using Services.Interfaces;
using Services.Tools;

namespace Services.Implements
{
	public class BuyerService : IBuyerService
	{
		private readonly IUnitOfWork _unitOfWork;
		private readonly UserPasswordHasher _userPasswordHasher;
		private readonly IUserService _userService;

		public BuyerService(IUnitOfWork unitOfWork, UserPasswordHasher userPasswordHasher, IUserService userService)
		{
			_unitOfWork = unitOfWork;
			_userPasswordHasher = userPasswordHasher;
			_userService = userService;
		}
		public async Task<Buyer> AddBuyerAsync(BuyerCreateRequest buyer)
		{
			if (await _unitOfWork.Buyers.IsPhoneExist(buyer.phone))
			{
				throw new ArgumentException("<PERSON><PERSON> điện thoại đã được sử dụng.");
			}

			if (await _unitOfWork.Buyers.IsEmailExist(buyer.email))
			{
				throw new ArgumentException("Email đã được sử dụng.");
			}
			var newBuyer = new Buyer
			{
				address = buyer.address,
				createdAt = DateTime.UtcNow,
				email = buyer.email,
				idVerifications = buyer.idVerifications,
				name = buyer.name,
				phone = buyer.phone,
				updatedAt = DateTime.UtcNow,
				bankCode = buyer.bankCode,
				bankName = buyer.bankName,
				faxCode = buyer.faxCode,
				taxCode = buyer.taxCode
			};

			await _unitOfWork.Buyers.AddAsync(newBuyer);

			return newBuyer;
		}

		public async Task CreateUserFromBuyerAsync(BuyerCreateRequest buyer)
		{
			var email_exist = await _userService.FindUserByEmailAsync(buyer.email) != null
				|| await _userService.FindUserByPhonenumberAsync(buyer.phone) != null;

			if (!email_exist)
			{
				var userdto = new UserDTO()
				{
					about = "Buyer",
					email = buyer.email,
					fullName = buyer.name,
					joinedAt = DateTime.UtcNow,
					phoneNumber = buyer.phone,
					userName = buyer.name,
					role = UserRole.User,
					status = Status.Online
				};
				var password = _userPasswordHasher.HashPassword(userdto, "QuindLand");
				var user = new User()
				{
					fullName = userdto.fullName,
					status = userdto.status,
					role = userdto.role,
					about = userdto.about,
					avatar = userdto.avatar,
					birthdate = userdto.birthdate,
					email = userdto.email,
					joinedAt = userdto.joinedAt,
					password = password,
					phoneNumber = userdto.phoneNumber,
					userName = userdto.userName,
				};
				await _unitOfWork.Users.AddAsync(user);
			}
		}

		public async Task<Buyer> DeleteBuyerAsync(string id) => await _unitOfWork.Buyers.DeleteAsync(id);

		public async Task<IEnumerable<Buyer>> GetAllBuyersAsync() => await _unitOfWork.Buyers.GetAllAsync();

		public async Task<List<Buyer>> GetAllBuyersAsync(QueryBuyer query)
		{
			if (query.searchTerm == null)
			{
				query.searchTerm = "";
			}

			var result = await _unitOfWork.Buyers.GetAllAsync();
			var tenantList = new List<Buyer>();

			if (result == null || !result.Any())
			{
				return new List<Buyer>();
			}

			// Tìm kiếm theo tên, số điện thoại, email, ngân hàng, mã thuế
			result = result.Where(t => t.name.Contains(query.searchTerm, StringComparison.OrdinalIgnoreCase) ||
									   t.phone.Contains(query.searchTerm, StringComparison.OrdinalIgnoreCase) ||
									   t.email.Contains(query.searchTerm, StringComparison.OrdinalIgnoreCase) ||
									   t.address.Contains(query.searchTerm, StringComparison.OrdinalIgnoreCase) ||
									   t.bankName.Contains(query.searchTerm, StringComparison.OrdinalIgnoreCase) ||
									   t.bankCode.Contains(query.searchTerm, StringComparison.OrdinalIgnoreCase) ||
									   t.faxCode.Contains(query.searchTerm, StringComparison.OrdinalIgnoreCase) ||
									   t.taxCode.Contains(query.searchTerm, StringComparison.OrdinalIgnoreCase))
						   .ToList();

			// Phân trang
			var paginatedBuyers = result.Skip((query.pageNumber - 1) * query.pageSize)
										 .Take(query.pageSize)
										 .ToList();

			return paginatedBuyers;
		}

		public async Task<Buyer> GetBuyerByEmailAsync(string email)
		{
			var tenants = await _unitOfWork.Buyers.GetAllAsync();
			return tenants.FirstOrDefault(t => t.email.Equals(email, StringComparison.OrdinalIgnoreCase));
		}

		public async Task<Buyer> GetBuyerByIdAsync(string id) => await _unitOfWork.Buyers.GetByIdAsync(id);

		public async Task<Buyer> GetBuyerByPhoneAsync(string phone) => await _unitOfWork.Buyers.GetBuyerByPhone(phone);

		public async Task<bool> UpdateBuyerAsync(string id, BuyerUpdateRequest buyerRequest)
		{

			var buyer = await _unitOfWork.Buyers.GetByIdAsync(id);
			if (buyer == null) return false;

			if (await _unitOfWork.Buyers.IsPhoneExist(buyerRequest.phone))
			{
				throw new ArgumentException("Số điện thoại đã được sử dụng.");
			}

			if (await _unitOfWork.Buyers.IsEmailExist(buyerRequest.email))
			{
				throw new ArgumentException("Email đã được sử dụng.");
			}

			bool isUpdated = false;


			if (buyerRequest.address != buyer.address && buyerRequest.address != null)
			{
				buyer.address = buyerRequest.address;
				isUpdated = true;
			}

			if (buyerRequest.idVerifications.Count > 0 && buyerRequest.idVerifications != null)
			{
				buyer.idVerifications = buyerRequest.idVerifications;
				isUpdated = true;
			}

			if (buyerRequest.name != buyer.name && buyerRequest.name != null)
			{
				buyer.name = buyerRequest.name;
				isUpdated = true;
			}

			if (buyerRequest.phone != buyer.phone && buyerRequest.phone != null)
			{
				buyer.phone = buyerRequest.phone;
				isUpdated = true;
			}

			if (buyerRequest.email != buyer.email && buyerRequest.email != null)
			{
				buyer.email = buyerRequest.email;
				isUpdated = true;
			}

			if (buyerRequest.bankCode != buyer.bankCode && buyerRequest.bankCode != null)
			{
				buyer.bankCode = buyerRequest.bankCode;
				isUpdated = true;
			}

			if (buyerRequest.bankName != buyer.bankName && buyerRequest.bankName != null)
			{
				buyer.bankName = buyerRequest.bankName;
				isUpdated = true;
			}

			if (buyerRequest.taxCode != buyer.taxCode && buyerRequest.taxCode != null)
			{
				buyer.taxCode = buyerRequest.taxCode;
				isUpdated = true;
			}

			if (buyerRequest.faxCode != buyer.faxCode && buyerRequest.faxCode != null)
			{
				buyer.faxCode = buyerRequest.faxCode;
				isUpdated = true;
			}

			if (isUpdated)
			{
				buyer.updatedAt = DateTime.UtcNow;
				await _unitOfWork.Buyers.UpdateAsync(id, buyer);
				return true;
			}

			return false;
		}
	}
}
