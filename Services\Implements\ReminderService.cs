﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.DTOs.ReminderDTO;
using BusinessObjects.Models;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using Repositories.Interfaces;
using Services.Interfaces;
using System.Linq.Expressions;
using System.Text.RegularExpressions;


namespace Services.Implements
{
    public class ReminderService : IReminderService
    {
        private readonly IReminderRepository _reminderRepository;
        private readonly IEmailService _emailService;
        private readonly ILogger<ReminderService> _logger;
        private readonly IUserService _userService;

        public ReminderService(
            IReminderRepository reminderRepository,
            IEmailService emailService,
            IUserService userService,
            ILogger<ReminderService> logger)
        {
            _reminderRepository = reminderRepository;
            _emailService = emailService;
            _userService = userService;
            _logger = logger;
        }

        public async Task CreateSampleReminderAsync()
        {
            var deadline = DateTime.UtcNow.AddDays(7); 
            var email = "<EMAIL>";

            var emailRegex = new Regex(@"^[^\\s@]+@[^\\s@]+\.[^\\s@]+$");
            if (!emailRegex.IsMatch(email))
            {
                _logger.LogError($"Invalid email in sample reminder: {email}");
                return;
            }

            var reminder = new Reminder
            {
                id = Guid.NewGuid(),
                title = "QuinLand Reminder",
                message = "You have a job to be completed! Please check your information again.",
                deadlineDate = deadline,
                scheduledTime = DateTime.UtcNow.AddDays(7),
                email = email,
                phoneNumber = "1234567890",
                userId = Guid.NewGuid().ToString(),
                lastContactDate = DateTime.UtcNow
            };

            await _reminderRepository.AddReminderAsync(reminder);
            _logger.LogInformation("Created a new sample reminder.");

            var from = DateTime.UtcNow;
            var to = DateTime.UtcNow.AddHours(1);
            var remindersToSend = await GetUpcomingFollowUpsAsync(reminder.userId, from, to);

            foreach (var r in remindersToSend)
            {
                await SendReminderAsync(r);
            }
        }



        public async Task<List<Reminder>> GetRemindersToNotifyAsync()
        {
            Expression<Func<Reminder, bool>> predicate = r =>
                r.scheduledTime <= DateTime.UtcNow
                && !string.IsNullOrWhiteSpace(r.email) 
                && new Regex(@"^[^\\s@]+@[^\\s@]+\.[^\\s@]+$").IsMatch(r.email); 

            return await _reminderRepository.GetRemindersAsync(predicate);
        }

            public async Task SendReminderAsync(Reminder reminder)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(reminder.email))
                {
                    return; 
                }

                var emailRegex = new Regex(@"^[^\\s@]+@[^\\s@]+\.[^\\s@]+$");
                if (!emailRegex.IsMatch(reminder.email))
                {
                    return; 
                }
                var subject = $"Reminder: {reminder.title}";
                var body = $"<h3>Reminder</h3><p>{reminder.message}</p><p>Scheduled Time: {reminder.scheduledTime.ToLocalTime().ToString("f")}";

                await _emailService.SendEmailAsync(reminder.email, subject, body);
                await _reminderRepository.UpdateReminderAsync(reminder);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to send reminder {reminder.id}: {ex.Message}");
            }
        }

        public async Task<List<Reminder>> GetUpcomingFollowUpsAsync(string userId, DateTime from, DateTime to)
        {
            Expression<Func<Reminder, bool>> predicate = r =>
                r.userId == userId &&
                r.scheduledTime >= from &&
                r.scheduledTime <= to;

            var reminders = await _reminderRepository.GetRemindersAsync(predicate);
            return reminders.OrderBy(r => r.scheduledTime).ToList();
        }

        public async Task ScheduleReminder(ReminderDTO dto)
        {
            if (string.IsNullOrEmpty(dto.userId))
            {
                throw new ArgumentException("UserId is required.");
            }

            var userExists = await _userService.FindUserByIdAsync(dto.userId);
            if (userExists == null)
            {
                throw new ArgumentException($"User with ID {dto.userId} does not exist.");
            }

            if (string.IsNullOrWhiteSpace(dto.title))
            {
                _logger.LogError("Title is null or empty.");
                throw new ArgumentException("Title is required.");
            }
            if (dto.title.Length > 100)
            {
                _logger.LogError("Title exceeds 100 characters.");
                throw new ArgumentException("Title cannot exceed 100 characters.");
            }

            if (!string.IsNullOrEmpty(dto.message) && dto.message.Length > 500)
            {
                _logger.LogError("Message exceeds 500 characters.");
                throw new ArgumentException("Message cannot exceed 500 characters.");
            }


            if (!string.IsNullOrEmpty(dto.email))
            {
                var emailRegex = new Regex(@"^[^\\s@]+@[^\\s@]+\.[^\\s@]+$");
                if (!emailRegex.IsMatch(dto.email))
                {
                    _logger.LogError($"Invalid email format: {dto.email}.");
                    throw new ArgumentException("Invalid email format.");
                }
            }

            if (!string.IsNullOrEmpty(dto.phoneNumber))
            {
                var phoneRegex = new Regex(@"^\+?[0-9]\d{8,11}$"); 
                if (!phoneRegex.IsMatch(dto.phoneNumber))
                {
                    _logger.LogError($"Invalid phone number format: {dto.phoneNumber}.");
                    throw new ArgumentException("Invalid phone number format.");
                }
            }

            var scheduled = CalculateScheduledTime(dto.scheduledTime);
            var reminder = new Reminder
            {
                userId = dto.userId,
                title = dto.title,
                message = dto.message,
                deadlineDate = dto.scheduledTime,
                scheduledTime = scheduled,
                email = dto.email,
                phoneNumber = dto.phoneNumber,
                lastContactDate = DateTime.UtcNow,
            };

            await _reminderRepository.AddReminderAsync(reminder);
        }

        public DateTime CalculateScheduledTime(DateTime deadline)
        {
            var now = DateTime.UtcNow;

            var timeUntilDeadline = deadline - now;

            if (timeUntilDeadline.TotalHours <= 2)
            {
                
                return deadline.AddHours(-1);
            }
            else if (timeUntilDeadline.TotalDays <= 4)
            {
                return deadline.AddDays(-1);
            }
            else if (timeUntilDeadline.TotalDays >= 60 && timeUntilDeadline.TotalDays < 150)
            {
                return deadline.AddMonths(-1);
            }
            else if (timeUntilDeadline.TotalDays >= 150)
            {
                return deadline.AddMonths(-3);
            }
            else
            {
                return deadline.AddDays(-2);
            }
        }
    }
}