using BusinessObjects.DTOs.FavoritePropertyDTOs;
using BusinessObjects.DTOs.PropertyDTO;
using BusinessObjects.QueryObject;

namespace Services.Interfaces
{
    public interface IFavoritePropertyService
    {
        Task<FavoritePropertyCreateRequest> AddFavoritePropertyAsync(FavoritePropertyCreateRequest request);
        Task<List<PropertySearchResponse>> GetFavoritePropertyByUserIdAsync(QueryProperty query, string userId);
        Task DeleteFavoritePropertyAsync(string propertyId);
    }
}