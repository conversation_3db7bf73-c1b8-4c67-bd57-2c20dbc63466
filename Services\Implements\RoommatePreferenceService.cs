using BusinessObjects.DTOs.RoommatePreferenceDTOs;
using BusinessObjects.Models;
using MongoDB.Driver;
using Repositories.Interfaces;
using Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Services.Implements
{
    public class RoommatePreferenceService : IRoommatePreferenceService
    {
        private readonly IRoommatePreferenceRepository _roommatePreferenceRepository;

        public RoommatePreferenceService(IRoommatePreferenceRepository roommatePreferenceRepository)
        {
            _roommatePreferenceRepository = roommatePreferenceRepository;
        }

        public async Task<RoommatePreferenceResponse> GetRoommatePreferenceByUserIdAsync(string userId)
        {
            var preference = await _roommatePreferenceRepository.GetByUserIdAsync(userId);
            
            if (preference == null)
            {
                return null;
            }

            return MapToResponse(preference);
        }

        public async Task<RoommatePreferenceResponse> UpdateRoommatePreferenceAsync(string userId, RoommatePreferenceUpdateRequest request)
        {
            var existingPreference = await _roommatePreferenceRepository.GetByUserIdAsync(userId);
            
            if (existingPreference == null)
            {
                // Create new preference if it doesn't exist
                var newPreference = new RoommatePreference
                {
                    userId = userId,
                    preferredGender = request.preferredGender,
                    ageMin = request.ageMin,
                    ageMax = request.ageMax,
                    allowPets = request.allowPets,
                    allowSmoking = request.allowSmoking,
                    occupation = request.occupation,
                    preferredLifestyles = request.preferredLifestyles,
                    interests = request.interests,
                    updatedAt = DateTime.UtcNow
                };

                await _roommatePreferenceRepository.AddAsync(newPreference);
                return MapToResponse(newPreference);
            }
            else
            {
                // Update existing preference
                existingPreference.preferredGender = request.preferredGender;
                existingPreference.ageMin = request.ageMin;
                existingPreference.ageMax = request.ageMax;
                existingPreference.allowPets = request.allowPets;
                existingPreference.allowSmoking = request.allowSmoking;
                existingPreference.occupation = request.occupation;
                existingPreference.preferredLifestyles = request.preferredLifestyles;
                existingPreference.interests = request.interests;
                existingPreference.updatedAt = DateTime.UtcNow;

                await _roommatePreferenceRepository.ReplaceOneAsync(p => p.userId == userId, existingPreference);
                return MapToResponse(existingPreference);
            }
        }

        private RoommatePreferenceResponse MapToResponse(RoommatePreference preference)
        {
            return new RoommatePreferenceResponse
            {
                Id = preference.id,
                userId = preference.userId,
                preferredGender = preference.preferredGender,
                ageMin = preference.ageMin,
                ageMax = preference.ageMax,
                allowPets = preference.allowPets,
                allowSmoking = preference.allowSmoking,
                occupation = preference.occupation,
                preferredLifestyles = preference.preferredLifestyles,
                updatedAt = preference.updatedAt,
                interests = preference.interests,
                
            };
        }
    }
} 