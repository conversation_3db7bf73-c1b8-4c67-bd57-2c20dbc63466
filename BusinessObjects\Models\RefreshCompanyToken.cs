﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.Models
{
	public class RefreshCompanyToken
	{
		[BsonId]
		[BsonRepresentation(BsonType.ObjectId)]
		public string id { get; set; } = ObjectId.GenerateNewId().ToString();
		public string companyId { get; set; }
		[ForeignKey(nameof(companyId))]
		public Company company { get; set; }
		public string token { get; set; }
		public string jwtId { get; set; }
		public bool isUsed { get; set; }
		public bool isRevoked { get; set; }
		public DateTime issuedAt { get; set; }
		public DateTime expiredAt { get; set; }
	}
}
