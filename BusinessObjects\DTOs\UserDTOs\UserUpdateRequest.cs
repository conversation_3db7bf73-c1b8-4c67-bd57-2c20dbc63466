﻿using BusinessObjects.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.DTOs.UserDTOs
{
    public class UserUpdateRequest
    {
        public string? userName { get; set; }
        public string? fullName { get; set; }
        [EmailAddress]
        public string? email { get; set; }
        [Phone]
        public string? phoneNumber { get; set; }
        public Status? status { get; set; }
        public string? about { get; set; }
        public DateTime? birthDate { get; set; }

    }
}
