﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.ReminderDTO;
using BusinessObjects.Models;
using Microsoft.AspNetCore.Mvc;
using Services.Interfaces;




namespace Controllers.Controllers
{

    [Route("api/reminders")]
    [ApiController]
    public class ReminderController :ControllerBase
    {
        private readonly IReminderService _reminderService;

        public ReminderController(IReminderService reminderService)
        {
            _reminderService = reminderService;
        }

        private ApiResponse CreateResponse(int code, bool status, string message, object data = null)
        {
            return new ApiResponse { code = code, status = status, message = message, data = data };
        }

        [HttpPost("schedule")]
        public async Task<IActionResult> ScheduleReminder([FromBody] ReminderDTO dto)
        {
            try
            {
                await _reminderService.ScheduleReminder(dto);
                return Ok(CreateResponse(200, true, "Reminder scheduled successfully."));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(400, false, "Reminder scheduled failed " + ex.Message));
            }
        }

        //[HttpPost("process")]
        //public async Task<IActionResult> ProcessReminders()
        //{
        //    try
        //    {
        //        await _reminderService.ProcessPendingReminders();
        //        return Ok(CreateResponse(200, true, "Pending reminders processed."));
        //    }
        //    catch (Exception ex)
        //    {
        //        return BadRequest(CreateResponse(400, false, " Process Reminder Failed " + ex.Message));
        //    }
        //}

        [HttpGet("upcoming-followups/{userId}")]
        public async Task<ActionResult<List<Reminder>>> GetUpcomingFollowUps(String userId)
        {
            try
            {
                if (string.IsNullOrEmpty(userId))
                {
                    return BadRequest("UserId is required.");
                }

                // Define a time range, e.g., from now to the next 7 days
                var from = DateTime.UtcNow;
                var to = from.AddDays(7);

                var followUps = await _reminderService.GetUpcomingFollowUpsAsync(userId, from, to);
                return Ok(CreateResponse(200, true, "Get Upcoming-followups successfully.", followUps));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(400, false, "Get Upcoming-followups Failed " + ex.Message));
            }
        }

    }
}
