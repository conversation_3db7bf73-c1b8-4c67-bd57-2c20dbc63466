﻿using BusinessObjects.QueryObject;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace BusinessObjects.Models
{
    public class Search
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string? Id { get; set; }
        [BsonRepresentation(BsonType.ObjectId)]
        public string? userId { get; set; }
        public string? searchTerm { get; set; }
        public bool isDescending { get; set; } = false;
        public List<PropertyStatus>? status { get; set; } = new();


        public List<PropertyType>? type { get; set; } = new();


        public List<PropDetailBoolFilter>? propertyDetailFilters { get; set; }


        public List<AmenityFilter>? amenityFilters { get; set; }

        public int? bedrooms { get; set; }
        public int? bathrooms { get; set; }
        public int? livingRooms { get; set; }
        public int? kitchens { get; set; }
        public double? landArea { get; set; }
        public double? landWidth { get; set; }
        public double? landLength { get; set; }
        public double? buildingArea { get; set; }
        public int? numberOfFloors { get; set; }
        public int? floorNumber { get; set; }

        [BsonRepresentation(BsonType.String)]
        public List<ApartmentOrientation>? apartmentOrientation { get; set; }
        public PropertyEnumSortBy sortBy { get; set; } = PropertyEnumSortBy.name;
        public DateTime createdAt { get; set; } = DateTime.UtcNow;
        public DateTime? updatedAt { get; set; } = DateTime.UtcNow;

    }
}
