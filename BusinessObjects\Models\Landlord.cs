﻿using BusinessObjects.Validations;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.Models
{
    public class Landlord
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        [BsonElement("landlordId")]
        public string id { get; set; } = ObjectId.GenerateNewId().ToString();

        [Required]
        public string? name { get; set; }

        [EmailAddress]
        [Required]
        public string? email { get; set; }

        [Required]
        [BsonElement("phone")]
        [PhoneVN]

        public string? phone { get; set; }

        [Required]
        public string? address { get; set; }

        public DateTime createdAt { get; set; }

        public DateTime updatedAt { get; set; }

    }
}
