﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace BusinessObjects.Models
{
    public class Deal
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public ObjectId id { get; set; } = ObjectId.GenerateNewId();

        [BsonElement("leadId")]
        [BsonRepresentation(BsonType.ObjectId)]
        public string leadId { get; set; }

        [BsonElement("salesRepId")]
        [BsonRepresentation(BsonType.ObjectId)]
        public string salesRepId { get; set; }
        [BsonRepresentation(BsonType.String)]
        public DealStatus status { get; set; } = DealStatus.New;
        public decimal position { get; set; }

        [BsonElement("title")]
        public string title { get; set; }

        [BsonElement("description")]
        public string description { get; set; }

        public List<DealProperty> properties { get; set; } = new List<DealProperty>();

        public List<DealNote> notes { get; set; } = new List<DealNote>();

        [BsonElement("created_at")]
        public DateTime createdAt { get; set; } = DateTime.UtcNow;

        [BsonElement("updated_at")]
        public DateTime updatedAt { get; set; } = DateTime.UtcNow;

        [BsonElement("status_updateAt")]
        public DateTime statusUpdateAt { get; set; }

        [BsonElement("priority")]
        [BsonRepresentation(BsonType.String)]
        public DealPriority priority { get; set; }

        [BsonElement("isDeleted")]
        public bool isDeleted { get; set; } = false;

        public enum DealStatus
        {
            New,
            Contacted,
            Negotiation,
            Closing,
            Won,
            Lost
        }
        public enum DealPriority
        {
            Low,
            Medium,
            High
        }

        public class DealNote
        {
            [BsonId]
            [BsonRepresentation(BsonType.ObjectId)]
            public ObjectId id { get; set; } = ObjectId.GenerateNewId();

            public string? title { get; set; }

            [BsonElement("content")]
            public required string content { get; set; }
            [BsonElement("created_at")]
            public DateTime createdAt { get; set; } = DateTime.UtcNow;
            [BsonElement("updated_at")]
            public DateTime updatedAt { get; set; } = DateTime.UtcNow;
            [BsonElement("created_by")]
            [BsonRepresentation(BsonType.ObjectId)]
            public string? createdBy { get; set; }

        }

        public class DealProperty
        {
            [BsonId]
            [BsonRepresentation(BsonType.ObjectId)]
            public ObjectId id { get; set; }

            public PropertyType type { get; set; }

            public double value { get; set; }
            public bool isFinalized { get; set; } = false;

            [BsonElement("created_at")]
            public DateTime createdAt { get; set; } = DateTime.UtcNow;
            [BsonElement("updated_at")]
            public DateTime updatedAt { get; set; } = DateTime.UtcNow;
        }
    }
}
