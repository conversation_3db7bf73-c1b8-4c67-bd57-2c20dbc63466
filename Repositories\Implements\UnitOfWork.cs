﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using Repositories.Interfaces;

namespace Repositories.Implements
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly RevoLandDbContext _context;
        private IGenericRepository<User> _users;
        private IGenericRepository<RefreshToken> _refreshTokens;
        private IRentalContractReminderRepository _rentalContractReminder;
        private IRentalContractNotificationRepository _rentalContractNotification;
        private IGenericRepository<AnonymousSession> _anonymousSessions;
        private ILocationRepository _location;
        private IAmenityRepository _amenities;
        private IOwnerRepository _owners;
        private IFavoritePropertyRepository _favoriteProperties;
        private IPriceDetailRepository _priceDetails;
        private IPropertyRepository _property;
        private IPropertyDetailRepository _propertyDetails;
        private ITransactionHistoryRepository _transactionHistory;
        private ITenantRepository _tenants;
        private ILeadRepository _leads;
        private IMatchingRepository _matching;
        private ISwipeRepository _swipes;
        private ILandlordRepository _landlords;
        private IContractRepository _contracts;
        private IDealRepository _deals;
        private IDealsActivityLogRepository _dealsActivityLogs;
        private ISearchRepository _search;
        private IAppointmentRepository _appointments;
        private IChatMessageRepository _chatMessages;
        private IConversationRepository _conversations;
        private ICustomerProfileRepository _customerProfiles;
        private IZaloTokenRepository _zaloTokens;
        private IAttachmentRepository _attachments;
        private IChatSessionRepository _chatSessions;
        private ISurveyRepository _surveys;
        public IGoogleCrendentialRepository GoogleCredentials { get; }
        private IPropertyViewHistoryRepository _propertyViewHistories;
        private IDraftPropertyRepository _draftProperties;
        private IGenericRepository<Company> _companies;
        private ITeamRepository _teams;
        private IAgentRepository _agents;
        private IAgentInviteRepository _agentInvites;
        private IAgentCompanyLinkRepository _agentCompanyLinks;
        private IAgentActivityLogRepository _agentActivityLogs;
        private IBuyerRepository _buyers;
        private IRoommatePreferenceRepository _roommatePreferences;
        private ICollectionPropertyRepository _collectionProperties;

        public UnitOfWork(RevoLandDbContext context, IGoogleCrendentialRepository googleCredentials)
        {
            _context = context;
            GoogleCredentials = googleCredentials;
        }

        public IAmenityRepository Amenities => _amenities ??= new AmenityRepository(_context);

        public ILocationRepository Locations => _location ??= new LocationRepository(_context);

        public IOwnerRepository Owners => _owners ??= new OwnerRepository(_context);

        public ITenantRepository Tenants => _tenants ??= new TenantRepository(_context);
        public ILeadRepository Leads => _leads ??= new LeadRepository(_context);
        public IFavoritePropertyRepository FavoriteProperties => _favoriteProperties ??= new FavoritePropertyRepository(_context);
        public ILandlordRepository Landlords => _landlords ??= new LandlordRepository(_context);
        public ISearchRepository Search => _search ??= new SearchRepository(_context);
        public IAppointmentRepository Appointments => _appointments ??= new AppointmentRepository(_context);
        public IChatMessageRepository ChatMessages => _chatMessages ??= new ChatMessageRepository(_context);
        public IConversationRepository Conversations => _conversations ??= new ConversationRepository(_context);
        public ICustomerProfileRepository CustomerProfiles => _customerProfiles ??= new CustomerProfileRepository(_context);
        public IZaloTokenRepository ZaloTokens => _zaloTokens ??= new ZaloTokenRepository(_context);
        public IAttachmentRepository Attachments => _attachments ??= new AttachmentRepository(_context);
        public IContractRepository Contracts => _contracts ??= new ContractRepository(_context);
        public ISwipeRepository Swipes => _swipes ??= new SwipeRepository(_context);
        public IMatchingRepository Matchings => _matching ??= new MatchingRepository(_context);
        public IPriceDetailRepository PriceDetails => _priceDetails ??= new PriceDetailRepository(_context);

        public IPropertyRepository Properties => _property ??= new PropertyRepository(_context);

        public IPropertyDetailRepository PropertyDetails => _propertyDetails ??= new PropertyDetailRepository(_context);

        public ITransactionHistoryRepository TransactionHistories => _transactionHistory ??= new TransactionHistoryRepository(_context);
        public IDealRepository Deals => _deals ??= new DealRepository(_context);

        public IDealsActivityLogRepository DealsActivityLogs => _dealsActivityLogs ??= new DealsActivityLogRepository(_context);

        public IGenericRepository<User> Users => _users ??= new GenericRepository<User>(_context, "Users");
        public IGenericRepository<RefreshToken> RefreshTokens => _refreshTokens ??= new GenericRepository<RefreshToken>(_context, "RefreshTokens");
        public IChatSessionRepository ChatSessions => _chatSessions ??= new ChatSessionRepository(_context);

        public IGenericRepository<AnonymousSession> AnonymousSessions => _anonymousSessions ??= new GenericRepository<AnonymousSession>(_context, "AnonymousSessions");
        public IRentalContractReminderRepository RentalContractReminder => _rentalContractReminder ??= new RentalContractReminderRepository(_context);
        public IRentalContractNotificationRepository RentalContractNotification => _rentalContractNotification ??= new RentalContractNotificationRepository(_context);
        public ISurveyRepository Surveys => _surveys ??= new SurveyRepository(_context);

        public IPropertyViewHistoryRepository PropertyViewHistories => _propertyViewHistories ??= new PropertyViewHistoryRepository(_context);
        public IDraftPropertyRepository DraftProperties => _draftProperties ??= new DraftPropertyRepository(_context);
        public ITeamRepository Teams => _teams ??= new TeamRepository(_context);

		public IGenericRepository<Company> Companies => _companies ??= new GenericRepository<Company>(_context, "Companies");
        public IAgentRepository Agents => _agents ??= new AgentRepository(_context);
        public IAgentInviteRepository AgentInvites => _agentInvites ??= new AgentInviteRepository(_context);
        public IAgentCompanyLinkRepository AgentCompanyLinks => _agentCompanyLinks ??= new AgentCompanyLinkRepository(_context);
        public IAgentActivityLogRepository AgentActivityLogs => _agentActivityLogs ??= new AgentActivityLogRepository(_context);

		public IBuyerRepository Buyers => _buyers ??= new BuyerRepository(_context);
        
        public IRoommatePreferenceRepository RoommatePreferences => _roommatePreferences ??= new RoommatePreferenceRepository(_context);
        public ICollectionPropertyRepository CollectionProperties => _collectionProperties ??= new CollectionPropertyRepository(_context);

        public async Task SaveAsync()
        {
            await Task.CompletedTask;
        }

    }
}