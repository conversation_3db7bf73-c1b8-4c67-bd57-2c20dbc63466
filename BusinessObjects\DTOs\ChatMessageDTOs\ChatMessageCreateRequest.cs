﻿namespace BusinessObjects.DTOs.ChatMessageDTOs
{
    public class ChatMessageCreateRequest
    {
        public string? senderId { get; set; }
        public string recipientId { get; set; }
        public string? platformUserId { get; set; }
        public string? direction { get; set; }
        public string? content { get; set; }

        public string? messageType { get; set; } = "text";

        public List<string>? attachmentIds { get; set; }
        public string? conversationId { get; set; }
        public string? replyToMessageId { get; set; }
    }


}
