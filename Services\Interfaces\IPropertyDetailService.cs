﻿using BusinessObjects.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Services.Interface
{
    public interface IPropertyDetailService
    {
        Task<IEnumerable<PropertyDetail>> GetAllPropertyDetailsAsync();
        Task<PropertyDetail> GetPropertyDetailByIdAsync(string id);
        Task<PropertyDetail> AddPropertyDetailAsync(PropertyDetail propertyDetail);
        Task<PropertyDetail> UpdatePropertyDetailAsync(string id, PropertyDetail propertyDetail);
        Task<bool> DeletePropertyDetailAsync(string id);
    }
}
