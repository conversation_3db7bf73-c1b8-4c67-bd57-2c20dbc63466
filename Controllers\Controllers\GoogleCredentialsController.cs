﻿using BusinessObjects.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Services.Interfaces;
using static BusinessObjects.DTOs.GoogleDTOs.GoogleCredentialsDTOs;

namespace Controllers.Controllers
{
    [Route("api/googleCredentials")]
    [ApiController]
    [Authorize]
    public class GoogleCredentialsController : ControllerBase
    {
        private readonly IGoogleCredentialService _googleCredentialService;
        private readonly ILogger<GoogleCredentialsController> _logger;
        private readonly ICurrentUserService _currentUserService;

        public GoogleCredentialsController(
            IGoogleCredentialService googleCredentialService,
            ICurrentUserService currentUserService,
            ILogger<GoogleCredentialsController> logger)
        {
            _googleCredentialService = googleCredentialService;
            _logger = logger;
            _currentUserService = currentUserService;

        }

        private ApiResponse CreateResponse(int code, bool status, string message, object data = null)
        {
            return new ApiResponse { code = code, status = status, message = message, data = data };
        }

        [HttpPost("save")]
        public async Task<IActionResult> SaveCredentials([FromBody] SaveGoogleCredentialsRequest request)
        {
            try
            {
                var userId = _currentUserService.GetUserId();
                if (string.IsNullOrEmpty(userId))
                    return Unauthorized("User ID not found");

                var userAgent = HttpContext.Request.Headers["User-Agent"].ToString();
                var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString();

                var result = await _googleCredentialService.SaveOrUpdateCredentialsAsync(userId, request, userAgent, ipAddress);

                return Ok(CreateResponse(200, true, "Lưu thông tin đăng nhập Google thành công", result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving Google credentials");
                return StatusCode(500, CreateResponse(500, false, "Lỗi khi lưu thông tin đăng nhập Google", ex.Message));
            }
        }

        [HttpGet("my-credentials")]
        public async Task<IActionResult> GetMyCredentials()
        {
            try
            {
                var userId = _currentUserService.GetUserId();
                if (string.IsNullOrEmpty(userId))
                    return Unauthorized("User ID not found");

                var credentials = await _googleCredentialService.GetCredentialsByUserIdAsync(userId);

                if (credentials == null)
                {
                    return NotFound(CreateResponse(404, false, "Không tìm thấy thông tin đăng nhập Google", null));
                }

                return Ok(CreateResponse(200, true, "Lấy thông tin đăng nhập Google thành công", credentials));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving Google credentials");
                return StatusCode(500, CreateResponse(500, false, "Lỗi khi lấy thông tin đăng nhập Google", ex.Message));
            }
        }

        [HttpPut("update")]
        public async Task<IActionResult> UpdateCredentials([FromBody] UpdateGoogleCredentialsRequest request)
        {
            try
            {
                var userId = _currentUserService.GetUserId();
                if (string.IsNullOrEmpty(userId))
                    return Unauthorized("User ID not found");

                var result = await _googleCredentialService.UpdateCredentialsAsync(userId, request);

                if (!result)
                {
                    return NotFound(CreateResponse(404, false, "Không tìm thấy thông tin đăng nhập Google để cập nhật", null));
                }

                return Ok(CreateResponse(200, true, "Cập nhật thông tin đăng nhập Google thành công", null));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating Google credentials");
                return StatusCode(500, CreateResponse(500, false, "Lỗi khi cập nhật thông tin đăng nhập Google", ex.Message));
            }
        }

        [HttpDelete("deactivate")]
        public async Task<IActionResult> DeactivateCredentials()
        {
            try
            {
                var userId = _currentUserService.GetUserId();
                if (string.IsNullOrEmpty(userId))
                    return Unauthorized("User ID not found");

                var result = await _googleCredentialService.DeactivateCredentialsAsync(userId);

                return Ok(CreateResponse(200, true, result ? "Vô hiệu hóa thông tin đăng nhập Google thành công" : "Không tìm thấy thông tin đăng nhập để vô hiệu hóa", null));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deactivating Google credentials");
                return StatusCode(500, CreateResponse(500, false, "Lỗi khi vô hiệu hóa thông tin đăng nhập Google", ex.Message));
            }
        }

        [HttpGet("token-status")]
        public async Task<IActionResult> CheckTokenStatus()
        {
            try
            {
                var userId = _currentUserService.GetUserId();
                if (string.IsNullOrEmpty(userId))
                    return Unauthorized("User ID not found");

                var isValid = await _googleCredentialService.IsTokenValidAsync(userId);

                return Ok(CreateResponse(200, true, "Kiểm tra trạng thái token thành công", new { isValid, status = isValid ? "Hợp lệ" : "Hết hạn hoặc không hợp lệ" }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking token status");
                return StatusCode(500, CreateResponse(500, false, "Lỗi khi kiểm tra trạng thái token", ex.Message));
            }
        }
    }
}
