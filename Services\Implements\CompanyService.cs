﻿using BusinessObjects.DTOs.CompanyDTOs;
using BusinessObjects.Models;
using BusinessObjects.Settings;
using BusinessObjects.Validations;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using MongoDB.Driver;
using Repositories.Implements;
using Repositories.Interfaces;
using Services.Interfaces;
using Services.Tools;
using System.Security.Claims;
using System.Text.RegularExpressions;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory.Database;
using static System.Net.WebRequestMethods;

namespace Services.Implements
{
    public class CompanyService : ICompanyService
    {
        private readonly ICompanyRepository _companyRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IRedisService _redisService;
        private readonly IEmailService _emailService;
        private readonly IZaloApiService _zaloApiService;
        private readonly CompanyPasswordHasher _companyPasswordHasher;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly FirebaseService _firebaseService;
        private readonly TokenTools _token;
        private readonly IAgentInviteRepository _agentInviteRepository;
        private readonly IAgentRepository _agentRepository;
        private readonly IAgentCompanyLinkRepository _agentCompanyLinkRepository;

		public CompanyService(ICompanyRepository companyRepository, IUnitOfWork unitOfWork, IRedisService redisService, IEmailService emailService, IZaloApiService zaloApiService, CompanyPasswordHasher companyPasswordHasher, TokenTools token, IHttpContextAccessor httpContextAccessor, FirebaseService firebaseService, IAgentInviteRepository agentInviteRepository, IAgentRepository agentRepository, IAgentCompanyLinkRepository agentCompanyLinkRepository)
        {
            _companyRepository = companyRepository;
            _unitOfWork = unitOfWork;
            _redisService = redisService;
            _emailService = emailService;
            _zaloApiService = zaloApiService;
            _companyPasswordHasher = companyPasswordHasher;
            _token = token;
            _httpContextAccessor = httpContextAccessor;
            _firebaseService = firebaseService;
			_agentInviteRepository = agentInviteRepository;
			_agentRepository = agentRepository;
			_agentCompanyLinkRepository = agentCompanyLinkRepository;
		}

        public async Task<TokenSetting> LoginAsync(CompanyLogin request)
        {
            string emailPattern = @"^[^@\s]+@[^@\s]+\.[^@\s]+$";
            string phonePattern = @"^\+?\d{10,15}$";
            if (string.IsNullOrEmpty(request.keyLogin))
            {
                throw new Exception("Tài khoản không đúng định dạng email hoặc số điện thoại.");
            }

            Company company = new Company();
            if (Regex.IsMatch(request.keyLogin, emailPattern))
            {
                company = await this.FindCompanyByEmailAsync(request.keyLogin);
            }

            if (Regex.IsMatch(request.keyLogin, phonePattern))
            {
                company = await this.FindCompanyByPhonenumberAsync(request.keyLogin);
            }
            if (company == null)
            {
                throw new Exception("Không tìm thấy công ty nào hợp lệ");
            }

            var companyConvert = new CompanyDTO
            {
                id = company.id,
                name = company.name,
                email = company.email ?? string.Empty,
                phone = company.phone ?? string.Empty,
                logoUrl = company.logoUrl,
                address = company.address,
                industry = company.industry,
                taxId = company.taxId,
                licenseNumber = company.licenseNumber,
                website = company.website,
                password = company.password,
                ownerId = company.ownerId
            };

            if (company != null)
            {
                var verifyPassword = _companyPasswordHasher.VerifyHashedPassword(companyConvert, company.password, request.password);
                if (verifyPassword == PasswordVerificationResult.Failed)
                {
                    throw new Exception("Sai tài khoản hoặc mật khẩu");
                }
            }
            else
            {
                throw new Exception("Sai email hoặc số điện thoại");
            }

            if (company.isVerified == false)
            {
                if (Regex.IsMatch(request.keyLogin, emailPattern))
                {
                    await SendCompanyEmailOTP(company.email, company.name);
                    throw new Exception("Tài khoản chưa được xác thực. Vui lòng kiểm tra email và nhập OTP để xác thực tài khoản của bạn.");
                }
                else if (Regex.IsMatch(request.keyLogin, phonePattern))
                {
                    await SendCompanyPhoneOTP(company.phone, company.name);
                    throw new Exception("Tài khoản chưa được xác thực. Vui lòng kiểm tra tin nhắn Zalo hoặc Sms và nhập OTP để xác thực tài khoản của bạn.");
                }
            }
            if (company.isActive == false)
            {
                throw new Exception("Tài khoản của công ty này đã bị dừng hoạt động.");
            }

            var token = await _token.GenerateCompanyToken(companyConvert);
            return token;
        }

        public async Task CompanyRegisterAsync(CompanyRegister model, string ownerId, IFormFile logoUrl)
        {
            // Determine if keyregister is email or phone
            bool isEmail = ValidationHelper.IsEmail(model.keyRegister);
            bool isPhone = ValidationHelper.IsPhoneNumber(model.keyRegister) && ValidationHelper.IsVietnamesePhone(model.keyRegister);

            if (!isEmail && !isPhone)
            {
                throw new Exception("Tài khoản không đúng định dạng email hoặc số điện thoại");
            }

            string? email = null;
            string? phoneNumber = null;

            if (isEmail)
            {
                email = model.keyRegister;
                var existingEmailCompany = await this.FindCompanyByEmailAsync(email);

                if (existingEmailCompany != null)
                {
                    if (!existingEmailCompany.isVerified)
                    {
                        await SendCompanyEmailOTP(email, model.name);
                        return;
                    }
                    throw new Exception("Email này đã được đăng kí");
                }

                var existingCompany = await this.FindCompanyByNameAsync(model.name);
                if (existingCompany != null)
                {
                    throw new Exception("CompanyName này đã tồn tại");
                }
                await SendCompanyEmailOTP(email, model.name);
            }
            else if (isPhone)
            {
                // Lưu phone với format gốc (ví dụ: 0937634111)
                phoneNumber = ValidationHelper.CleanPhoneForDB(model.keyRegister);

                // Check phone existence
                var existingPhoneCompany = await this.FindCompanyByPhonenumberAsync(phoneNumber);
                if (existingPhoneCompany != null)
                {
                    if (!existingPhoneCompany.isVerified)
                    {
                        await SendCompanyPhoneOTP(phoneNumber, model.name);
                        return;
                    }
                    throw new Exception("Số điện thoại này đã được đăng kí");
                }
                // Send OTP via Zalo với format 84937634111
                await SendCompanyPhoneOTP(phoneNumber, model.name);
            }
            else
            {
                throw new Exception("Invalid email or phone number format");
            }

            var hashedPassword = _companyPasswordHasher.HashPassword(new CompanyDTO(), model.password);
            var stringLogoUrl = "";

            if (logoUrl != null && logoUrl.Length != 0)
            {
                using var stream = logoUrl.OpenReadStream();
                stringLogoUrl = await _firebaseService.UploadFileAsync(stream, logoUrl.FileName);
            }

            // Handle avatar upload
            var newCompany = new Company()
            {
                name = model.name,
                email = email,
                password = hashedPassword,
                phone = phoneNumber,
                logoUrl = stringLogoUrl,
                address = model.address,
                industry = model.industry,
                taxId = model.taxId,
                licenseNumber = model.licenseNumber,
                website = model.website,
                isVerified = false, // Chưa xác thực
                ownerId = ownerId // Sử dụng ownerId từ token
            };
            await this.SaveCompanyAsync(newCompany);
        }

        private async Task SendCompanyEmailOTP(string email, string name)
        {
            var random = new Random();
            var otpCode = random.Next(100000, 999999).ToString();
            var redisKey = $"OTP:{email}"; // Redis key: OTP:<EMAIL>

            string? cachedOtp = await _redisService.GetStringAsync(redisKey);
            if (cachedOtp != null)
            {
                throw new Exception("Mã OTP đã được gửi. Vui lòng kiểm tra email.");
            }

            await _redisService.SetStringAsync(redisKey, otpCode, TimeSpan.FromMinutes(2));
            var emailContent = EmailTemplate.GetOtpEmailContent(name, otpCode, 2);
            _emailService.SendEmailAsync(email, "QuindLand - OTP Verification", emailContent);
        }

		public async Task InviteAgentAsync(string email, string companyId, int validHours, AgentCompanyRole role)
		{
			//tạo token ngẫu nhiên cho lời mời
			var token = Guid.NewGuid().ToString();

			var existingInvite = await _agentInviteRepository.GetByEmailAndCompanyAsync(email, companyId);
			if (existingInvite == null)
			{
				await _agentInviteRepository.CreateNewAsync(email, companyId, token, role, validHours);
			}
			else
			{
				await _agentInviteRepository.UpdateInviteTokenAsync(email, companyId, token, validHours);
			}

			var company = await this.FindCompanyByIdAsync(companyId);
			if (company == null)
			{
				throw new Exception("Công ty không tồn tại hoặc không hợp lệ.");
			}

			var request = _httpContextAccessor.HttpContext?.Request;
			var scheme = request?.Scheme;
			var host = request?.Host.Host;
			var port = request?.Host.Port;
			var domain = port != null ? $"{scheme}://{host}:{port}" : $"{scheme}://{host}";

			var inviteLink = $"{domain}/api/company/accept-invite?token={token}";

			var emailContent = EmailTemplate.GetInviteAgentEmailContent(email, inviteLink, company.name, validHours);
			await _emailService.SendEmailAsync(email, $"[QuindLand] {company.name} mời bạn tham gia", emailContent);
		}

		public async Task ReInviteAgentAsync(string email, string companyId)
		{
			//tạo token ngẫu nhiên cho lời mời
			var token = Guid.NewGuid().ToString();

			var existingInvite = await _agentInviteRepository.GetByEmailAndCompanyAsync(email, companyId);
			if (existingInvite != null)
			{
				await _agentInviteRepository.UpdateInviteTokenAsync(email, companyId, token, 72);
			}
			else
			{
				throw new Exception("Không tìm thấy lời mời nào trước đây");
			}

			var company = await this.FindCompanyByIdAsync(companyId);
			if (company == null)
			{
				throw new Exception("Công ty không tồn tại hoặc không hợp lệ.");
			}

			var request = _httpContextAccessor.HttpContext?.Request;
			var scheme = request?.Scheme;
			var host = request?.Host.Host;
			var port = request?.Host.Port;
			var domain = port != null ? $"{scheme}://{host}:{port}" : $"{scheme}://{host}";

			var inviteLink = $"{domain}/api/company/accept-invite?token={token}";

			var emailContent = EmailTemplate.GetInviteAgentEmailContent(email, inviteLink, company.name, 72);
			await _emailService.SendEmailAsync(email, $"[QuindLand] {company.name} mời bạn tham gia", emailContent);
		}

		public async Task AcceptInviteAsync(string token)
		{
			var invite = await _agentInviteRepository.GetByTokenAsync(token);

			if (invite == null || invite.isUsed || invite.expiredAt < DateTime.UtcNow)
				throw new Exception("Token không hợp lệ hoặc đã hết hạn");

			var agent = await _agentRepository.GetAgentByEmailAsync(invite.email);
			if (agent == null)
			{
				agent = new Agent
				{
					email = invite.email
				};
				agent = await _unitOfWork.Agents.AddAsync(agent);
			}

			var alreadyLinked = await _agentCompanyLinkRepository.ExistsAsync(agent.id, invite.companyId);
			if (alreadyLinked == null)
			{
				var agentCompanyLink = new AgentCompanyLink
                {
                    agentId = agent.id,
                    companyId = invite.companyId,
                    role = invite.role,
                    token = token,
					acceptAt = DateTime.UtcNow.AddHours(7),
				};
                await _unitOfWork.AgentCompanyLinks.AddAsync(agentCompanyLink);
			}

			await _agentInviteRepository.MarkUsedAsync(invite.id);
            invite.acceptAt = DateTime.UtcNow.AddHours(7);
            invite.isUsed = true;
			await _unitOfWork.AgentInvites.UpdateAsync(invite.id, invite);
		}

		private async Task SendCompanyPhoneOTP(string phoneNumber, string name)
        {
            // Format phone để gửi OTP: 0937634111 -> 84937634111
            string formattedPhone = ValidationHelper.FormatPhoneForOTP(phoneNumber);

            var random = new Random();
            var otpCode = random.Next(100000, 999999).ToString();
            var redisKey = $"OTP:{formattedPhone}"; // Redis key: OTP:84937634111

            string? cachedOtp = await _redisService.GetStringAsync(redisKey);
            if (cachedOtp != null)
            {
                throw new Exception("OTP already sent. Please wait for a while before requesting again.");
            }

            await _redisService.SetStringAsync(redisKey, otpCode, TimeSpan.FromMinutes(5));
            _zaloApiService.SendOTPAsync(formattedPhone); // Gửi với format 84937634111
        }

        public async Task VerifyCompanyOTPAsync(string otp, string verifyKey)
        {
            string phonePattern = @"^\+?\d{10,15}$";
            string type = "email";
            string redisKey;
            if (Regex.IsMatch(verifyKey, phonePattern))
            {
                type = "phone";
                string formattedPhone = "84" + verifyKey.Substring(1);
                redisKey = $"OTP:{formattedPhone}";
            }
            else
            {
                redisKey = $"OTP:{verifyKey}";
                if (verifyKey.Length < 6 || !verifyKey.Contains("@"))
                {
                    throw new Exception("Email không đúng định dạng.");
                }
            }

            string? cachedOtp = await _redisService.GetStringAsync(redisKey);

            if (cachedOtp == null)
            {
                throw new Exception("Mã OTP này đã hết hạn hoặc không tồn tại, vui lòng gửi yêu cầu cấp lại mã OTP mới.");
            }

            if (cachedOtp != otp)
            {
                throw new Exception("Mã OTP không hợp lệ.");
            }

            await _redisService.DeleteKeyAsync(redisKey);
            Company company;
            if (type == "email")
            {
                company = await this.FindCompanyByEmailAsync(verifyKey);
            }
            else
            {
                company = await this.FindCompanyByPhonenumberAsync(verifyKey);
            }

            if (company == null)
            {
                throw new Exception("Tài khoản này không tồn tại");
            }

            company.isVerified = true;
            await _unitOfWork.Companies.UpdateAsync(company.id, company);
        }

        public async Task RequestCompanyOTPAsync(string verifyKey)
        {
            string phonePattern = @"^\+?\d{10,15}$";
            string emailPattern = @"^[^@\s]+@[^@\s]+\.[^@\s]+$";
            if (Regex.IsMatch(verifyKey, phonePattern))
            {
                string formattedPhone = "84" + verifyKey.Substring(1);
                var random = new Random();
                var otpCode = random.Next(100000, 999999).ToString();
                var redisKey = $"OTP:{formattedPhone}";
                string? cachedOtp = await _redisService.GetStringAsync(redisKey);
                if (cachedOtp != null)
                {
                    throw new Exception("OTP already sent. Please wait for a while before requesting again.");
                }

                var company = await this.FindCompanyByPhonenumberAsync(verifyKey);

                if (company == null)
                {
                    throw new Exception("Company not found");
                }
                await _redisService.SetStringAsync(redisKey, otpCode, TimeSpan.FromMinutes(1));
                _zaloApiService.SendOTPAsync(formattedPhone);
            }
            else if (Regex.IsMatch(verifyKey, emailPattern))
            {
                var company = await this.FindCompanyByEmailAsync(verifyKey) ?? throw new Exception("Không tìm thấy công ty nào hợp lệ");
                if (company.isVerified)
                {
                    throw new Exception("Email đã được xác thực. Vui lòng đăng nhập.");
                }
                await SendCompanyEmailOTP(verifyKey, company.name);
            }
            else
            {
                throw new Exception("Tài khoản không đúng định dạng email hoặc số điện thoại");
            }
        }

        public async Task<Company> FindCompanyByEmailAsync(string email)
        {
            return await _companyRepository.GetCompanyByEmailAsync(email);
        }

        public async Task<Company> FindCompanyByIdAsync(string id)
        {
            return await _companyRepository.GetByIdAsync(id);
        }

        public async Task<List<Company>> FindCompanyByIdsAsync(List<string> id)
        {
            return await _companyRepository.GetByIdsAsync(id);
        }

        public async Task<Company> FindCompanyByPhonenumberAsync(string phonenumber)
        {
            return await _companyRepository.GetCompanyByPhonenumberAsync(phonenumber);
        }

        public async Task<Company> FindCompanyByNameAsync(string name)
        {
            return await _companyRepository.GetCompanyByNameAsync(name);
        }

        public Task SaveCompanyAsync(Company company)
        {
            return _unitOfWork.Companies.AddAsync(company);
        }

        public async Task<Company> UpdateCompanyAsync(string id, CompanyUpdateRequest request, IFormFile logoUrl)
        {
            var foundCompany = await _unitOfWork.Companies.GetByIdAsync(id);
            if (foundCompany == null)
            {
                throw new Exception("Company not found");
            }

            var stringLogoUrl = "";

            if (logoUrl != null && logoUrl.Length != 0)
            {
                using var stream = logoUrl.OpenReadStream();
                stringLogoUrl = await _firebaseService.UploadFileAsync(stream, logoUrl.FileName);
            }

            var hashedPassword = _companyPasswordHasher.HashPassword(new CompanyDTO(), request.password);            

            foundCompany.name = request.name ?? foundCompany.name;
            foundCompany.email = request.email ?? foundCompany.email;
            foundCompany.password = hashedPassword ?? foundCompany.password;
            foundCompany.phone = request.phone ?? foundCompany.phone;
            foundCompany.logoUrl = stringLogoUrl ?? foundCompany.logoUrl;
            foundCompany.address = request.address ?? foundCompany.address;
            foundCompany.industry = request.industry ?? foundCompany.industry;
            foundCompany.taxId = request.taxId ?? foundCompany.taxId;
            foundCompany.licenseNumber = request.licenseNumber ?? foundCompany.licenseNumber;
            foundCompany.website = request.website ?? foundCompany.website;
            
            await _unitOfWork.Companies.UpdateAsync(id, foundCompany);
            return foundCompany;
        }

        public async Task UpdateCompanyStatusAsync(string id, bool status)
        {
            var foundCompany = await _unitOfWork.Companies.GetByIdAsync(id);
            if (foundCompany == null)
            {
                throw new Exception("Company not found");
            }

            foundCompany.isActive = status;

            await _unitOfWork.Companies.UpdateAsync(id, foundCompany);
        }

		public async Task<IEnumerable<Company>> GetAllCompanyAsync()
		{
			return await _unitOfWork.Companies.GetAllAsync();
		}
	}
}
