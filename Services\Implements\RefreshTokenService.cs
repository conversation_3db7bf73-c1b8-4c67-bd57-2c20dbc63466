﻿using BusinessObjects.Models;
using Repositories.Interfaces;
using Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Services.Implements
{
    public class RefreshTokenService : IRefreshTokenService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IRefreshTokenRepository _repository;
        public RefreshTokenService(IUnitOfWork unitOfWork, IRefreshTokenRepository repository)
        {
            _unitOfWork = unitOfWork;
            _repository = repository;
        }
        public async Task<RefreshToken> GetRefreshToken(string refreshtoken)
        {
            return await _repository.GetRefreshToken(refreshtoken);
        }

        public async Task Update(string id,RefreshToken refreshToken)
        {
            await _unitOfWork.RefreshTokens.UpdateAsync(id,refreshToken);
        }
    }
}
