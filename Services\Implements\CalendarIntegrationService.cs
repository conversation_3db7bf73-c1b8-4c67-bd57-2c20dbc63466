﻿using BusinessObjects.DTOs.AppoinmentDTOs;
using BusinessObjects.Models;
using Microsoft.Extensions.Logging;
using Repositories.Interfaces;
using Services.Interfaces;
using static BusinessObjects.DTOs.CalendarDTOs.GoogleCalendar;
using static BusinessObjects.DTOs.CalendarDTOs.GoogleCalendarIntegration;

namespace Services.Implements
{
    public class CalendarIntegrationService : ICalendarIntegrationService
    {
        private readonly ICustomCalendarService _calendarService;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IGoogleCredentialService _googleCredentialService;
        private readonly ILogger<CalendarIntegrationService> _logger;

        public CalendarIntegrationService(
            ICustomCalendarService calendarService,
            IUnitOfWork unitOfWork,
            IGoogleCredentialService googleCredentialService,
            ILogger<CalendarIntegrationService> logger)
        {
            _calendarService = calendarService;
            _unitOfWork = unitOfWork;
            _googleCredentialService = googleCredentialService;
            _logger = logger;
        }
        
        public async Task<List<CalendarInfo>> GetUserCalendarsAsync(string userId)
        {
            try
            {
                var credentials = await _googleCredentialService.GetValidCredentialsAsync(userId);
                if (credentials?.accessToken != null)
                    return await _calendarService.GetCalendarsAsync(credentials.accessToken);
                _logger.LogWarning($"No access token found for user {userId}");
                return new List<CalendarInfo>();

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to get calendars for user {userId}");
                return new List<CalendarInfo>();
            }
        }
        
        public async Task<List<EventInfo>> GetUserEventsAsync(string userId, GetUserEventsRequest request)
        {
            try
            {
                var credentials = await _googleCredentialService.GetValidCredentialsAsync(userId);
                if (credentials?.accessToken == null)
                {
                    _logger.LogWarning($"No access token found for user {userId}");
                    return new List<EventInfo>();
                }

                var eventsRequest = new GetEventsRequest
                {
                    accessToken = credentials.accessToken,
                    calendarId = request.calendarId,
                    timeMin = request.timeMin,
                    timeMax = request.timeMax,
                    maxResults = request.maxResults,
                    orderBy = request.orderBy,
                    showDeleted = request.showDeleted,
                    singleEvents = request.singleEvents
                };

                return await _calendarService.GetEventsAsync(eventsRequest);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to get events for user {userId}");
                return new List<EventInfo>();
            }
        }

        public async Task<string?> CreateCalendarEventForAppointmentAsync(
                                                                          Appointment appointment,
                                                                          string? targetUserId = null)
        {
            try
            {
                var userId = targetUserId ?? appointment.salerId;
                if (string.IsNullOrEmpty(userId))
                {
                    _logger.LogWarning($"No valid user ID for appointment {appointment.id}");
                    return null;
                }

                _logger.LogInformation($"Creating calendar event for appointment: {appointment.id}, user: {userId}");

                // ✅ Single database call batch
                var appointmentData = await FetchAppointmentDataAsync(appointment);
                if (!ValidateAppointmentData(appointmentData, appointment.id))
                    return null;

                // ✅ Get credentials
                var credentials = await _googleCredentialService.GetValidCredentialsAsync(userId);
                if (credentials == null)
                {
                    _logger.LogWarning($"User {userId} doesn't have active Google credentials.");
                    return null;
                }

                // ✅ Build request with fetched data
                var calendarRequest = BuildCalendarRequest(appointmentData, appointment, credentials);

                // ✅ Create event with pre-fetched data
                return await CreateEventWithDataAsync(appointment, calendarRequest, appointmentData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error creating calendar event for appointment {appointment.id}");
                return null;
            }
        }


        public async Task<bool> UpdateCalendarEventForAppointmentAsync(
                                                                        Appointment appointment,
                                                                        string userId,
                                                                        string eventId)
        {
            try
            {
                if (string.IsNullOrEmpty(eventId) || string.IsNullOrEmpty(userId))
                {
                    _logger.LogWarning($"Invalid parameters for updating calendar event. EventId: {eventId}, UserId: {userId}");
                    return false;
                }

                _logger.LogInformation($"Updating calendar event {eventId} for appointment {appointment.id}, user: {userId}");

                // ✅ Get credentials
                var credentials = await _googleCredentialService.GetValidCredentialsAsync(userId);
                if (credentials == null)
                {
                    _logger.LogWarning($"User {userId} doesn't have active Google credentials for updating event {eventId}");
                    return false;
                }

                // ✅ Fetch appointment data
                var appointmentData = await FetchAppointmentDataAsync(appointment);
                if (!ValidateAppointmentData(appointmentData, appointment.id))
                    return false;
                var attendees = new List<string>();

                if (appointmentData.saler != null && !string.IsNullOrEmpty(appointmentData.saler.email))
                    attendees.Add(appointmentData.saler.email);

                if (appointmentData.lead != null && !string.IsNullOrEmpty(appointmentData.lead.email))
                    attendees.Add(appointmentData.lead.email);

                // ✅ Build update request using your existing UpdateEventRequest
                var updateRequest = new UpdateEventRequest
                {
                    accessToken = credentials.accessToken,
                    title = $"Property Viewing - {appointmentData.property.title}",
                    description = BuildEventDescription(appointmentData.lead, appointmentData.property, appointment),
                    location = appointmentData.property.location?.address ?? "TBD",
                    startTime = appointment.date,
                    endTime = appointment.date.AddHours(1),
                    timeZone = "Asia/Ho_Chi_Minh",
                    sendNotifications = true,
                    attendees = attendees
                };

                //Use existing UpdateEventAsync method
                var result = await _calendarService.UpdateEventAsync(
                    credentials.calendarId,
                    eventId,
                    updateRequest);

                if (result.success)
                {
                    _logger.LogInformation($"Calendar event {eventId} updated successfully for appointment {appointment.id}");
                    return true;
                }
                else
                {
                    _logger.LogError($"Failed to update calendar event {eventId}: {result.message}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating calendar event {eventId} for appointment {appointment.id}");
                return false;
            }
        }



        public async Task<bool> DeleteCalendarEventForAppointmentAsync(
                                                                        Appointment appointment,
                                                                        string userId,
                                                                        string eventId)
        {
            try
            {
                if (string.IsNullOrEmpty(eventId) || string.IsNullOrEmpty(userId))
                {
                    _logger.LogWarning($"Invalid parameters for deleting calendar event. EventId: {eventId}, UserId: {userId}");
                    return false;
                }

                _logger.LogInformation($"Deleting calendar event {eventId} for appointment {appointment.id}, user: {userId}");

                // ✅ Get credentials
                var credentials = await _googleCredentialService.GetValidCredentialsAsync(userId);
                if (credentials == null)
                {
                    _logger.LogWarning($"User {userId} doesn't have active Google credentials for deleting event {eventId}");
                    return false;
                }

                // ✅ Use existing DeleteEventAsync method
                var result = await _calendarService.DeleteEventAsync(
                    credentials.calendarId,
                    eventId,
                    credentials.accessToken);

                if (result.success)
                {
                    _logger.LogInformation($"Calendar event {eventId} deleted successfully for appointment {appointment.id}");
                    return true;
                }
                else
                {
                    _logger.LogError($"Failed to delete calendar event {eventId}: {result.message}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting calendar event {eventId} for appointment {appointment.id}");
                return false;
            }
        }

        // ✅ Overload để delete cả 2 events cùng lúc
        public async Task<(bool userDeleted, bool salerDeleted)> DeleteAllCalendarEventsForAppointmentAsync(
            Appointment appointment)
        {
            var userDeleted = false;
            var salerDeleted = false;

            var deleteTasks = new List<Task>();

            // ✅ Delete User's calendar event
            if (!string.IsNullOrEmpty(appointment.userGoogleCalendarEventId) &&
                !string.IsNullOrEmpty(appointment.userId))
            {
                deleteTasks.Add(Task.Run(async () =>
                {
                    userDeleted = await DeleteCalendarEventForAppointmentAsync(
                        appointment,
                        appointment.userId,
                        appointment.userGoogleCalendarEventId);
                }));
            }

            // ✅ Delete Saler's calendar event
            if (!string.IsNullOrEmpty(appointment.salerGoogleCalendarEventId) &&
                !string.IsNullOrEmpty(appointment.salerId))
            {
                deleteTasks.Add(Task.Run(async () =>
                {
                    salerDeleted = await DeleteCalendarEventForAppointmentAsync(
                        appointment,
                        appointment.salerId,
                        appointment.salerGoogleCalendarEventId);
                }));
            }

            // ✅ Execute all deletions in parallel
            if (deleteTasks.Any())
            {
                await Task.WhenAll(deleteTasks);
            }

            _logger.LogInformation($"Calendar events deletion completed for appointment {appointment.id}. User: {userDeleted}, Saler: {salerDeleted}");

            return (userDeleted, salerDeleted);
        }


        public async Task<CalendarEventSyncResult> SyncAppointmentToCalendarsAsync(Appointment appointment)
        {
            var result = new CalendarEventSyncResult();

            try
            {
                // ✅ Create user calendar event
                var userEventId = await CreateCalendarEventForAppointmentAsync(appointment, appointment.userId);
                if (!string.IsNullOrEmpty(userEventId))
                {
                    result.userEventCreated = true;
                    result.userEventId = userEventId;
                }
                else
                {
                    result.errors.Add("Failed to create user calendar event");
                }

                // ✅ Create saler calendar event (if salerId exists)
                if (!string.IsNullOrEmpty(appointment.salerId))
                {
                    var salerEventId = await CreateCalendarEventForAppointmentAsync(appointment, appointment.salerId);
                    if (!string.IsNullOrEmpty(salerEventId))
                    {
                        result.salerEventCreated = true;
                        result.salerEventId = salerEventId;
                    }
                    else
                    {
                        result.errors.Add("Failed to create saler calendar event");
                    }
                }

                // ✅ Determine sync status
                if (result.userEventCreated && result.salerEventCreated)
                    result.status = CalendarSyncStatus.FullySynced;
                else if (result.userEventCreated)
                    result.status = CalendarSyncStatus.UserSynced;
                else if (result.salerEventCreated)
                    result.status = CalendarSyncStatus.SalerSynced;
                else
                    result.status = CalendarSyncStatus.Failed;

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error syncing appointment {appointment.id} to calendars");
                result.status = CalendarSyncStatus.Failed;
                result.errors.Add(ex.Message);
            }

            return result;
        }





        private async Task<AppointmentData> FetchAppointmentDataAsync(Appointment appointment)
        {
            var tasks = new List<Task>
            {
                _unitOfWork.Leads.GetByIdAsync(appointment.leadId),
                _unitOfWork.Properties.GetByIdAsync(appointment.propertyId)
            };

            Task<User> salerTask = null;
            if (!string.IsNullOrEmpty(appointment.salerId))
            {
                //case : update user event after assign saler
                salerTask = _unitOfWork.Users.GetByIdAsync(appointment.salerId);
                tasks.Add(salerTask);
            }

            await Task.WhenAll(tasks);

            return new AppointmentData
            {
                lead = await (Task<Lead?>)tasks[0],
                property = await (Task<Property?>)tasks[1],
                saler = salerTask != null ? await salerTask : null
            };
        }

        private bool ValidateAppointmentData(AppointmentData data, string appointmentId)
        {
            if (data.lead == null || data.property == null)
            {
                _logger.LogWarning($"Missing required data for appointment {appointmentId}");
                return false;
            }

            //if (isRequireSaler && data.saler == null)
            //{
            //    _logger.LogWarning($"Saler required but not found for appointment {appointmentId}");
            //    return false;
            //}

            return true;
        }

        private CreateEventRequestForAppointment BuildCalendarRequest(
            AppointmentData data,
            Appointment appointment,
            GoogleCredentialModel credentials)
        {
            return new CreateEventRequestForAppointment
            {
                accessToken = credentials.accessToken,
                calendarId = credentials.calendarId,
                title = $"Property Viewing - {data.property.title}",
                description = BuildEventDescription(data.lead, data.property, appointment),
                location = data.property.location?.address ?? "TBD",
                startTime = appointment.date,
                endTime = appointment.date.AddHours(1),
                timeZone = "Asia/Ho_Chi_Minh",
                isAllDay = false,
                colorId = 9,
                sendNotifications = true,
                reminders = new List<EventReminder>
          {
              new EventReminder { method = "email", minutes = 60 },
              new EventReminder { method = "popup", minutes = 15 }
          }
            };
        }

        // ✅ Optimized CreateEvent - không gọi database nữa
        private async Task<string?> CreateEventWithDataAsync(
            Appointment appointment,
            CreateEventRequestForAppointment request,
            AppointmentData data)
        {
            try
            {
                // ✅ Build attendees từ data đã fetch
                var attendees = new List<string>();

                if (data.saler != null && !string.IsNullOrEmpty(data.saler.email))
                    attendees.Add(data.saler.email);

                if (data.lead != null && !string.IsNullOrEmpty(data.lead.email))
                    attendees.Add(data.lead.email);

                var createEventRequest = new CreateEventRequest
                {
                    accessToken = request.accessToken,
                    title = request.title,
                    calendarId = request.calendarId,
                    colorId = request.colorId,
                    description = request.description,
                    location = request.location,
                    reminders = request.reminders,
                    sendNotifications = request.sendNotifications,
                    startTime = request.startTime,
                    endTime = request.endTime,
                    isAllDay = request.isAllDay,
                    attendees = attendees,
                    timeZone = request.timeZone
                };

                var result = await _calendarService.CreateEventAsync(createEventRequest);

                if (result.success)
                {
                    _logger.LogInformation($"Calendar event created successfully: {result.eventId}");
                    return result.eventId;
                }
                else
                {
                    _logger.LogError($"Failed to create calendar event: {result.message}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in CreateEventWithDataAsync for appointment {appointment.id}");
                return null;
            }
        }

        private string BuildEventDescription(Lead? lead, Property? property, Appointment appointment)
        {
            var description = "Property Viewing Appointment\n\n";

            if (lead != null)
            {
                description += $"Client Information:\n";
                description += $"• Name: {lead.name}\n";
                description += $"• Phone: {lead.phone}\n";
                description += $"• Email: {lead.email}\n\n";
            }

            if (property != null)
            {
                description += $"Property Details:\n";
                description += $"• Title: {property.title}\n";
                description += $"• Type: {property.type}\n";

                // ✅ Kiểm tra null cho priceDetails
                if (property.priceDetails != null)
                {
                    description += $"• Deposit Amount: {property.priceDetails.depositAmount:C}\n";
                    description += $"• Payment Methods: {string.Join(", ", property.priceDetails.paymentMethods ?? new List<string>())}\n";
                    description += $"• Price Per SquareMeter: {property.priceDetails.pricePerSquareMeter:C}\n";
                }

                description += $"• Address: {property.location?.address}\n\n";
            }

            description += $"Appointment Details:\n";
            description += $"• Date: {appointment.date:yyyy-MM-dd HH:mm}\n";
            description += $"• Status: {appointment.status}\n";
            description += $"• Created: {appointment.createdAt:yyyy-MM-dd HH:mm}";

            return description;
        }

        private List<string> BuildUpdatedAttendeesList(List<string> existingAttendees, string sellerEmail, string userEmail)
        {
            var attendees = new List<string>();

            // ✅ Add existing attendees (preserve original list)
            if (existingAttendees != null)
            {
                attendees.AddRange(existingAttendees.Where(email => !string.IsNullOrEmpty(email)));
            }

            // ✅ Ensure user is in the list (should already be there)
            if (!attendees.Any(email => email.Equals(userEmail, StringComparison.OrdinalIgnoreCase)))
            {
                attendees.Add(userEmail);
                _logger.LogInformation($"📧 Added user {userEmail} to attendees list");
            }

            // ✅ Add seller if not already present
            if (!attendees.Any(email => email.Equals(sellerEmail, StringComparison.OrdinalIgnoreCase)))
            {
                attendees.Add(sellerEmail);
                _logger.LogInformation($"Added seller {sellerEmail} to attendees list");
            }
            else
            {
                _logger.LogInformation($"Seller {sellerEmail} already in attendees list");
            }

            return attendees.Distinct(StringComparer.OrdinalIgnoreCase).ToList();
        }
    }
}
