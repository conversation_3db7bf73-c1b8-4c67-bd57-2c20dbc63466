﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using Microsoft.EntityFrameworkCore;
using MongoDB.Bson;
using MongoDB.Driver;
using Repositories.Interfaces;

namespace Repositories.Implements
{
    public class LeadRepository : GenericRepository<Lead>, ILeadRepository
    {
        private readonly IMongoCollection<Lead> _leads;

        public LeadRepository(RevoLandDbContext context) : base(context, "Leads")
        {
            _leads = context.GetCollection<Lead>("Leads");
        }

        public async Task<bool> CheckExistedLeadForSeller(string sellerId, string phone)
        {
            var filter = Builders<Lead>.Filter.And(
                Builders<Lead>.Filter.Eq("phone", phone),
                Builders<Lead>.Filter.Eq("assignedTo.0", new ObjectId(sellerId))
            );
            var exists = await _leads.Find(filter).AnyAsync();
            return exists;
        }

        public async Task<Lead?> GetLeadByEmailAsync(string email)
        {
            var filter = Builders<Lead>.Filter.Eq(l => l.email, email);
            return await _leads.Find(filter).FirstOrDefaultAsync();
        }

        public async Task<List<Lead>> GetLeadsByScoreAsync(LeadScore score)
        {
            var filter = Builders<Lead>.Filter.Eq(l => l.score, score);
            return await _leads.Find(filter).ToListAsync();
        }

        public async Task<Lead?> GetLeadByPhoneAsync(string phone)
        {
            var filter = Builders<Lead>.Filter.Eq(l => l.phone, phone);
            return await _leads.Find(filter).FirstOrDefaultAsync();
        }

        public async Task<Lead?> GetLeadByUserIdAsync(string userId)
        {
            var filter = Builders<Lead>.Filter.Eq(l => l.userId, userId);
            return await _leads.Find(filter).FirstOrDefaultAsync();
        }

        public async Task<List<Lead>> GetLeadsBySalerIdAsync(string salerId)
        {
            if (string.IsNullOrEmpty(salerId))
            {
                return [];
            }

            // Filter để tìm leads có assignedTo chứa object với id = salerId
            var filter = Builders<Lead>.Filter.ElemMatch(l => l.assignedTo,
                Builders<AssignedTo>.Filter.Eq(a => a.id, salerId));

            var leads = await _leads.Find(filter).ToListAsync();
            return leads;
        }


        public IQueryable<Lead> GetAll()
        {
            return _leads.AsQueryable();
        }
    }
}
