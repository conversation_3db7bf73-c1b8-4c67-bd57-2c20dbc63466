﻿using BusinessObjects.Models;
using static BusinessObjects.DTOs.GoogleDTOs.GoogleCredentialsDTOs;

namespace Services.Interfaces
{
    public interface IGoogleCredentialService
    {
        Task<GoogleCredentialResponse> SaveCredentialsAsync(string userId, SaveGoogleCredentialsRequest request, string? userAgent = null, string? ipAddress = null);

        Task<GoogleCredentialResponse> SaveOrUpdateCredentialsAsync(string userId, SaveGoogleCredentialsRequest request, string? userAgent = null, string? ipAddress = null);

        Task<GoogleCredentialResponse?> GetCredentialsByUserIdAsync(string userId);
        Task<GoogleCredentialModel?> GetActiveCredentialsAsync(string userId);
        Task<bool> DeactivateCredentialsAsync(string userId);
        Task<bool> UpdateCredentialsAsync(string userId, UpdateGoogleCredentialsRequest request);
        Task<bool> IsTokenValidAsync(string userId);
        Task<GoogleCredentialModel?> RefreshTokenAsync(string userId, string newAccessToken, DateTime? newExpiry);
        Task<List<GoogleCredentialModel>> GetExpiredTokensAsync();

        Task<GoogleCredentialModel?> GetValidCredentialsAsync(string userId);
    }
}
