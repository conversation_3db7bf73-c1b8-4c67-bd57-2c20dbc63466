﻿using BusinessObjects.DTOs.AppoinmentDTOs;

namespace BusinessObjects.DTOs.ResultDTOs
{
    public class ResultDTO
    {
        public class AssignmentResult
        {
            public bool isSuccess { get; set; }
            public string appointmentId { get; set; }
            public string sellerId { get; set; }
            public string sellerName { get; set; }
            public TaskStatus calendarTaskStatus { get; set; }
            public TaskStatus emailTaskStatus { get; set; }
            public string message { get; set; }
        }

        /// <summary>
        /// Result cho reassignment operation
        /// </summary>
        public class ReassignmentResult
        {
            public bool isSuccess { get; set; }
            public string appointmentId { get; set; } = string.Empty;
            public string? oldSellerId { get; set; }
            public string? oldSellerName { get; set; }
            public string newSellerId { get; set; } = string.Empty;
            public string newSellerName { get; set; } = string.Empty;
            public TaskStatus calendarTaskStatus { get; set; }
            public TaskStatus emailTaskStatus { get; set; }
            public string message { get; set; } = string.Empty;
            public List<string> warnings { get; set; } = new List<string>();
        }

        public class ConflictCheckResult
        {
            public bool hasAnyConflict { get; set; }
            public bool hasUserConflict { get; set; }
            public bool hasSalerConflict { get; set; }
            public List<AppointmentConflictInfo> userConflicts { get; set; } = new List<AppointmentConflictInfo>();
            public List<AppointmentConflictInfo> salerConflicts { get; set; } = new List<AppointmentConflictInfo>();

            public string GetConflictMessage()
            {
                var messages = new List<string>();

                if (hasUserConflict)
                {
                    var userConflictDetails = userConflicts.Select(c =>
                        $"• {c.propertyTitle} at {c.propertyAddress} on {c.date:yyyy-MM-dd HH:mm}").ToList();
                    messages.Add($"User conflicts:\n{string.Join("\n", userConflictDetails)}");
                }

                if (hasSalerConflict)
                {
                    var salerConflictDetails = salerConflicts.Select(c =>
                        $"• {c.propertyTitle} at {c.propertyAddress} on {c.date:yyyy-MM-dd HH:mm}").ToList();
                    messages.Add($"Saler conflicts:\n{string.Join("\n", salerConflictDetails)}");
                }

                return string.Join("\n\n", messages);
            }
        }

        public class BackgroundTaskResult
        {
            public TaskStatus emailStatus { get; set; }
            public TaskStatus calendarStatus { get; set; }
        }

        public enum TaskStatus
        {
            Pending,
            Success,
            Failed
        }


    }
}
