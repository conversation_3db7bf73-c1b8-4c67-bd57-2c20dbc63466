﻿using BusinessObjects.Models;
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.DTOs
{
    public class UserDTO
    {
        public string Id { get; set; }
        public string userName { get; set; }
        public string fullName { get; set; }
        public string email { get; set; }
        public string password { get; set; }
        public string? phoneNumber { get; set; }
        public string? avatar { get; set; }
        public Status? status { get; set; }
        public UserRole role { get; set; }
        public string? about { get; set; }
        [Required]
        public DateTime? birthdate { get; set; }
        public DateTime joinedAt { get; set; }
    }
}
