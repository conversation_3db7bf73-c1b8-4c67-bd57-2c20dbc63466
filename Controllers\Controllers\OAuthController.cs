﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Services.Interfaces;
using static BusinessObjects.DTOs.GoogleDTOs.GoogleCredentialsDTOs;

namespace Controllers.Controllers
{
    [Route("api/oauth-calendar")]
    [Authorize]
    [ApiController]
    public class OAuthController : ControllerBase
    {

        private readonly IGoogleOAuthService _googleOAuthService;
        private readonly ILogger<OAuthController> _logger;
        private readonly ICurrentUserService _currentUserService;
        private readonly IGoogleCredentialService _googleCredentialService;
        private readonly ICustomCalendarService _calendarService;
        private readonly IUserService _userService;


        public OAuthController(
            ICustomCalendarService calendarService,
            IGoogleOAuthService googleOAuthService,
            ILogger<OAuthController> logger,
            ICurrentUserService currentUserService,
            IGoogleCredentialService googleCredentialService,
            IUserService userService)
        {
            _googleOAuthService = googleOAuthService;
            _logger = logger;
            _currentUserService = currentUserService;
            _googleCredentialService = googleCredentialService;
            _calendarService = calendarService;
            _userService = userService;
        }

        #region Google OAuth Flow

        /// <summary>
        /// Bước 1: Tạo URL để user authorize với Google
        /// </summary>
        [HttpGet("auth/google")]
        [Authorize]
        public IActionResult GetGoogleAuthUrl([FromQuery] string? returnUrl = null)
        {
            try
            {
                var userId = _currentUserService.GetUserId();
                if (string.IsNullOrEmpty(userId))
                    return Unauthorized("User ID not found");

                var state = returnUrl ?? "calendar_setup";
                var authUrl = _googleOAuthService.GenerateCalendarAuthUrl(state, userId);

                return Ok(new { authUrl, message = "Redirect user to this URL for Google authorization" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating Google auth URL");
                return StatusCode(500, new { message = "Error generating authorization URL" });
            }
        }

        /// <summary>
        /// Bước 2: Callback từ Google sau khi user authorize
        /// </summary>
        [HttpGet("auth/google/callback")]
        [AllowAnonymous]
        public async Task<IActionResult> GoogleCallback([FromQuery] string code, [FromQuery] string state)
        {
            try
            {
                // ✅ Validate inputs
                if (string.IsNullOrEmpty(code))
                {
                    _logger.LogWarning("Google callback received without authorization code");
                    return Redirect("/calendar/setup?error=missing_code");
                }

                if (string.IsNullOrEmpty(state))
                {
                    _logger.LogWarning("Google callback received without state parameter");
                    return Redirect("/calendar/setup?error=missing_state");
                }

                // Parse state để lấy userId
                var stateParts = state.Split('|');
                if (stateParts.Length != 2 || string.IsNullOrEmpty(stateParts[1]))
                {
                    _logger.LogWarning($"Invalid state parameter format: {state}");
                    return Redirect("/calendar/setup?error=invalid_state");
                }

                var originalState = stateParts[0];
                var userId = stateParts[1];

                _logger.LogInformation($"Processing Google callback for user: {userId}");

                // Exchange code for tokens
                var tokens = await _googleOAuthService.GetTokensFromCodeAsync(code);

                if (tokens == null)
                {
                    _logger.LogError("Failed to exchange code for tokens");
                    return Redirect("/calendar/setup?error=token_exchange_failed");
                }

                // ✅ Validate token response
                if (string.IsNullOrEmpty(tokens.AccessToken))
                {
                    _logger.LogError("Received empty access token from Google");
                    return Redirect("/calendar/setup?error=invalid_token");
                }

                // ✅ Get user info from Google (using the new method) (if use did not logged in with google before => fail
                var userInfo = await _googleOAuthService.GetUserInfoWithGoogleApiAsync(tokens.AccessToken);
                if (userInfo == null)
                {
                    _logger.LogError("Failed to retrieve user info from Google");
                    return Redirect("/calendar/setup?error=user_info_failed");
                }

                // ✅ Validate user info
                //if (!_googleOAuthService.ValidateUserInfo(userInfo))
                //{
                //    _logger.LogError($"Invalid user info received from Google for user: {userId}");
                //    return Redirect("/calendar/setup?error=invalid_user_info");
                //}

                // ✅ Get system user email and validate
                var systemUser = await _userService.GetUserProfileAsync(userId);
                if (systemUser == null)
                {
                    _logger.LogError($"System user not found: {userId}");
                    return Redirect("/calendar/setup?error=user_not_found");
                }

                if (!userInfo.email.Equals(systemUser.email, StringComparison.OrdinalIgnoreCase))
                {
                    _logger.LogWarning($"Email mismatch for userId {userId}. Expected {systemUser.email}, but got {userInfo.email}");
                    return Redirect("/calendar/setup?error=email_mismatch");
                }

                // ✅ Map to SaveGoogleCredentialsRequest
                var saveRequest = new SaveGoogleCredentialsRequest
                {
                    accessToken = tokens.AccessToken,
                    refreshToken = tokens.RefreshToken,
                    tokenExpiry = DateTime.UtcNow.AddSeconds(tokens.ExpiresInSeconds ?? 3600),
                    scope = string.Join(" ", GetRequestedScopes()),
                    tokenType = tokens.TokenType ?? "Bearer"
                };

                // Get request metadata
                var userAgent = Request.Headers["User-Agent"].ToString();
                var ipAddress = GetClientIpAddress();

                // Store credentials
                var savedCredential = await _googleCredentialService.SaveOrUpdateCredentialsAsync(
                    userId,
                    saveRequest,
                    userAgent,
                    ipAddress
                );

                _logger.LogInformation($"Successfully saved Google credentials for user: {userId}");
                _logger.LogInformation($"  - Credential ID: {savedCredential.id}");
                _logger.LogInformation($"  - Calendar ID: {savedCredential.calendarId}");
                _logger.LogInformation($"  - Google User: {userInfo.FullName} ({userInfo.email})");

                // ✅ Redirect với thêm thông tin
                var redirectUrl = $"/calendar/setup?success=true&state={originalState}&credential_id={savedCredential.id}&calendar_id={savedCredential.calendarId}";
                return Redirect(redirectUrl);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error in Google callback");
                return Redirect("/calendar/setup?error=unexpected_error");
            }
        }





        #endregion

        // ✅ Helper method để get scopes
        private string[] GetRequestedScopes()
        {
            return new[]
            {
        "https://www.googleapis.com/auth/calendar",
        "https://www.googleapis.com/auth/calendar.events",
        "https://www.googleapis.com/auth/calendar.readonly",
        "https://www.googleapis.com/auth/userinfo.email",
        "https://www.googleapis.com/auth/userinfo.profile"
    };
        }

        // ✅ Helper method để get client IP
        private string GetClientIpAddress()
        {
            try
            {
                var httpContext = Request.HttpContext;

                // ✅ All possible IP headers to check
                var ipHeaders = new[]
                {
            "X-Forwarded-For",
            "X-Real-IP",
            "CF-Connecting-IP",     // Cloudflare
            "X-Client-IP",
            "X-Forwarded",
            "X-Cluster-Client-IP",  // AWS ELB
            "Forwarded-For",
            "Forwarded"
        };

                foreach (var header in ipHeaders)
                {
                    var headerValue = Request.Headers[header].FirstOrDefault();
                    if (string.IsNullOrEmpty(headerValue)) continue;

                    _logger.LogDebug($"Checking header {header}: {headerValue}");

                    // Handle comma-separated IPs (X-Forwarded-For can have multiple)
                    var ips = headerValue.Split(',', StringSplitOptions.RemoveEmptyEntries);

                    foreach (var ip in ips)
                    {
                        var cleanIp = ip.Trim();

                        // Skip private/internal IPs if we're looking for real client IP
                        if (IsValidPublicIpAddress(cleanIp))
                        {
                            _logger.LogInformation($"Found public IP from {header}: {cleanIp}");
                            return cleanIp;
                        }

                        // If no public IP found, use first valid IP
                        if (IsValidIpAddress(cleanIp))
                        {
                            _logger.LogDebug($"Found valid IP from {header}: {cleanIp}");
                            return cleanIp;
                        }
                    }
                }

                // ✅ Fallback to connection IP
                var connectionIp = httpContext.Connection.RemoteIpAddress?.ToString();
                if (!string.IsNullOrEmpty(connectionIp))
                {
                    // Handle IPv6 localhost
                    if (connectionIp == "::1") return "127.0.0.1";

                    // Clean IPv6 scope
                    if (connectionIp.Contains('%'))
                        connectionIp = connectionIp.Split('%')[0];

                    _logger.LogDebug($"Using connection IP: {connectionIp}");
                    return connectionIp;
                }

                _logger.LogWarning("No IP address found, using Unknown");
                return "Unknown";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error determining client IP");
                return "Unknown";
            }
        }

        private bool IsValidPublicIpAddress(string ipAddress)
        {
            if (!IsValidIpAddress(ipAddress)) return false;

            if (!System.Net.IPAddress.TryParse(ipAddress, out var ip)) return false;

            // Check if it's a public IP (not private/internal)
            var bytes = ip.GetAddressBytes();

            // IPv4 private ranges
            if (ip.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
            {
                // 10.0.0.0/8
                if (bytes[0] == 10) return false;

                // **********/12
                if (bytes[0] == 172 && bytes[1] >= 16 && bytes[1] <= 31) return false;

                // ***********/16
                if (bytes[0] == 192 && bytes[1] == 168) return false;

                // *********/8 (localhost)
                if (bytes[0] == 127) return false;

                // ***********/16 (link-local)
                if (bytes[0] == 169 && bytes[1] == 254) return false;
            }

            return true;
        }

        private bool IsValidIpAddress(string ipAddress)
        {
            if (string.IsNullOrWhiteSpace(ipAddress))
                return false;

            // Remove port if present
            if (ipAddress.Contains(':') && !ipAddress.StartsWith('['))
            {
                var parts = ipAddress.Split(':');
                if (parts.Length == 2 && int.TryParse(parts[1], out _))
                {
                    ipAddress = parts[0];
                }
            }

            return System.Net.IPAddress.TryParse(ipAddress, out _);
        }



    }
}
