﻿using BusinessObjects.DTOs.StatisticDTOs;
using BusinessObjects.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using Repositories.Interfaces;
using Services.Interfaces;
using System.Security.Claims;

namespace Services.Implements
{
    public class StatisticsService : IStatisticsService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<StatisticsService> _logger;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IDealService _dealService;

        public StatisticsService(
            IUnitOfWork unitOfWork,
            ILogger<StatisticsService> logger,
            IHttpContextAccessor httpContextAccessor,
            IDealService dealService)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
            _httpContextAccessor = httpContextAccessor;
            _dealService = dealService;
        }

        public async Task<StatisticsResponse> GetTopKPIsAsync(StatisticsQuery query)
        {
            var (currentUserId, currentUser) = await GetCurrentUserAsync();

            return currentUser.role switch
            {
                UserRole.Admin => await GetAdminTopKPIsAsync(query),
                UserRole.Saler => await GetSalerTopKPIsAsync(currentUserId, query),
                _ => throw new UnauthorizedAccessException("User does not have permission to view statistics.")
            };
        }

        public async Task<StatisticsResponse> GetAdminTopKPIsAsync(StatisticsQuery query)
        {
            try
            {
                _logger.LogInformation("Generating admin dashboard statistics");

                // Parallel execution for better performance
                var (totalLeads, totalLeadsToday) = await Task.WhenAll(
                    GetLeadsCountAsync(null, query.leadsFilters),
                    GetLeadsCountAsync(null, CreateTodayQuery())
                ).ContinueWith(t => (t.Result[0], t.Result[1]));

                var response = CreateStatsResponse(new Dictionary<string, object>
                {
                    ["totalLeadsToday"] = CreateStatItem(totalLeadsToday, "New Leads Today", "Number of leads created today"),
                    ["totalLeads"] = CreateStatItem(totalLeads, "Total Leads", "Total number of leads filtered by query")
                }, query.leadsFilters);

                _logger.LogInformation("Admin stats generated: Total={TotalLeads}, Today={TotalToday}", totalLeads, totalLeadsToday);
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating admin dashboard statistics");
                throw;
            }
        }

        // ✅ Updated Saler Dashboard method
        public async Task<StatisticsResponse> GetSalerTopKPIsAsync(string salerId, StatisticsQuery query)
        {
            try
            {
                _logger.LogInformation("Generating saler dashboard statistics for {SalerId}", salerId);

                // Parallel execution for all statistics
                var statisticsTasks = new Task<int>[]
                {
            GetLeadsCountAsync(salerId, query.leadsFilters),
            GetLeadsAssignedTodayAsync(salerId),
            _dealService.GetDealsInProgressCountAsync(salerId),
            _dealService.GetDealsClosedThisMonthCountAsync(salerId),
            _dealService.GetDealsWonThisMonthCountAsync(salerId),
            _dealService.GetDealsLostThisMonthCountAsync(salerId)
                };

                var results = await Task.WhenAll(statisticsTasks);

                var totalLeadsAssigned = results[0];
                var leadsAssignedToday = results[1];
                var dealsInProgress = results[2];
                var dealsClosedThisMonth = results[3];
                var dealsWonThisMonth = results[4];
                var dealsLostThisMonth = results[5];

                // Get additional statistics
                var dealsByStatusTask = _dealService.GetDealsByStatusCountAsync(salerId);
                var conversionRateTask = _dealService.GetConversionRateAsync(salerId, totalLeadsAssigned);

                await Task.WhenAll(dealsByStatusTask, conversionRateTask);

                var dealsByStatus = await dealsByStatusTask;
                var conversionRate = await conversionRateTask;

                var response = CreateStatsResponse(new Dictionary<string, object>
                {
                    ["leadsAssignedToday"] = CreateStatItem(
                        leadsAssignedToday,
                        "Assigned Today",
                        "Leads assigned to me today"
                    ),
                    ["totalLeadsAssigned"] = CreateStatItem(
                        totalLeadsAssigned,
                        "My Total Leads",
                        "Total leads assigned to me"
                    ),
                    ["dealsInProgress"] = CreateStatItem(
                        dealsInProgress,
                        "Deals in Progress",
                        "Active deals (New, Contacted, Negotiation, Closing)",
                        "info"
                    ),
                    ["dealsClosedThisMonth"] = CreateStatItem(
                        dealsClosedThisMonth,
                        "Deals Closed This Month",
                        "Deals won or lost this month",
                        "primary"
                    ),
                    ["dealsWonThisMonth"] = CreateStatItem(
                        dealsWonThisMonth,
                        "Deals Won This Month",
                        "Successfully closed deals this month",
                        "success"
                    ),
                    ["dealsLostThisMonth"] = CreateStatItem(
                        dealsLostThisMonth,
                        "Deals Lost This Month",
                        "Lost deals this month",
                        "danger"
                    ),
                    ["conversionRate"] = CreateStatItem(
                        $"{conversionRate:F1}%",
                        "Conversion Rate",
                        "Percentage of leads converted to won deals",
                        "warning"
                    ),
                    ["dealsByStatus"] = CreateDealStatusStats(dealsByStatus)
                }, query.leadsFilters);

                _logger.LogInformation(
                    "Comprehensive saler stats generated for {SalerId}: Leads={TotalLeads}, DealsInProgress={DealsInProgress}, DealsWon={DealsWon}, ConversionRate={ConversionRate}%",
                    salerId, totalLeadsAssigned, dealsInProgress, dealsWonThisMonth, conversionRate);

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating saler dashboard statistics for {SalerId}", salerId);
                throw;
            }
        }

        public async Task<StatisticsResponse> GetTodayTaskAndScheduledPropertyViewings(string salerId)
        {
            try
            {
                _logger.LogInformation("Generating admin dashboard statistics");

                // Parallel execution for better performance
                var dealValueByProperty = await _dealService.GetDealValueByPropertyTypeAsync();

                var response = CreateStatsResponse(new Dictionary<string, object>
                {
                    ["dealValueByProperty"] = CreateStatItem(dealValueByProperty, "Deal Value by Property Type", "Deal Value by Property Type for each property type (e.g. Apartment, House)"),
                });

                //_logger.LogInformation("Admin stats generated: Total={TotalLeads}, Today={TotalToday}", totalLeads, totalLeadsToday);
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error Today's Task And Scheduled Property Viewings");
                throw;
            }
        }


        // ✅ Method mới để lấy leads assigned today cho saler
        private async Task<int> GetLeadsAssignedTodayAsync(string salerId)
        {
            try
            {
                var builder = Builders<Lead>.Filter;
                var today = DateOnly.FromDateTime(DateTime.Today);

                // Filter leads where saler was assigned today
                var filter = builder.ElemMatch(lead => lead.assignedTo,
                    assigned => assigned.id == salerId &&
                               assigned.assignedAt == today);

                var count = await _unitOfWork.Leads.CountAsync(filter);

                _logger.LogInformation("Retrieved {Count} leads assigned today to saler {SalerId}", count, salerId);
                return (int)count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting leads assigned today for saler {SalerId}", salerId);
                throw;
            }
        }



        #region Private Helper Methods

        private async Task<(string userId, User user)> GetCurrentUserAsync()
        {
            var currentUserId = _httpContextAccessor.HttpContext?.User?.FindFirstValue("UserID");
            if (string.IsNullOrEmpty(currentUserId))
                throw new InvalidDataException("Current user ID not found.");

            var currentUser = await _unitOfWork.Users.GetByIdAsync(currentUserId);
            if (currentUser == null)
                throw new InvalidDataException("Current user not found.");

            return (currentUserId, currentUser);
        }

        private async Task<int> GetLeadsCountAsync(string salerId, LeadsStatisticsQuery query)
        {
            try
            {
                var filter = BuildLeadFilter(salerId, query);
                var count = await _unitOfWork.Leads.CountAsync(filter);
                return (int)count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting leads count for {UserType}: {SalerId}",
                    salerId == null ? "Admin" : "Saler", salerId ?? "All");
                throw;
            }
        }

        private static LeadsStatisticsQuery CreateTodayQuery() => new() { today = true };

        private object CreateStatItem(object value, string title, string description, string type = "default")
        {
            return new
            {
                value = value,
                title = title,
                description = description,
                type = type,
                timestamp = DateTime.UtcNow
            };
        }


        private static StatisticsResponse CreateStatsResponse(Dictionary<string, object> stats, LeadsStatisticsQuery? query = null) => new()
        {
            dictionary = stats,
            metadata = new StatisticsMetadata
            {
                generatedAt = DateTime.UtcNow,
                filters = ConvertQueryToFilters(query),
                version = "1.0.0"
            }
        };

        // ✅ Updated BuildLeadFilter method
        private FilterDefinition<Lead> BuildLeadFilter(string salerId, LeadsStatisticsQuery query)
        {
            var builder = Builders<Lead>.Filter;
            var filters = new List<FilterDefinition<Lead>>();

            // ✅ THAY ĐỔI: Saler restriction với cấu trúc mới
            if (!string.IsNullOrEmpty(salerId))
            {
                filters.Add(builder.ElemMatch(lead => lead.assignedTo,
                    assigned => assigned.id == salerId));
            }

            if (query == null)
                return filters.Count > 0 ? builder.And(filters) : builder.Empty;

            // Lead-specific filters
            if (query.source.HasValue)
                filters.Add(builder.Eq(lead => lead.source, query.source.Value));

            if (query.score.HasValue)
                filters.Add(builder.Eq(lead => lead.score, query.score.Value));

            if (!string.IsNullOrEmpty(query.assignedTo))
            {
                filters.Add(builder.ElemMatch(lead => lead.assignedTo,
                    assigned => assigned.id == query.assignedTo));
            }

            // Date filters (based on createdAt)
            var dateFilter = BuildDateFilter(builder, query);
            if (dateFilter != builder.Empty)
                filters.Add(dateFilter);

            return filters.Count > 0 ? builder.And(filters) : builder.Empty;
        }

        private static FilterDefinition<Lead> BuildDateFilter(FilterDefinitionBuilder<Lead> builder, LeadsStatisticsQuery query)
        {
            // Custom date range has priority
            if (query.dateFrom.HasValue || query.dateTo.HasValue)
            {
                var dateFilters = new List<FilterDefinition<Lead>>();

                if (query.dateFrom.HasValue)
                    dateFilters.Add(builder.Gte(lead => lead.createdAt, query.dateFrom.Value));

                if (query.dateTo.HasValue)
                    dateFilters.Add(builder.Lt(lead => lead.createdAt, query.dateTo.Value.Date.AddDays(1)));

                return dateFilters.Count > 0 ? builder.And(dateFilters) : builder.Empty;
            }

            // Quick date filters
            var dateRanges = new Dictionary<bool, (DateTime start, DateTime end)>
            {
                [query.today] = (DateTime.Today, DateTime.Today.AddDays(1)),
                [query.yesterday] = (DateTime.Today.AddDays(-1), DateTime.Today),
                [query.thisWeek] = (DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek), DateTime.Today.AddDays(7 - (int)DateTime.Today.DayOfWeek)),
                [query.thisMonth] = (new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1), new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1).AddMonths(1)),
                [query.last7Days] = (DateTime.Today.AddDays(-7), DateTime.Today.AddDays(1)),
                [query.last30Days] = (DateTime.Today.AddDays(-30), DateTime.Today.AddDays(1))
            };

            var activeRange = dateRanges.FirstOrDefault(x => x.Key);
            if (activeRange.Key)
            {
                var (start, end) = activeRange.Value;
                return builder.And(
                    builder.Gte(lead => lead.createdAt, start),
                    builder.Lt(lead => lead.createdAt, end)
                );
            }

            return builder.Empty;
        }

        private static Dictionary<string, object> ConvertQueryToFilters(LeadsStatisticsQuery query)
        {
            if (query == null) return new Dictionary<string, object>();

            var filters = new Dictionary<string, object>();
            var properties = new Dictionary<string, object>
            {
                [nameof(query.source)] = query.source?.ToString(),
                [nameof(query.score)] = query.score?.ToString(),
                [nameof(query.assignedTo)] = query.assignedTo,
                [nameof(query.dateFrom)] = query.dateFrom,
                [nameof(query.dateTo)] = query.dateTo,
                [nameof(query.today)] = query.today,
                [nameof(query.yesterday)] = query.yesterday,
                [nameof(query.thisWeek)] = query.thisWeek,
                [nameof(query.thisMonth)] = query.thisMonth,
                [nameof(query.last7Days)] = query.last7Days,
                [nameof(query.last30Days)] = query.last30Days
            };

            foreach (var (key, value) in properties)
            {
                if (value != null && (value is not bool boolValue || boolValue))
                    filters[key] = value;
            }

            return filters;
        }

        private object CreateDealStatusStats(Dictionary<string, int> dealsByStatus)
        {
            return new
            {
                value = dealsByStatus,
                title = "Deals by Status",
                description = "Breakdown of deals by current status",
                details = new
                {
                    new_deals = dealsByStatus.GetValueOrDefault("New", 0),
                    contacted = dealsByStatus.GetValueOrDefault("Contacted", 0),
                    negotiation = dealsByStatus.GetValueOrDefault("Negotiation", 0),
                    closing = dealsByStatus.GetValueOrDefault("Closing", 0),
                    won = dealsByStatus.GetValueOrDefault("Won", 0),
                    lost = dealsByStatus.GetValueOrDefault("Lost", 0)
                }
            };
        }




        #endregion
    }
}
