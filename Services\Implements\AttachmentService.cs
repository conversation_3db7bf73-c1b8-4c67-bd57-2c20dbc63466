﻿using BusinessObjects.Models;
using Microsoft.AspNetCore.Http;
using MongoDB.Bson;
using Repositories.Interfaces;
using Services.Interfaces;

namespace Services.Implements
{
    public class AttachmentService : IAttachmentService
    {
        private readonly FirebaseService _firebaseService;
        private readonly IUnitOfWork _unitOfWork;

        public AttachmentService(FirebaseService firebaseService, IUnitOfWork unitOfWork)
        {
            _firebaseService = firebaseService;
            _unitOfWork = unitOfWork;
        }

        public async Task<List<Attachment>> UploadAttachmentsAsync(List<IFormFile> files)
        {
            var attachments = new List<Attachment>();

            foreach (var file in files)
            {
                if (file.Length > 0)
                {
                    var stream = file.OpenReadStream();
                    try
                    {
                        var fileUrl = await _firebaseService.UploadAttachmentMessageAsync(stream, file.FileName);
                        var attachment = new Attachment
                        {
                            id = ObjectId.GenerateNewId().ToString(),
                            fileName = file.FileName,
                            fileUrl = fileUrl,
                            fileType = file.ContentType,
                            fileSize = file.Length
                        };
                        attachments.Add(attachment);
                        await _unitOfWork.Attachments.AddAsync(attachment);
                    }
                    finally
                    {
                        stream.Close();
                        stream.Dispose();
                    }
                }
            }
            return attachments;
        }

        public async Task<List<Attachment>> UploadFloorPlanAsync(List<IFormFile> files)
        {
            var attachments = new List<Attachment>();

            foreach (var file in files)
            {
                if (file.Length > 0)
                {
                    var stream = file.OpenReadStream();
                    try
                    {
                        var fileUrl = await _firebaseService.UploadFloorPlanAsync(stream, file.FileName);
                        var attachment = new Attachment
                        {
                            id = ObjectId.GenerateNewId().ToString(),
                            fileName = file.FileName,
                            fileUrl = fileUrl,
                            fileType = file.ContentType,
                            fileSize = file.Length
                        };
                        attachments.Add(attachment);
                        await _unitOfWork.Attachments.AddAsync(attachment);
                    }
                    finally
                    {
                        stream.Close();
                        stream.Dispose();
                    }
                }
            }

            return attachments;

        }

        public async Task<Attachment> UploadPropertyVideoAsync(IFormFile file)
        {
            if (file == null || file.Length == 0)
            {
                throw new ArgumentException("File không được để trống.");
            }

            // Kiểm tra định dạng video qua Content-Type
            var allowedVideoTypes = new[]
            {
        "video/mp4",
        "video/avi",
        "video/mov",
        "video/wmv",
        "video/webm"
    };

            if (!allowedVideoTypes.Contains(file.ContentType.ToLower()))
            {
                throw new InvalidOperationException("Chỉ chấp nhận file video (MP4, AVI, MOV, WMV, WebM).");
            }

            // Kiểm tra kích thước file
            if (file.Length > 100 * 1024 * 1024) // 100MB
            {
                throw new InvalidOperationException("Kích thước file video vượt quá giới hạn 100MB.");
            }

            var stream = file.OpenReadStream();
            try
            {
                var fileUrl = await _firebaseService.UploadPropertyVideoAsync(stream, file.FileName);
                var attachment = new Attachment
                {
                    id = ObjectId.GenerateNewId().ToString(),
                    fileName = file.FileName,
                    fileUrl = fileUrl,
                    fileType = file.ContentType,
                    fileSize = file.Length
                };

                await _unitOfWork.Attachments.AddAsync(attachment);
                return attachment;
            }
            finally
            {
                stream.Close();
                stream.Dispose();
            }
        }


        public async Task<List<Attachment>> UploadPropertyImagesAsync(List<IFormFile> files)
        {
            var attachments = new List<Attachment>();
            foreach (var file in files)
            {
                if (file.Length > 0)
                {
                    var stream = file.OpenReadStream();
                    try
                    {
                        var fileUrl = await _firebaseService.UploadPropertyImagesAsync(stream, file.FileName);
                        var attachment = new Attachment
                        {
                            id = ObjectId.GenerateNewId().ToString(),
                            fileName = file.FileName,
                            fileUrl = fileUrl,
                            fileType = file.ContentType,
                            fileSize = file.Length
                        };
                        attachments.Add(attachment);
                        await _unitOfWork.Attachments.AddAsync(attachment);
                    }
                    finally
                    {
                        stream.Close();
                        stream.Dispose();
                    }
                }
            }
            return attachments;
        }

        public async Task<List<Attachment>> UploadPropertyLegalDocumentAsync(List<IFormFile> files)
        {
            var attachments = new List<Attachment>();
            foreach (var file in files)
            {
                if (file.Length > 0)
                {
                    var stream = file.OpenReadStream();
                    try
                    {
                        var fileUrl = await _firebaseService.UploadPropertyLegalDocumentAsync(stream, file.FileName);
                        var attachment = new Attachment
                        {
                            id = ObjectId.GenerateNewId().ToString(),
                            fileName = file.FileName,
                            fileUrl = fileUrl,
                            fileType = file.ContentType,
                            fileSize = file.Length
                        };
                        attachments.Add(attachment);
                        await _unitOfWork.Attachments.AddAsync(attachment);
                    }
                    finally
                    {
                        stream.Close();
                        stream.Dispose();
                    }
                }
            }
            return attachments;
        }
    }
}
