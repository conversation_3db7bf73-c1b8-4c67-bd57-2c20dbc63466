﻿using BusinessObjects.Validations;

namespace BusinessObjects.DTOs.DealDTOs
{
    public class DealUpdateRequest : IDealBaseRequest
    {
        [ValidObjectId(ErrorMessage = "leadId must be a valid ObjectId")]
        public string? leadId { get; set; }
        [ValidObjectId(ErrorMessage = "salesRepId must be a valid ObjectId")]
        public string? salesRepId { get; set; }
        /// <summary>
        /// The status value, represented as a string (e.g., "New", "Contacted", etc.)
        /// </summary>
        public string? status { get; set; }

        public int? position { get; set; }

        /// <summary>
        /// Filter by priority , e.g. "Low", "Medium", "High"
        /// </summary>
        public string? priority { get; set; }

        public string? title { get; set; }
        public string? description { get; set; }

        public string? propertyId { get; set; }

        public DealNoteRequest? note { get; set; }
    }
}
