using BusinessObjects.DTOs;
using BusinessObjects.Models;
using FirebaseAdmin.Auth.Multitenancy;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Pipelines.Sockets.Unofficial.Buffers;
using Repositories.Interfaces;
using Services.Interfaces;
using VNPAY.NET;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Services.Implements
{
    public class ContractService : IContractService
    {
        private readonly IUnitOfWork _unitOfWork;

        private readonly FirebaseService _firebaseService;
        private readonly IConfiguration _configuration;
        private readonly IVnpay _vnpay;
        private readonly IVnpayService _vnpayService;
        private readonly ICurrentUserService _currentUserService;
        private readonly IEmailService _emailService;
        private readonly IUserService _userService;

        public ContractService(IUnitOfWork unitOfWork, IVnpay vnPayservice, FirebaseService firebaseService, IVnpayService vnpayService, IConfiguration configuration, ICurrentUserService currentUserService, IEmailService emailService, IUserService userService)
        {
            _unitOfWork = unitOfWork;
            _firebaseService = firebaseService;
            _configuration = configuration;
            _vnpay = vnPayservice;
            _vnpayService = vnpayService;
            _currentUserService = currentUserService;
            _emailService = emailService;
            _userService = userService;
            _vnpay.Initialize(_configuration["Vnpay:TmnCode"], _configuration["Vnpay:HashSecret"], _configuration["Vnpay:BaseUrl"], _configuration["Vnpay:ReturnUrl"]);

        }

        public async Task<PagedResult<ContractDTO>> GetAllContractsAsync(
            int pageNumber, int pageSize,
            string? propertyId = null, string? ownerId = null, string? tenantId = null, string? buyerId = null, string? salerId = null, ContractType? contractType = null)
        {
            var allContracts = await _unitOfWork.Contracts.GetAllAsync();

            if (!string.IsNullOrEmpty(propertyId))
                allContracts = allContracts.Where(c => c.propertyId == propertyId);

            if (!string.IsNullOrEmpty(ownerId))
                allContracts = allContracts.Where(c => c.ownerId == ownerId);

            if (!string.IsNullOrEmpty(tenantId))
                allContracts = allContracts.Where(c => c.tenantId == tenantId);

            if (!string.IsNullOrEmpty(buyerId))
                allContracts = allContracts.Where(c => c.buyerId == buyerId);

            if (!string.IsNullOrEmpty(salerId))
                allContracts = allContracts.Where(c => c.salerId == salerId);

            if (contractType.HasValue)
                allContracts = allContracts.Where(c => c.contractType == contractType);

			var totalRecords = allContracts.Count();
            var contracts = allContracts.Skip((pageNumber - 1) * pageSize).Take(pageSize).ToList();
            var contractDtos = new List<ContractDTO>();
            foreach (var contract in contracts)
            {
                contractDtos.Add(await MapToDTO(contract));
            }
            return new PagedResult<ContractDTO>
            {
                data = contractDtos,
                totalCount = totalRecords,
                currentPage = pageNumber,
                pageSize = pageSize
            };
        }

        public async Task<ContractDTO> GetContractByIdAsync(string id)
        {
            var contract = await _unitOfWork.Contracts.GetByIdAsync(id);
            if (contract == null)
                return null;

            return await MapToDTO(contract);
        }

        public async Task<PagedResult<ContractDTO>> GetActiveContractsAsync(int pageNumber, int pageSize)
        {
            var allContracts = await _unitOfWork.Contracts.GetActiveContractsAsync();
            var totalRecords = allContracts.Count();
            var contracts = allContracts.Skip((pageNumber - 1) * pageSize).Take(pageSize).ToList();
            var contractDtos = new List<ContractDTO>();
            foreach (var contract in contracts)
            {
                contractDtos.Add(await MapToDTO(contract));
            }
            return new PagedResult<ContractDTO>
            {
                data = contractDtos,
                totalCount = totalRecords,
                currentPage = pageNumber,
                pageSize = pageSize
            };
        }

        public async Task<PagedResult<ContractDTO>> GetExpiringContractsAsync(int daysToExpire, int pageNumber, int pageSize)
        {
            var allContracts = await _unitOfWork.Contracts.GetExpiringContractsAsync(daysToExpire);
            var totalRecords = allContracts.Count();
            var contracts = allContracts.Skip((pageNumber - 1) * pageSize).Take(pageSize).ToList();
            var contractDtos = new List<ContractDTO>();
            foreach (var contract in contracts)
            {
                contractDtos.Add(await MapToDTO(contract));
            }
            return new PagedResult<ContractDTO>
            {
                data = contractDtos,
                totalCount = totalRecords,
                currentPage = pageNumber,
                pageSize = pageSize
            };
        }
        public async Task<ContractDTO> CreateSaleContractAsync(CreateSaleContractDTO createSaleContractDTO)
        {
            var property = await _unitOfWork.Properties.GetByIdAsync(createSaleContractDTO.propertyId);
            if (property == null)
                throw new Exception("Property not found");
            
            var contract = new Contract
            {
                propertyId = createSaleContractDTO.propertyId,
                ownerId = property.ownerId, 
                buyerId = createSaleContractDTO.buyerId,
                salerId = property.salerId,
                status = ContractStatus.Active,
                contractType = ContractType.Sale,
                paymentSchedule = createSaleContractDTO.paymentSchedule
                .Select(dto => new PaymentScheduleItem
                {
                    paymentDate = dto.paymentDate,
                    amount = dto.amount,
                    note = dto.note,
                }).ToList(),
                pdfContractUrls = createSaleContractDTO.pdfContractUrls ?? new List<string>(),
                content = createSaleContractDTO.content,
                createdAt = DateTime.UtcNow,
                updatedAt = DateTime.UtcNow,
                isDeleted = false,
                deletedAt = null  
            };
            var created = await _unitOfWork.Contracts.AddAsync(contract);
            return await MapToDTO(created);
        }
        public async Task<ContractDTO> CreateRentalContractAsync(CreateRentalContractDTO createContractDTO)
        {
            // Validate property exists and get ownerId
            var property = await _unitOfWork.Properties.GetByIdAsync(createContractDTO.propertyId);
            if (property == null)
                throw new Exception("Property not found");

            if (!property.status.Equals(PropertyStatus.Available)) 
                throw new Exception("Property is not available");

            // Tạo hợp đồng mới, tự động lấy ownerId từ property.owner.id và chuyển đổi sang string
            var contract = new Contract
            {
                propertyId = createContractDTO.propertyId,
                ownerId = property.ownerId, // Chuyển đổi ObjectId thành string
                tenantId = createContractDTO.tenantId,
                salerId = property.salerId,
                contractStartDate = createContractDTO.contractStartDate,
                contractEndDate = createContractDTO.contractEndDate,
                paymentTerms = createContractDTO.paymentTerms,
                recurringRent = createContractDTO.curringRent,
                depositAmount = createContractDTO.depositAmount,
                rentDueDate = createContractDTO.rentDueDate ?? createContractDTO.contractStartDate.AddDays(5),
                status = ContractStatus.Active,
                contractType = ContractType.Rental,
                pdfContractUrls = createContractDTO.pdfContractUrls ?? new List<string>(),
                content = createContractDTO.content,
                renewalLogs = new List<RenewalLog>(),
                createdAt = DateTime.UtcNow,
                updatedAt = DateTime.UtcNow,
                isDeleted = false,
                deletedAt = null
            };

            var created = await _unitOfWork.Contracts.AddAsync(contract);
            return await MapToDTO(created);
        }

        public async Task<ContractDTO> UpdateRentalContractAsync(string id, UpdateRentalContractDTO updateContractDTO)
        {
            var existingContract = await _unitOfWork.Contracts.GetByIdAsync(id);
            if (existingContract == null)
                return null;

            // Update only provided fields
            if (updateContractDTO.contractEndDate.HasValue)
                existingContract.contractEndDate = updateContractDTO.contractEndDate.Value;

            if (updateContractDTO.paymentTerms.HasValue)
                existingContract.paymentTerms = updateContractDTO.paymentTerms.Value;

            if (updateContractDTO.status.HasValue)
                existingContract.status = updateContractDTO.status.Value;

            if (updateContractDTO.monthlyRent.HasValue)
                existingContract.recurringRent = updateContractDTO.monthlyRent.Value;

            if (!string.IsNullOrEmpty(updateContractDTO.content))
                existingContract.content = updateContractDTO.content;

			existingContract.updatedAt = DateTime.UtcNow;

            var updated = await _unitOfWork.Contracts.UpdateAsync(id, existingContract);
            return await MapToDTO(updated);
        }

		public async Task<ContractDTO> UpdateSaleContractAsync(string id, UpdateSaleContractDTO updateContractDTO)
		{
			var existingContract = await _unitOfWork.Contracts.GetByIdAsync(id);
			if (existingContract == null)
				return null;

            var listPaymentSchedule = updateContractDTO.paymentSchedule.Select(x => new PaymentScheduleItem
            {
                amount = x.amount,
                note = x.note,
                paymentDate = x.paymentDate
            }).ToList();

            // Update only provided fields
			if (updateContractDTO.status.HasValue)
				existingContract.status = updateContractDTO.status.Value;

            if (updateContractDTO.paymentSchedule.Count > 0 && updateContractDTO.paymentSchedule != null)
                existingContract.paymentSchedule = listPaymentSchedule;

            if (!string.IsNullOrEmpty(updateContractDTO.content))
                existingContract.content = updateContractDTO.content;

            existingContract.updatedAt = DateTime.UtcNow;

			var updated = await _unitOfWork.Contracts.UpdateAsync(id, existingContract);
			return await MapToDTO(updated);
		}

		public async Task<ContractDTO> RenewContractAsync(string id, RenewContractDTO renewContractDTO)
        {
            var existingContract = await _unitOfWork.Contracts.GetByIdAsync(id);
            if (existingContract == null)
                return null;

            // Create renewal log
            var renewalLog = new RenewalLog
            {
                previousEndDate = existingContract.contractEndDate.Value,
                newEndDate = renewContractDTO.newEndDate,
                renewalDate = DateTime.UtcNow,
                renewalNote = renewContractDTO.renewalNote
            };

            var updated = await _unitOfWork.Contracts.AddRenewalLogAsync(id, renewalLog);
            return await MapToDTO(updated);
        }

        public async Task<bool> DeleteContractAsync(string id)
        {
            var contract = await _unitOfWork.Contracts.GetByIdAsync(id);
            if (contract == null)
                return false;

            await _unitOfWork.Contracts.DeleteAsync(id);
            return true;
        }

        public async Task<ContractDTO> SoftDeleteContractAsync(string id)
        {
            var deleted = await _unitOfWork.Contracts.SoftDeleteAsync(id);
            if (deleted == null)
                return null;

            return await MapToDTO(deleted);
        }

        public async Task<PagedResult<ContractDTO>> GetDeletedContractsAsync(int pageNumber, int pageSize)
        {
            var allContracts = await _unitOfWork.Contracts.GetDeletedContractsAsync();
            var totalRecords = allContracts.Count();
            var contracts = allContracts.Skip((pageNumber - 1) * pageSize).Take(pageSize).ToList();
            var contractDtos = new List<ContractDTO>();
            foreach (var contract in contracts)
            {
                contractDtos.Add(await MapToDTO(contract));
            }
            return new PagedResult<ContractDTO>
            {
                data = contractDtos,
                totalCount = totalRecords,
                currentPage = pageNumber,
                pageSize = pageSize
            };
        }

        public async Task<ContractDTO> RestoreContractAsync(string id)
        {
            var restored = await _unitOfWork.Contracts.RestoreContractAsync(id);
            if (restored == null)
                return null;

            return await MapToDTO(restored);
        }

        public async Task<ContractDTO> UpdateContractPdfAsync(string id, IFormFile pdfFile)
        {
            var existingContract = await _unitOfWork.Contracts.GetByIdAsync(id);
            if (existingContract == null)
                return null;

            if (pdfFile != null && pdfFile.Length > 0)
            {
                using (var stream = pdfFile.OpenReadStream())
                {
                    var pdfUrl = await _firebaseService.UploadContractPdfAsync(stream, pdfFile.FileName);
                    existingContract.pdfContractUrls = new List<string> { pdfUrl };
                    existingContract.updatedAt = DateTime.UtcNow;
                }
            }

            var updated = await _unitOfWork.Contracts.UpdateAsync(id, existingContract);
            return await MapToDTO(updated);
        }

        // Helper methods
        private async Task<ContractDTO> MapToDTO(Contract contract)
        {
            var property = await _unitOfWork.Properties.GetByIdAsync(contract.propertyId);
            return new ContractDTO
            {
                id = contract.id.ToString(),
                Property = new PropertyDetails
                {
                    Id = contract.propertyId,
                    ownerId = property?.ownerId ?? string.Empty,
                    salerId = property?.salerId ?? string.Empty,
                    Name = property?.name ?? string.Empty,
                    TransactionType = property?.transactionType ?? PropertyTransactionType.ForRent,
                    Type = property?.type ?? PropertyType.Apartment,
                    Status = property?.status ?? PropertyStatus.Available,
                    Code = property?.code ?? string.Empty,
                    PriceDetails = property?.priceDetails,
                    Image = property?.images?.FirstOrDefault() ?? string.Empty,
                    YearBuilt = property?.yearBuilt ?? 0,
                    LegalDocuments = property?.legalDocuments ?? new List<string>()
                },
                tenantId = contract.tenantId,
                buyerId = contract.buyerId,
                contractStartDate = contract.contractStartDate,
                contractEndDate = contract.contractEndDate,
                paymentTerms = contract.paymentTerms,
                contractType = contract.contractType,             
                status = contract.status,
                recurringRent = contract.recurringRent,
                depositAmount = contract.depositAmount,
                pdfContractUrls = contract.pdfContractUrls,
                renewalLogs = contract.renewalLogs?.Select(log => new RenewalLogDTO
                {
                    previousEndDate = log.previousEndDate,
                    newEndDate = log.newEndDate,
                    renewalDate = log.renewalDate,
                    renewalNote = log.renewalNote
                }).ToList(),
                rentDueDate = contract.rentDueDate,
                content = contract.content,
				paymentSchedule = contract.paymentSchedule?.Select(x => new PaymentScheduleItemDTO
                {
                    amount = x.amount,
                    note = x.note,
                    paymentDate = x.paymentDate
                }).ToList()
			};
        }

        public async Task<string> CreatePayRentalContraclRequestAsync(string id)
        {
            var contract = await _unitOfWork.Contracts.GetByIdAsync(id);
            if (contract == null)
                throw new Exception("Rental contract not found");
            // Check if the payment is due
            if (contract.rentDueDate > DateTime.UtcNow)
                throw new Exception("Payment is not due yet");

            if (contract.isRentPaid == true) throw new Exception("Rent has already been paid");

            var total = contract.recurringRent + contract.depositAmount;
            var paymentUrl = await _vnpayService.CreatePaymentUrl(total.Value, contract.id.ToString());
            return paymentUrl;
        }
        
        public async Task<(bool Success, string Message)> ProcessIpnAsync(IQueryCollection query)
        {
            if (query == null || !query.Any())
                return (false, "Không tìm thấy thông tin thanh toán.");

            try
            {
                var paymentResult = _vnpay.GetPaymentResult(query);
                if (paymentResult.IsSuccess)
                {
                    var contractId = paymentResult.Description; // Đảm bảo OrderId là contractId
                    var contract = await _unitOfWork.Contracts.GetByIdAsync(contractId);
                    if (contract == null)
                        return (false, "Rental contract not found");

                    contract.isRentPaid = true;
                    await _unitOfWork.Contracts.UpdateAsync(contractId, contract);

                    return (true, "Pay successfully");
                }

                return (false, "Pay fail");
            }
            catch (Exception ex)
            {
                return (false, ex.Message);
            }
        }

        public async Task<PagedResult<ContractDTO>> GetAllContractForSalerAsync(int pageNumber, int pageSize, string? propertyId = null, string? ownerId = null, string? tenantId = null, string? buyerId = null, ContractType? contractType = null)
        {
            var salerId = _currentUserService.GetUserId();          

            return await GetAllContractsAsync(pageNumber, pageSize, propertyId, ownerId, tenantId, buyerId, salerId, contractType);
        }      
        public async Task<bool> SendContractByEmailAsync(string email, string contractLink)
        {
            try
            {
                if (string.IsNullOrEmpty(email))
                {
                    return false;
                }
                
                var subject = "Hợp đồng bất động sản - RevoLand";
                
                var emailContent = $@"
                    <div style='font-family: Arial, sans-serif; max-width: 600px; margin: auto; background: #f9f9f9; border: 1px solid #e0e0e0; padding: 30px; border-radius: 10px;'>                      
                        <h2 style='color: #2c3e50;'>Kính gửi Quý khách,</h2>
                        <p style='font-size: 16px; color: #333;'>Chúng tôi gửi đến bạn hợp đồng bất động sản từ hệ thống <strong>RevoLand</strong>.</p>
                        <p style='font-size: 16px; color: #333;'>Bạn có thể xem và tải hợp đồng tại đường dẫn sau:</p>
                        <div style='text-align: center; margin: 20px 0;'>
                            <a href='{contractLink}' style='display: inline-block; padding: 12px 24px; background-color: #3498db; color: #fff; text-decoration: none; border-radius: 6px; font-weight: bold;'>📄 Xem hợp đồng</a>
                        </div>
                        <p style='font-size: 16px; color: #333;'>Nếu bạn có bất kỳ câu hỏi nào, đừng ngần ngại liên hệ với chúng tôi nhé!</p>
                        <p style='font-size: 16px; color: #333;'>Trân trọng,</p>
                        <p style='font-weight: bold; color: #2c3e50;'>💼 Đội ngũ RevoLand</p>
                        <hr style='margin-top: 30px;' />
                        <p style='font-size: 12px; color: #888; text-align: center;'>Email này được gửi tự động. Vui lòng không phản hồi lại email này.</p>
                    </div>";

                await _emailService.SendEmailAsync(email, subject, emailContent);
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }
    }
}
