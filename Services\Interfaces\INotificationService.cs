﻿namespace Services.Interfaces
{
    public interface INotificationService
    {
        Task SendEmailAsync(string email, string subject, string message);
        Task SendSmsAsync(string phoneNumber, string message);
        Task SendInAppNotificationAsync(string userId, string message);
        Task SendNotificationAsync(string email, string phoneNumber, string userId, string message);

        Task SendNotificationToSellerAsync(string sellerId, string message);

        void RegisterSeller(string sellerId, string connectionId);
        void RemoveSeller(string connectionId);
    }
}
