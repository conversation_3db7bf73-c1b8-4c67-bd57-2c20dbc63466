﻿using BusinessObjects.DTOs.FacebookDTOs;
using Microsoft.Extensions.Configuration;
using Services.Interfaces;
using System.Text;
using System.Text.Json;

namespace Services.Implements
{
    public class FacebookMessengerService : IFacebookMessengerService
    {
        private readonly IHttpClientFactory _httpFactory;
        private readonly string _pageAccessToken;
        private readonly string _url = "https://graph.facebook.com/v17.0";
        private readonly string _pageId = "413753015162613";
        public FacebookMessengerService(IConfiguration config, IHttpClientFactory httpFactory)
        {
            _httpFactory = httpFactory;
            _pageAccessToken = config["Facebook:PageAccessToken"];
        }



        public async Task SubscribePageWebhookAsync()
        {
            var client = _httpFactory.CreateClient();
            var url = $"{_url}/{_pageId}/subscribed_apps" +
                      $"?access_token={_pageAccessToken}" +
                      $"&subscribed_fields=messages,messaging_postbacks,message_echoes";
            var resp = await client.PostAsync(url, null);
            resp.EnsureSuccessStatusCode();
        }

        public async Task SendTextMessageAsync(string recipientPsid, string text)
        {
            var url = $"{_url}/me/messages?access_token={_pageAccessToken}";
            var payload = new
            {
                recipient = new { id = recipientPsid },
                message = new { text }
            };
            var client = _httpFactory.CreateClient();
            var content = new StringContent(
            JsonSerializer.Serialize(payload),
            Encoding.UTF8,
            "application/json"
                                            );
            var response = await client.PostAsync(url, content);
            if (!response.IsSuccessStatusCode)
            {
                var errorJson = await response.Content.ReadAsStringAsync();
                // print log errorJson in console or file logs
                Console.Error.WriteLine($"Facebook Send API Error: {errorJson}");

                // throw exception
                response.EnsureSuccessStatusCode();
            }
        }

        public async Task<FacebookUserProfile> GetUserProfileAsync(string psid)
        {
            // 1. Build URL
            var url = $"{_url}/{psid}" +
                      $"?fields=first_name,last_name,name,profile_pic" +
                      $"&access_token={_pageAccessToken}";

            // 2. Call API
            var client = _httpFactory.CreateClient();
            var resp = await client.GetAsync(url);
            resp.EnsureSuccessStatusCode();

            // 3. Deserialize
            var json = await resp.Content.ReadAsStringAsync();
            var profile = JsonSerializer.Deserialize<FacebookUserProfile>(json,
                new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

            return profile;
        }
    }
}
