using BusinessObjects.Models;
using BusinessObjects.DatabaseSettings;
using Services.Interfaces;
using System.Threading.Tasks;
using MongoDB.Driver;
using Microsoft.Extensions.Configuration;
using Twilio;
using Twilio.Rest.Api.V2010.Account;
using Repositories.Interfaces;
using System;
using Services.Tools;

namespace Services.Implements
{
    public class RentalContractNotificationService : IRentalContractNotificationService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IEmailService _emailService;
        private readonly string _twilioAccountSid;
        private readonly string _twilioAuthToken;
        private readonly string _twilioPhoneNumber;

        public RentalContractNotificationService(IUnitOfWork unitOfWork, IEmailService emailService, IConfiguration configuration)
        {
            _unitOfWork = unitOfWork;
            _emailService = emailService;
            _twilioAccountSid = configuration["Twilio:AccountSid"];
            _twilioAuthToken = configuration["Twilio:AuthToken"];
            _twilioPhoneNumber = configuration["Twilio:PhoneNumber"];
            TwilioClient.Init(_twilioAccountSid, _twilioAuthToken);
        }

        public async Task SendNotificationAsync(string userId, string message, string type)
        {
            await SendEmailNotificationAsync(userId, message);
            await SendSmsNotificationAsync(userId, message);
            await SendInAppNotificationAsync(userId, message, type);
        }

        public async Task SendEmailNotificationAsync(string userId, string message)
        {
            var user = await _unitOfWork.Users.GetByIdAsync(userId);
            if (user != null && !string.IsNullOrEmpty(user.email))
            {
                var daysRemaining = message.Contains("sau") ? int.Parse(message.Split("sau")[1].Split("ngày")[0].Trim()) : 0;
                var contractId = "";
                var webAppUrl = "https://quindland.com";
                var emailContent = EmailTemplate.GetLeaseRenewalReminderEmail(user.fullName, DateTime.UtcNow.AddDays(daysRemaining), daysRemaining, contractId, webAppUrl);
                await _emailService.SendEmailAsync(user.email, "Rental Contract Reminder", emailContent);
                var notification = new RentalContractNotification
                {
                    id = System.Guid.NewGuid().ToString(),
                    userId = userId,
                    content = message,
                    sentAt = System.DateTime.UtcNow,
                    isDelivered = true
                };
                await _unitOfWork.RentalContractNotification.CreateNotificationAsync(notification);
            }
        }

        public async Task SendSmsNotificationAsync(string userId, string message)
        {
            var user = await _unitOfWork.Users.GetByIdAsync(userId);
            if (user != null && !string.IsNullOrEmpty(user.phoneNumber))
            {
                await MessageResource.CreateAsync(
                    body: message,
                    from: new Twilio.Types.PhoneNumber(_twilioPhoneNumber),
                    to: new Twilio.Types.PhoneNumber(user.phoneNumber)
                );
                var notification = new RentalContractNotification
                {
                    id = System.Guid.NewGuid().ToString(),
                    userId = userId,
                    content = message,
                    sentAt = System.DateTime.UtcNow,
                    isDelivered = true
                };
                await _unitOfWork.RentalContractNotification.CreateNotificationAsync(notification);
            }
        }

        public async Task SendInAppNotificationAsync(string userId, string message, string type)
        {
            var notification = new RentalContractNotification
            {
                id = System.Guid.NewGuid().ToString(),
                userId = userId,
                content = message,
                sentAt = System.DateTime.UtcNow,
                isDelivered = false
            };
            await _unitOfWork.RentalContractNotification.CreateNotificationAsync(notification);
        }
    }
} 