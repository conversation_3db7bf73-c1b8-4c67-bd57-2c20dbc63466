﻿using BusinessObjects.DTOs.RequestFeatures;

namespace BusinessObjects.QueryObject
{
    public class QueryDealKanban : RequestParameters
    {
        public string? searchTerm { get; set; }

        public string? leadId { get; set; }
        public string? salesRepId { get; set; }
        public DateTime? createdAt { get; set; }
        public DateTime? updatedAt { get; set; }
        public int? createdYear { get; set; }
        public int? createdMonth { get; set; }
        public int? createdDay { get; set; }
        public int? updatedYear { get; set; }
        public int? updatedMonth { get; set; }
        public int? updatedDay { get; set; }
        public string? status { get; set; }
        public DealEnumSortByKanban sortBy { get; set; } = DealEnumSortByKanban.createdAt;
        public bool? isAscending { get; set; }
        public enum DealEnumSortByKanban
        {
            createdAt,
            updatedAt
        }

    }
}
