﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.DTOs.TeamDTOs
{
	public class TeamResponse
	{
		public string id { get; set; } 
		public string name { get; set; }
		public string companyId { get; set; } 
		public string? description { get; set; }
		public DateTime createdAt { get; set; } 
		public DateTime? updatedAt { get; set; }
	}
}
