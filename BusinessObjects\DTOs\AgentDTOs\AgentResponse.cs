﻿using BusinessObjects.Models;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.DTOs.AgentDTOs
{
	public class AgentResponse
	{
		public string id { get; set; }

		public string fullName { get; set; }

		public string email { get; set; }

		public string phone { get; set; }

		public string? avatar { get; set; }

		public Gender? gender { get; set; }
		public string? bio { get; set; }
		public DateTime? joinedAt { get; set; }
	}
}
