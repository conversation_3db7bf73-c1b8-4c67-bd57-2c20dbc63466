﻿using NPOI.SS.Formula.Functions;

namespace Services.Tools
{
    public static class EmailTemplate
    {
        public static string GetPasswordResetEmail(string fullName, string resetLink)
        {
            return $@"
            <p>Hi {fullName},</p>
            <p>You recently requested to reset your password for your QuindLand account.</p>
            <p>Click the link below to reset your password:</p>
            <a href='{resetLink}'>{resetLink}</a>
            <p>If you did not request this, please ignore this email or contact support if you have questions.</p>
            <p>Thanks,<br>The QuindLand Team</p>";
        }

        public static string GetLeaseRenewalReminderEmail(string fullName, DateTime contractEndDate, int daysRemaining, string contractId, string webAppUrl)
        {
            string priority = daysRemaining <= 7 ? "high" : daysRemaining <= 14 ? "medium" : "low";
            string color = daysRemaining <= 7 ? "#dc3545" : daysRemaining <= 14 ? "#fd7e14" : "#28a745";
            string contractDetailsLink = $"{webAppUrl}/contracts/{contractId}";

            return $@"
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset='utf-8'>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                    .header {{ background-color: #f8f9fa; padding: 15px; text-align: center; border-radius: 5px 5px 0 0; }}
                    .content {{ padding: 20px; border: 1px solid #ddd; border-radius: 0 0 5px 5px; }}
                    .button {{ display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; 
                               text-decoration: none; border-radius: 4px; margin-top: 15px; }}
                    .priority {{ display: inline-block; padding: 5px 10px; background-color: {color}; color: white; 
                                border-radius: 4px; font-size: 12px; margin-left: 10px; }}
                    .footer {{ margin-top: 20px; font-size: 12px; color: #777; text-align: center; }}
                </style>
            </head>
            <body>
                <div class='container'>
                    <div class='header'>
                        <h2>QuindLand - Lease Renewal Notification</h2>
                    </div>
                    <div class='content'>
                        <p>Dear {fullName},</p>
                        
                        <p>We're sending this email to inform you that your rental contract is about to expire <strong>({daysRemaining} days remaining)</strong>
                        <span class='priority'>Priority: {priority}</span></p>
                        
                        <p><strong>Expiration date:</strong> {contractEndDate.ToString("MM/dd/yyyy")}</p>
                        
                        <p>Please consider the following options:</p>
                        <ul>
                            <li>Renew your current contract</li>
                            <li>Discuss adjustments to terms or rental price</li>
                            <li>End the contract and prepare to move out</li>
                        </ul>
                        
                        <p>Don't delay! Please contact us as soon as possible to avoid any disruption.</p>
                        
                        <a href='{contractDetailsLink}' class='button'>View Contract Details</a>
                        
                        <p>If you have already renewed or have any questions, please contact us.</p>
                        
                        <p>Regards,<br>The QuindLand Team</p>
                    </div>
                    <div class='footer'>
                        <p>This is an automated email, please do not reply. If you need assistance, please contact <a href='mailto:<EMAIL>'><EMAIL></a></p>
                    </div>
                </div>
            </body>
            </html>";
        }

        public static string GetRentDueReminderEmail(string fullName, DateTime dueDate)
        {
            return $@"
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset='utf-8'>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                    .header {{ background-color: #f8f9fa; padding: 15px; text-align: center; border-radius: 5px 5px 0 0; }}
                    .content {{ padding: 20px; border: 1px solid #ddd; border-radius: 0 0 5px 5px; }}
                    .button {{ display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; 
                               text-decoration: none; border-radius: 4px; margin-top: 15px; }}
                    .footer {{ margin-top: 20px; font-size: 12px; color: #777; text-align: center; }}
                </style>
            </head>
            <body>
                <div class='container'>
                    <div class='header'>
                        <h2>QuindLand - Nhắc nhở đóng tiền thuê nhà</h2>
                    </div>
                    <div class='content'>
                        <p>Xin chào {fullName},</p>
                        <p>Chúng tôi nhắc bạn rằng tiền thuê nhà của bạn sẽ đến hạn vào ngày {dueDate:dd/MM/yyyy}. Vui lòng thanh toán đúng hạn để tránh phí phạt.</p>
                        <p>Nếu bạn đã thanh toán hoặc có bất kỳ câu hỏi nào, vui lòng liên hệ với chúng tôi.</p>
                        <a href='https://your-web-app-url.com/payment' class='button'>Thanh toán ngay</a>
                        <p>Trân trọng,<br>Đội ngũ QuindLand</p>
                    </div>
                    <div class='footer'>
                        <p>Đây là email tự động, vui lòng không trả lời. Nếu cần hỗ trợ, liên hệ <a href='mailto:<EMAIL>'><EMAIL></a></p>
                    </div>
                </div>
            </body>
            </html>";
        }

        public static string GetOtpEmailContent(string fullName, string otpCode, int validMinutes)
        {
            return $@"
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset='utf-8'>
                    <style>
                        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                        .header {{ background-color: #f8f9fa; padding: 15px; text-align: center; border-radius: 5px 5px 0 0; }}
                        .content {{ padding: 20px; border: 1px solid #ddd; border-radius: 0 0 5px 5px; }}
                        .otp-code {{ font-size: 24px; font-weight: bold; color: #d63384; }}
                        .footer {{ margin-top: 20px; font-size: 12px; color: #777; text-align: center; }}
                    </style>
                </head>
                <body>
                    <div class='container'>
                        <div class='header'>
                            <h2>QuindLand - Mã xác thực OTP</h2>
                        </div>
                        <div class='content'>
                            <p>Xin chào {fullName},</p>
                            <p>Mã xác thực (OTP) của bạn là:</p>
                            <p class='otp-code'>{otpCode}</p>
                            <p>Mã có hiệu lực trong {validMinutes} phút. Vui lòng không chia sẻ mã này với bất kỳ ai.</p>
                            <p>Nếu bạn không yêu cầu mã này, vui lòng bỏ qua email.</p>
                            <p>Trân trọng,<br>Đội ngũ QuindLand</p>
                        </div>
                        <div class='footer'>
                            <p>Đây là email tự động, vui lòng không trả lời. Nếu cần hỗ trợ, liên hệ <a href='mailto:<EMAIL>'><EMAIL></a></p>
                        </div>
                    </div>
                </body>
                </html>";
        }

        public static string GetAppointmentEmailContent(string sellerName, string customerName, string propertyAddress, DateTime appointmentDateTime)
        {
            return $@"
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='utf-8'>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #f8f9fa; padding: 15px; text-align: center; border-radius: 5px 5px 0 0; }}
                .content {{ padding: 20px; border: 1px solid #ddd; border-radius: 0 0 5px 5px; }}
                .footer {{ margin-top: 20px; font-size: 12px; color: #777; text-align: center; }}
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h2>Thông báo đặt lịch hẹn</h2>
                </div>
                <div class='content'>
                    <p>Xin chào {sellerName},</p>
                    <p>Chúng tôi xin thông báo rằng khách hàng <strong>{customerName}</strong> đã đặt lịch hẹn để xem nhà.</p>
                    <p>Địa chỉ nhà: <strong>{propertyAddress}</strong></p>
                    <p>Thời gian hẹn: <strong>{appointmentDateTime.ToString("dd/MM/yyyy HH:mm")}</strong></p>
                    <p>Vui lòng chuẩn bị và xác nhận lịch hẹn này.</p>
                    <p>Trân trọng,<br>Đội ngũ hỗ trợ</p>
                </div>
                <div class='footer'>
                    <p>Đây là email tự động, vui lòng không trả lời. Nếu cần hỗ trợ, liên hệ <a href='mailto:<EMAIL>'><EMAIL></a></p>
                </div>
            </div>
        </body>
        </html>";
        }

		public static string GetInviteAgentEmailContent(string fullName, string inviteLink, string companyName, int validHours)
		{
			return $@"
	    <!DOCTYPE html>
	    <html>
	    <head>
		    <meta charset='utf-8'>
		    <style>
                body {{font - family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{max - width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{background - color: #f8f9fa; padding: 15px; text-align: center; border-radius: 5px 5px 0 0; }}
                .content {{padding: 20px; border: 1px solid #ddd; border-radius: 0 0 5px 5px; }}
                .button {{display: inline-block;
                    padding: 12px 24px;
                    background-color: #28a745;
                    color: #ffffff !important;
                    text-decoration: none !important;
                    border-radius: 6px;
                    font-weight: bold;
                    font-size: 16px;
                    transition: background-color 0.3s ease;
                }}
                .button:hover {{background - color: #218838;
                }}
                .footer {{margin - top: 20px; font-size: 12px; color: #777; text-align: center; }}
            </style>
	    </head>
	    <body>
		    <div class='container'>
			    <div class='header'>
				    <h2>{companyName} - Lời mời tham gia công ty</h2>
			    </div>
			    <div class='content'>
				    <p>Xin chào {fullName},</p>
				    <p>Bạn đã được mời tham gia công ty <strong>{companyName}</strong> trên nền tảng QuindLand.</p>
				    <p>Vui lòng nhấn vào nút bên dưới để chấp nhận lời mời (lời mời sẽ hết hạn sau {validHours} giờ):</p>
				    <a href='{inviteLink}' class='button'>Chấp nhận lời mời</a>
				    <p>Nếu bạn không yêu cầu hoặc không quen biết người mời, bạn có thể bỏ qua email này.</p>
				    <p>Trân trọng,<br>Đội ngũ QuindLand</p>
			    </div>
			    <div class='footer'>
				    <p>Đây là email tự động, vui lòng không trả lời. Cần hỗ trợ, liên hệ <a href='mailto:<EMAIL>'><EMAIL></a></p>
			    </div>
		    </div>
	    </body>
	    </html>";
		}
	}
}
