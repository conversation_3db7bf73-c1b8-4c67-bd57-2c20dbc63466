﻿namespace BusinessObjects.DTOs
{
    public class ValidationResultDTO
    {
        public bool isValid { get; set; }
        public string message { get; set; }

        public static ValidationResultDTO Success() => new ValidationResultDTO { isValid = true };
        public static ValidationResultDTO Fail(string message) => new ValidationResultDTO { isValid = false, message = message };
    }
    public class ValidationResultDTO<T> : ValidationResultDTO
    {
        public T data { get; set; }
        public static ValidationResultDTO<T> Success(T data) => new ValidationResultDTO<T> { isValid = true, data = data };
        public static ValidationResultDTO<T> Fail(string message) => new ValidationResultDTO<T> { isValid = false, message = message, data = default(T) };
    }
}
