﻿
using BusinessObjects.DTOs.CollectionDTOs;
using BusinessObjects.Models;
using BusinessObjects.Tools;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using MongoDB.Bson;
using Repositories.Interfaces;
using Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text;
using Twilio.Rest.Api.V2010.Account;

namespace Services.Implements
{
    public class CollectionPropertyService : ICollectionPropertyService
    {
        private readonly ICollectionPropertyRepository _collectionPropertyRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICurrentUserService _currentUserService;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public CollectionPropertyService(ICollectionPropertyRepository collectionPropertyRepository, IUnitOfWork unitOfWork, ICurrentUserService currentUserService, IHttpContextAccessor httpContextAccessor)
        {
            _collectionPropertyRepository = collectionPropertyRepository;
            _unitOfWork = unitOfWork;
            _currentUserService = currentUserService;
            _httpContextAccessor = httpContextAccessor;
        }
        // Trong CollectionPropertyService
        public async Task<CollectionProperty> AddPropertyToCollection(string collectionId, string propertyId)
        {
           
            var prop = await _unitOfWork.Properties.GetByIdAsync(propertyId.ToString());
            if (prop == null)
            {
                throw new KeyNotFoundException($"Không tìm thấy Property với ID: {propertyId}.");
            }

            var collection = await _collectionPropertyRepository.FindExistCollectionProperty(collectionId);
            if (collection == null)
            {
                throw new KeyNotFoundException($"Không tìm thấy Collection với ID: {collectionId}.");
            }
            var (userId, role) = _currentUserService.GetCurrentUser();
            if (userId != collection.userId)
            {
                throw new UnauthorizedAccessException("Chỉ người dùng sở hữu Collection mới có thể thêm Property vào Collection.");
            }

                if (collection.listProperties.Any(p => p == prop.id.ToString()))
            {
                throw new InvalidOperationException("Property đã tồn tại trong Collection.");
            }

            collection.listProperties.Add(prop.id.ToString());
            await _unitOfWork.CollectionProperties.UpdateAsync(collectionId, collection);
            await _unitOfWork.SaveAsync();
            await AutoUpdateCollectionImagesAsync(collectionId);
            return collection;
        }



        public async Task<CollectionProperty> CreateCollectionAsync(CollectionCreateRequest createRequest)
        {
            var (userId, role) = _currentUserService.GetCurrentUser();
            if (string.IsNullOrWhiteSpace(role) || role != "User")
            {
                throw new UnauthorizedAccessException("Chỉ người dùng đã đăng nhập mới có thể tạo Collection.");
            }
            if (string.IsNullOrWhiteSpace(createRequest.name))
            {
                throw new ArgumentException("Tên Collection không thể để trống.", nameof(createRequest.name));
            }
            if (createRequest.name.Length > 25)
            {
                throw new ArgumentException("Tên Collection không được vượt quá 25 ký tự.", nameof(createRequest.name));
            }
            var collectionProperty = new CollectionProperty
            {

                collectionName = createRequest.name,
                userId = userId,
                description = createRequest.description ?? string.Empty,
            };
            await _unitOfWork.CollectionProperties.AddAsync(collectionProperty);
            await _unitOfWork.SaveAsync();
            return collectionProperty;

        }

        public async Task<CollectionProperty> DeleteCollectionAsync(string id)
        {
            await GetCollectionByIdAsync(id);
            return await _unitOfWork.CollectionProperties.DeleteAsync(id);
        }

        public async Task<List<CollectionGetResponse>> GetAllCollectionsAsync()
        {
            // Lấy tất cả collections
            var collections = await _unitOfWork.CollectionProperties.GetAllAsync();
            if (collections == null || !collections.Any())
            {
                throw new KeyNotFoundException("Không tìm thấy bất kỳ Collection nào.");
            }

            // Thu thập tất cả các ID Property duy nhất
            var allPropertyIds = collections
                .SelectMany(c => c.listProperties ?? new List<string>())
                .Distinct()
                .ToList();

            // Lấy tất cả Properties trong một truy vấn
            IEnumerable<Property> allProperties;
            try
            {
                allProperties = await _unitOfWork.Properties.GetByIdsAsync(allPropertyIds);
            }
            catch (NotImplementedException)
            {
                // Fallback: Lấy từng Property bằng GetByIdAsync
                var propertyList = new List<Property>();
                foreach (var propertyId in allPropertyIds)
                {
                    var property = await _unitOfWork.Properties.GetByIdAsync(propertyId);
                    if (property != null)
                    {
                        propertyList.Add(property);
                    }
                }
                allProperties = propertyList;
            }

            // Tạo dictionary để tra cứu nhanh
            var propertyDict = allProperties.ToDictionary(p => p.id.ToString(), p => p);

            // Ánh xạ sang CollectionGetResponse
            var collectionResponses = collections.Select(c => new CollectionGetResponse
            {
                collectionId = c.id.ToString(),
                collectionName = c.collectionName,
                description = c.description,
                userId = c.userId,
                collectionImage = c.collectionImage ?? new List<string>(), // Đảm bảo không null
                listProperties = (c.listProperties ?? new List<string>())
                    .Select(p => propertyDict.TryGetValue(p, out var prop) ? prop?.ToPropertyResponseDTO() : null)
                    .Where(p => p != null)
                    .ToList()
            }).ToList();

            return collectionResponses;
        }

        public async Task<CollectionGetResponse> GetCollectionByIdAsync(string id)
        {
            // Kiểm tra ID đầu vào
            if (string.IsNullOrWhiteSpace(id))
            {
                throw new ArgumentException("ID không thể để trống hoặc chỉ chứa khoảng trắng.");
            }

            if (!ObjectId.TryParse(id, out _))
            {
                throw new ArgumentException("ID không hợp lệ, phải là định dạng ObjectId.");
            }

            // Lấy collection
            var collection = await _unitOfWork.CollectionProperties.GetByIdAsync(id);
            if (collection == null)
            {
                throw new KeyNotFoundException($"Không tìm thấy Collection với ID: {id}.");
            }

            // Thu thập tất cả các ID Property
            var propertyIds = (collection.listProperties ?? new List<string>())
                .Distinct()
                .Where(pid => ObjectId.TryParse(pid, out _)) // Lọc ID hợp lệ
                .ToList();

            // Lấy tất cả Properties trong một truy vấn
            IEnumerable<Property> allProperties;
            try
            {
                allProperties = await _unitOfWork.Properties.GetByIdsAsync(propertyIds);
            }
            catch (NotImplementedException)
            {
                // Fallback: Lấy từng Property bằng GetByIdAsync
                var propertyList = new List<Property>();
                foreach (var propertyId in propertyIds)
                {
                    var property = await _unitOfWork.Properties.GetByIdAsync(propertyId);
                    if (property != null)
                    {
                        propertyList.Add(property);
                    }
                }
                allProperties = propertyList;
            }

            // Tạo dictionary để tra cứu nhanh
            var propertyDict = allProperties.ToDictionary(p => p.id.ToString(), p => p);

            // Ánh xạ sang CollectionGetResponse
            var collectionResponse = new CollectionGetResponse
            {
                collectionId = collection.id.ToString(),
                collectionName = collection.collectionName,
                description = collection.description,
                userId = collection.userId,
                collectionImage = collection.collectionImage ?? new List<string>(), // Đảm bảo không null
                listProperties = (collection.listProperties ?? new List<string>())
                    .Where(p => propertyDict.ContainsKey(p)) // Chỉ lấy Property hợp lệ
                    .Select(p => propertyDict[p].ToPropertyResponseDTO())
                    .ToList()
            };

            return collectionResponse;
        }

        public async Task<List<CollectionGetResponse>> GetCollectionByUserIdAsync()
        {
            // Lấy userId của người dùng hiện tại
            var (userId, _) = _currentUserService.GetCurrentUser();
            if (string.IsNullOrWhiteSpace(userId))
            {
                throw new UnauthorizedAccessException("Chỉ người dùng đã đăng nhập mới có thể lấy Collection.");
            }

            // Lấy tất cả collections của userId
            var collections = await _unitOfWork.CollectionProperties.GetAllByUserIdAsync(userId);
            if (collections == null || !collections.Any())
            {
                throw new KeyNotFoundException($"Không tìm thấy Collection nào cho người dùng với ID: {userId}.");
            }

            // Thu thập tất cả các ID Property duy nhất
            var allPropertyIds = collections
                .SelectMany(c => c.listProperties ?? new List<string>())
                .Distinct()
                .ToList();

            // Kiểm tra ID hợp lệ
            var validPropertyIds = allPropertyIds
                .Where(id => ObjectId.TryParse(id, out _))
                .ToList();

            // Lấy tất cả Properties trong một truy vấn
            IEnumerable<Property> allProperties;
            try
            {
                allProperties = await _unitOfWork.Properties.GetByIdsAsync(validPropertyIds);
            }
            catch (NotImplementedException)
            {
                // Fallback: Lấy từng Property bằng GetByIdAsync
                var propertyList = new List<Property>();
                foreach (var propertyId in validPropertyIds)
                {
                    var property = await _unitOfWork.Properties.GetByIdAsync(propertyId);
                    if (property != null)
                    {
                        propertyList.Add(property);
                    }
                }
                allProperties = propertyList;
            }

            // Tạo dictionary để tra cứu nhanh
            var propertyDict = allProperties.ToDictionary(p => p.id.ToString(), p => p);

            // Ánh xạ sang CollectionGetResponse
            var collectionResponses = collections.Select(c => new CollectionGetResponse
            {
                collectionId = c.id.ToString(),
                collectionName = c.collectionName,
                description = c.description,
                userId = c.userId,
                collectionImage = c.collectionImage ?? new List<string>(), // Đảm bảo không null
                listProperties = (c.listProperties ?? new List<string>())
                    .Where(p => propertyDict.ContainsKey(p)) // Chỉ lấy Property hợp lệ
                    .Select(p => propertyDict[p].ToPropertyResponseDTO())
                    .ToList()
            }).ToList();

            return collectionResponses;
        }
        public async Task<CollectionProperty> RemovePropertyFromCollection(string collectionId, string propertyId)
        {


            var collection = await _unitOfWork.CollectionProperties.GetByIdAsync(collectionId);
            if (collection == null)
            {
                throw new KeyNotFoundException($"Không tìm thấy Collection với ID: {collectionId}.");
            }
            var (userId, role) = _currentUserService.GetCurrentUser();
            if (userId != collection.userId)
            {
                throw new UnauthorizedAccessException("Chỉ người dùng sở hữu Collection mới có thể xóa Property.");
            }

            // Check if listProperties is null or empty
            if (collection.listProperties == null || !collection.listProperties.Any())
            {
                throw new InvalidOperationException("Collection không có Properties để xóa.");
            }

            // Find and remove the property
            var propertyToRemove = collection.listProperties.FirstOrDefault(p => p == propertyId);
            if (propertyToRemove == null)
            {
                throw new KeyNotFoundException($"Không tìm thấy Property với ID: {propertyId} trong Collection.");
            }

            collection.listProperties.Remove(propertyToRemove);

            await _unitOfWork.CollectionProperties.UpdateAsync(collectionId, collection);
            await _unitOfWork.SaveAsync();
            await AutoUpdateCollectionImagesAsync(collectionId);
            return collection;
        }

        public async Task<CollectionProperty> UpdateCollectionAsync(string id, CollectionUpdateRequest updateCollection)
        {
            var sellerId = _httpContextAccessor.HttpContext?.User?.FindFirstValue("UserID");
            var role = _httpContextAccessor.HttpContext?.User?.FindFirst(ClaimTypes.Role)?.Value;
            var foundCollection = await _unitOfWork.CollectionProperties.GetByIdAsync(id);
            if (foundCollection == null)
            {
                throw new Exception("Collection not found");
            }
            foundCollection.collectionName = updateCollection.name ?? foundCollection.collectionName;
            foundCollection.description = updateCollection.description ?? foundCollection.description;
            await _unitOfWork.CollectionProperties.UpdateAsync(id, foundCollection);
            return foundCollection;
        }

        private async Task AutoUpdateCollectionImagesAsync(string collectionId)
        {
            var collection = await _collectionPropertyRepository.FindExistCollectionProperty(collectionId);
            if (collection == null || collection.listProperties == null) return;

            var firstFourPropertyIds = collection.listProperties.Take(4).ToList();
            var properties = await _unitOfWork.Properties.GetByIdsAsync(firstFourPropertyIds);

            var images = properties
                .Where(p => p.images != null && p.images.Any())
                .Select(p => p.images.First())
                .Take(4)
                .ToList();

            if (images.Any())
            {
                collection.collectionImage = images;
                await _unitOfWork.CollectionProperties.UpdateAsync(collectionId, collection);
                await _unitOfWork.SaveAsync();
            }
        }
    }
}
