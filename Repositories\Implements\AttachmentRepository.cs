﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using MongoDB.Driver;
using Repositories.Interfaces;

namespace Repositories.Implements
{
    public class AttachmentRepository : GenericRepository<Attachment>, IAttachmentRepository
    {
        private readonly IMongoCollection<Attachment> _attachments;

        public AttachmentRepository(RevoLandDbContext context) : base(context, "Attachments")
        {
            _attachments = context.GetCollection<Attachment>("Attachments");
        }
    }
}
