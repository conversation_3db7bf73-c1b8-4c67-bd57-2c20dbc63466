﻿using BusinessObjects.Models;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.DTOs.CollectionDTOs
{
    public class CollectionGetResponse
    {
        public string collectionId { get; set; }

        [BsonElement("collectionProperties")]
        public List<PropertyResponseDTO> listProperties { get; set; } = new List<PropertyResponseDTO>();
        [BsonElement("collectionImage")]
        public List<string> collectionImage { get; set; } = new List<string>();
        public string? collectionName { get; set; }
        public string description { get; set; }
        public string userId { get; set; }
    }
}
