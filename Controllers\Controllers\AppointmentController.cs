﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.AppoinmentDTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Services.Interfaces;


namespace Controllers.Controllers
{
    [Route("api/appointments")]
    [ApiController]
    [Authorize]
    public class AppointmentController : ControllerBase
    {
        private readonly IAppointmentService _appointmentService;
        private readonly ILogger<AppointmentController> _logger;

        public AppointmentController(IAppointmentService appointmentService, ILogger<AppointmentController> logger)
        {
            _appointmentService = appointmentService;
            _logger = logger;

        }

        private ApiResponse CreateResponse(int code, bool status, string message, object data = null)
        {
            return new ApiResponse { code = code, status = status, message = message, data = data };
        }

        /// <summary>
        /// Get all appointments (for Admin)
        /// </summary>
        [HttpGet()]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> GetAllAppointments()
        {
            try
            {
                var appointments = await _appointmentService.GetAppointmentsAsync();
                return Ok(CreateResponse(200, true, "<PERSON><PERSON>y tất cả lịch hẹn thành công", appointments));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(500, false, ex.Message));
            }
        }

        /// <summary>
        /// Delete appointment by id (for Admin and User who book this appointment)
        /// </summary> 
        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin,User")]
        public async Task<IActionResult> DeleteAppointment(string id)
        {
            try
            {
                await _appointmentService.DeleteAppointmentAsync(id);
                return Ok(CreateResponse(200, true, "Xóa lịch hẹn thành công"));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(500, false, ex.Message));
            }
        }

        /// <summary>
        /// Create a new appointment (for User)
        /// </summary>
        [HttpPost]
        [Authorize(Roles = "User")]
        public async Task<IActionResult> AddAppointment(AppointmentCreateRequest request)
        {
            try
            {
                var response = await _appointmentService.AddAppointmentAsync(request);
                return Ok(CreateResponse(201, true, "Tạo lịch hẹn thành công", response));
            }
            catch (Exception ex)
            {
                return Ok(CreateResponse(500, false, ex.Message));
            }
        }



        /// <summary>
        /// Cancel an appointment with optional cancellation details
        /// </summary>
        /// <param name="id">Appointment ID</param>
        /// <param name="request">Cancellation request details</param>
        /// <returns>Cancellation result</returns>
        [HttpPatch("{id}/cancel")]
        public async Task<IActionResult> CancelAppointment(
        [FromRoute] string id,
        [FromBody] CancelAppointmentRequest? request = null)
        {
            try
            {
                _logger.LogInformation($"Cancelling appointment {id} with reason: {request.cancellationReason}");
                await _appointmentService.CancelAppointmentAsync(
                    id: id,
                    cancellationReason: request.cancellationReason,
                    cancellationType: request.cancellationType,
                    notes: request.notes,
                    relatedTicketId: request.relatedTicketId
                );

                var responseData = new
                {
                    appointmentId = id,
                    cancelledAt = DateTime.UtcNow,
                    calendarEventDeletionStatus = "Processing", // Background task
                    cancellationDetails = new
                    {
                        reason = request.cancellationReason ?? "Không có lý do được cung cấp",
                        type = request.cancellationType?.ToString(),
                        notes = request.notes,
                        relatedTicketId = request.relatedTicketId
                    }
                };

                _logger.LogInformation($"Appointment {id} cancelled successfully via API");

                return Ok(CreateResponse(
                    code: 200,
                    status: true,
                    message: "Hủy lịch hẹn thành công",
                    data: responseData
                ));
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning($"Unauthorized cancellation attempt for appointment {id}: {ex.Message}");
                return StatusCode(403, CreateResponse(
                    code: 403,
                    status: false,
                    message: "Bạn không có quyền hủy lịch hẹn này"
                ));
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning($"Invalid argument for appointment {id}: {ex.Message}");
                return BadRequest(CreateResponse(
                    code: 400,
                    status: false,
                    message: ex.Message
                ));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning($" Invalid operation for appointment {id}: {ex.Message}");
                return BadRequest(CreateResponse(
                    code: 400,
                    status: false,
                    message: ex.Message
                ));
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning($" Appointment {id} not found");
                return NotFound(CreateResponse(
                    code: 404,
                    status: false,
                    message: ex.Message
                ));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $" Unexpected error cancelling appointment {id}");
                return StatusCode(500, CreateResponse(
                    code: 500,
                    status: false,
                    message: ex.Message
                ));
            }
        }

        /// <summary>
        /// Get cancellation details for an appointment
        /// </summary>
        /// <param name="id">Appointment ID</param>
        /// <returns>Cancellation information</returns>
        [HttpGet("{id}/cancellation")]
        public async Task<IActionResult> GetCancellationDetails([FromRoute] string id)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(id))
                {
                    return BadRequest(CreateResponse(
                        code: 400,
                        status: false,
                        message: "ID lịch hẹn là bắt buộc"
                    ));
                }

                var appointment = await _appointmentService.GetAppointmentByIdAsync(id);
                if (appointment == null)
                {
                    return NotFound(CreateResponse(
                        code: 404,
                        status: false,
                        message: "Không tìm thấy lịch hẹn"
                    ));
                }

                if (appointment.cancellationInfo == null)
                {
                    var notCancelledData = new
                    {
                        appointmentId = id,
                        isCancelled = false,
                        status = appointment.status.ToString()
                    };

                    return Ok(CreateResponse(
                        code: 200,
                        status: true,
                        message: "Lịch hẹn chưa bị hủy",
                        data: notCancelledData
                    ));
                }

                var cancellationData = new
                {
                    appointmentId = id,
                    isCancelled = true,
                    cancellationInfo = new
                    {
                        reason = appointment.cancellationInfo.reason,
                        cancelledAt = appointment.cancellationInfo.cancelledAt,
                        cancelledBy = appointment.cancellationInfo.cancelledBy,
                        type = appointment.cancellationInfo.type.ToString(),
                        notes = appointment.cancellationInfo.notes,
                        isAutomatic = appointment.cancellationInfo.isAutomatic,
                        relatedTicketId = appointment.cancellationInfo.relatedTicketId,
                        calendarDeletion = new
                        {
                            isDeleted = appointment.cancellationInfo.calendarDeletion.isDeleted,
                            deletedAt = appointment.cancellationInfo.calendarDeletion.deletedAt,
                            userEventDeleted = appointment.cancellationInfo.calendarDeletion.userEventDeleted,
                            salerEventDeleted = appointment.cancellationInfo.calendarDeletion.salerEventDeleted,
                            deletionStatus = appointment.cancellationInfo.calendarDeletion.deletionStatus,
                            failureReason = appointment.cancellationInfo.calendarDeletion.failureReason,
                            retryCount = appointment.cancellationInfo.calendarDeletion.retryCount
                        }
                    }
                };

                return Ok(CreateResponse(
                    code: 200,
                    status: true,
                    message: "Lấy thông tin hủy lịch hẹn thành công",
                    data: cancellationData
                ));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting cancellation details for appointment {id}");
                return StatusCode(500, CreateResponse(
                    code: 500,
                    status: false,
                    message: "Đã xảy ra lỗi khi lấy thông tin hủy lịch hẹn"
                ));
            }
        }

        /// <summary>
        /// Get an appointment by id (for Admin, User who book this appointment, and Saler who was assigned)
        /// </summary>
        [HttpGet("{id}")]
        [Authorize(Roles = "Admin,User,Saler")]
        public async Task<IActionResult> GetAppointmentById(string id)
        {
            try
            {
                var appointment = await _appointmentService.GetAppointmentByIdAsync(id);
                return Ok(CreateResponse(200, true, "Lấy lịch hẹn thành công", appointment));
            }
            catch (Exception ex)
            {
                return Ok(CreateResponse(500, false, ex.Message, null));
            }
        }

        /// <summary>
        /// Get all appointments by user id with optional status filter (for leads and seller)
        /// </summary>
        [HttpGet("user")]
        [Authorize(Roles = "User,Saler")]
        public async Task<IActionResult> GetAppointmentByUserId([FromQuery] string? status)
        {
            try
            {
                var appointments = await _appointmentService.GetAppointmentsByUserIdAsync(status);
                return Ok(CreateResponse(201, true, "Lấy lịch hẹn theo ID người dùng thành công", appointments));
            }
            catch (Exception ex)
            {
                return Ok(CreateResponse(500, false, ex.Message));
            }
        }

        /// <summary>
        /// Create a new message for an appointment (only for User and Saler)
        /// </summary>
        [HttpPost("{id}/messages")]
        [Authorize(Roles = "User,Saler")]
        public async Task<IActionResult> CreateMessageForAppointment(string id, AppointmentMessageCreateRequest request)
        {
            try
            {
                await _appointmentService.CreateMessageForAppointmentAsync(id, request);
                return Ok(CreateResponse(201, true, "Tạo tin nhắn cho lịch hẹn thành công"));
            }
            catch (Exception ex)
            {
                return Ok(CreateResponse(500, false, ex.Message));
            }

        }

        /// <summary>
        /// Reschedule an appointment by id (for User and Saler)    
        /// </summary>
        [HttpPatch("{id}/reschedule")]
        [Authorize(Roles = "User,Saler")]
        public async Task<IActionResult> RescheduleAppointment(string id, RescheduleAppointmentRequest request)
        {
            try
            {
                await _appointmentService.RescheduleAppointmentAsync(id, request.date);
                return Ok(CreateResponse(201, true, "Đổi lịch hẹn thành công"));
            }
            catch (Exception ex)
            {
                return Ok(CreateResponse(500, false, ex.Message));
            }
        }



        /// <summary>
        /// Assign a seller to an appointment (for Admin)
        /// </summary>
        [HttpPatch("{id}/assign-seller")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> AssignSeller(string id, [FromBody] AssignSellerToAppointmentRequest request)
        {
            try
            {
                var result = await _appointmentService.AssignSellerToAppointmentAsyncs(id, request);

                if (result.isSuccess)
                {
                    return Ok(CreateResponse(201, true, "Phân công cho lịch hẹn thành công"));
                }
                else
                {
                    return BadRequest(CreateResponse(400, false, result.message));
                }
            }
            catch (InvalidDataException ex)
            {
                return BadRequest(CreateResponse(
                    code: 400,
                    status: false,
                    message: ex.Message
                ));
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(CreateResponse(
                    code: 404,
                    status: false,
                    message: ex.Message
                ));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateResponse(
                    code: 500,
                    status: false,
                    message: ex.Message
                ));
            }
        }


        /// <summary>
        /// re-assign a seller to an appointment (for Admin)
        /// </summary>
        [HttpPatch("{id}/reassign-seller")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> AssignSeller(string id, [FromBody] ReassignSellerRequest request)
        {
            try
            {
                var result = await _appointmentService.ReassignSellerToAppointmentAsync(id, request);

                if (result.isSuccess)
                {
                    return Ok(CreateResponse(201, true, "Phân công lại cho lịch hẹn thành công"));
                }
                else
                {
                    return BadRequest(CreateResponse(400, false, result.message));
                }
            }
            catch (InvalidDataException ex)
            {
                return BadRequest(CreateResponse(
                    code: 400,
                    status: false,
                    message: ex.Message
                ));
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(CreateResponse(
                    code: 404,
                    status: false,
                    message: ex.Message
                ));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateResponse(
                    code: 500,
                    status: false,
                    message: ex.Message
                ));
            }
        }
    }
}
