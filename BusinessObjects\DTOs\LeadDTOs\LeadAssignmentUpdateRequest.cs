﻿using BusinessObjects.Models;
using BusinessObjects.Validations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.DTOs.LeadDTOs
{
    public class LeadAssignmentUpdateRequest
    {
        [ValidObjectId(ErrorMessage = "AssignedTo must be a valid ObjectId")]
        public string? assignedTo { get; set; }
        public LeadScore? score { get; set; }
    }
}
