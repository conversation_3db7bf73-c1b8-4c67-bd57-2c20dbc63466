using BusinessObjects.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace BusinessObjects.DTOs
{
    public class ContractDTO
    {
        public string id { get; set; }

        // Thuộc tính từ Property
        public PropertyDetails Property { get; set; } = new PropertyDetails();

        public string tenantId { get; set; }

        public string buyerId { get; set; }

        public ContractType contractType { get; set; }

        public DateTime? contractStartDate { get; set; }

        public DateTime? contractEndDate { get; set; }

        public PaymentTerms? paymentTerms { get; set; }

        public ContractStatus status { get; set; } = ContractStatus.Pending;

        public double? recurringRent { get; set; }
       
        public double? depositAmount { get; set; }

        public List<string> pdfContractUrls { get; set; } = new List<string>();
        public List<PaymentScheduleItemDTO>? paymentSchedule { get; set; }

        public List<RenewalLogDTO>? renewalLogs { get; set; }

        public DateTime? rentDueDate { get; set; }

        public string? content { get; set; }

    }

    public class PropertyDetails
    {
        [Required]
        public string Id { get; set; } = string.Empty;
        public string ownerId { get; set; }
        public string salerId { get; set; }
        public string Name { get; set; } = string.Empty;
        public PropertyTransactionType TransactionType { get; set; }
        public PropertyType Type { get; set; }
        public PropertyStatus Status { get; set; }
        public string Code { get; set; } = string.Empty;
        public PriceDetail PriceDetails { get; set; }
        public string Image { get; set; } = string.Empty;
        public int YearBuilt { get; set; } = 0;
        public List<string> LegalDocuments { get; set; } = new List<string>();
    }

    public class RenewalLogDTO
    {
        [Required]
        public DateTime previousEndDate { get; set; }

        [Required]
        public DateTime newEndDate { get; set; }

        public DateTime renewalDate { get; set; }

        public string renewalNote { get; set; }
    }

    public class CreateRentalContractDTO
    {
        [Required]
        public string propertyId { get; set; }

        [Required]
        public string tenantId { get; set; }

        [Required]
        public DateTime contractStartDate { get; set; }

        [Required]
        public DateTime contractEndDate { get; set; }

        [Required]
        public PaymentTerms paymentTerms { get; set; }

        [Required]
        public double curringRent { get; set; }
        
        [Required]
        public DateTime? rentDueDate { get; set; }

        [Required]
        public double depositAmount { get; set; }
        public List<string>? pdfContractUrls { get; set; }

        [Required]
        public string content { get; set; }
    }
    public class CreateSaleContractDTO {

        [Required]
        public string propertyId { get; set; }
        [Required]
        public string buyerId { get; set; }
        [Required]
        public List<PaymentScheduleItemDTO> paymentSchedule { get; set; }
        public List<string>? pdfContractUrls { get; set; }
        [Required]
        public string content { get; set; }
    }

    public class UpdateRentalContractDTO
    {
        public DateTime? contractEndDate { get; set; }
        public PaymentTerms? paymentTerms { get; set; }
        public ContractStatus? status { get; set; }
        public double? monthlyRent { get; set; }
        public string? content { get; set; }
	}

	public class UpdateSaleContractDTO
	{
		public ContractStatus? status { get; set; }
		public List<PaymentScheduleItemDTO>? paymentSchedule { get; set; }
		public string? content { get; set; }
	}

	public class RenewContractDTO
    {
        public DateTime newEndDate { get; set; }
        
        public string renewalNote { get; set; }
    }
    public class PaymentScheduleItemDTO
    {
        [Required]
        public DateTime paymentDate { get; set; }
        [Required]
        [Range(0.1, double.MaxValue)]
        public double amount { get; set; }
        public string? note { get; set; }
    }

    public class SendContractByEmailDTO
    {
        [Required]
        public string email { get; set; }
        
        [Required]
        public string contractLink { get; set; }
    }
} 