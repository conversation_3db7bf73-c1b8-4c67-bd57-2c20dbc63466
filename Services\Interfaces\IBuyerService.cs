﻿using BusinessObjects.DTOs.BuyerDTOs;
using BusinessObjects.Models;
using BusinessObjects.QueryObject;
using Microsoft.AspNetCore.Http;

namespace Services.Interfaces
{
	public interface IBuyerService
	{
		Task<IEnumerable<Buyer>> GetAllBuyersAsync();
		Task<Buyer> GetBuyerByIdAsync(string id);
		Task<Buyer> GetBuyerByPhoneAsync(string phone);
		Task CreateUserFromBuyerAsync(BuyerCreateRequest buyer);
		Task<bool> UpdateBuyerAsync(string id, BuyerUpdateRequest buyerRequest);
		Task<Buyer> DeleteBuyerAsync(string id);
		Task<List<Buyer>> GetAllBuyersAsync(QueryBuyer query);
		Task<Buyer> AddBuyerAsync(BuyerCreateRequest buyer);
		Task<Buyer> GetBuyerByEmailAsync(string email);
	}
}
