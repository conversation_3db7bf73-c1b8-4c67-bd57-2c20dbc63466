﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.DealDTOs;
using BusinessObjects.Models;
using BusinessObjects.QueryObject;
using DnsClient.Internal;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using MongoDB.Driver;
using Repositories.Interfaces;
using Services.Helpers;
using Services.Interfaces;
using Services.Tools;
using System.ComponentModel;
using System.Security.Claims;
using static BusinessObjects.Models.Deal;

namespace Services.Implements
{
    public class DealService : IDealService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ICurrentUserService _currentUserService;
        private readonly IAuthorizationHelper _authorizationHelper;
        private readonly ILogger<DealService> _logger;
        private readonly Dictionary<DealStatus, List<DealStatus>> allowedTransitions = new Dictionary<DealStatus, List<DealStatus>>
{
    { DealStatus.New, new List<DealStatus> { DealStatus.Contacted, DealStatus.Lost } },
    { DealStatus.Contacted, new List<DealStatus> { DealStatus.Negotiation, DealStatus.Lost } },
    { DealStatus.Negotiation, new List<DealStatus> { DealStatus.Closing, DealStatus.Lost } },
    { DealStatus.Closing, new List<DealStatus> { DealStatus.Won, DealStatus.Lost } },
    { DealStatus.Won, new List<DealStatus>() }, // Không có chuyển tiếp
    { DealStatus.Lost, new List<DealStatus>() } // Không có chuyển tiếp
};
        public DealService(IUnitOfWork unitOfWork, IDealRepository dealRepository, IHttpContextAccessor httpContextAccessor, ILogger<DealService> logger, ICurrentUserService currentUserService, IAuthorizationHelper authorizationHelper)
        {
            _unitOfWork = unitOfWork;
            _httpContextAccessor = httpContextAccessor;
            _logger = logger;
            _currentUserService = currentUserService;
            _authorizationHelper = authorizationHelper;
        }

        public async Task<DealResponse> AddDealByCurrentUserAsync(DealCreateRequest request)
        {
            var currentUserId = _httpContextAccessor.HttpContext?.User?.FindFirstValue("UserID");
            if (string.IsNullOrEmpty(currentUserId))
                throw new InvalidDataException("Current user ID not found.");

            var currentUser = await _unitOfWork.Users.GetByIdAsync(currentUserId);
            if (currentUser == null)
                throw new InvalidDataException("Current user not found.");

            if (currentUser.role == UserRole.Admin)
                return await AddDealAsync(request);
            else if (currentUser.role == UserRole.Saler)
                return await AddDealByCurrentSalerAsync(request, currentUserId);
            else
                throw new UnauthorizedAccessException("You do not have permission to access this deal.");


        }

        public async Task<DealResponse> AddDealAsync(DealCreateRequest deal)
        {
            var validationResult = await IsValidAsync((IDealBaseRequest)deal);
            if (!validationResult.isValid)
            {
                throw new ArgumentException(validationResult.message);
            }
            Deal mappedDeal = CreateToDeal(deal);

            // Kick off both operations in parallel
            Task<Deal> dealTask = _unitOfWork.Deals.AddAsync(mappedDeal);
            Task<Lead> leadTask = _unitOfWork.Leads.GetByIdAsync(deal.leadId);
            Task<User> salerTask = _unitOfWork.Users.GetByIdAsync(mappedDeal.salesRepId);

            // Wait for both to finish
            await Task.WhenAll(dealTask, leadTask);

            // Persist changes (e.g. storing the new deal)
            await _unitOfWork.SaveAsync();

            // Retrieve the actual results
            Deal createdDeal = dealTask.Result;
            Lead customer = leadTask.Result;
            User saler = salerTask.Result ?? throw new InvalidDataException("The sales representative of this deal currently not found");

            // Map to your response DTO
            return Mapper.ToDealResponseDto(createdDeal, customer, saler);
        }

        public async Task<DealResponse> AddDealByCurrentSalerAsync(DealCreateRequest request, string currentUserId)
        {

            if (!ObjectId.TryParse(currentUserId, out ObjectId currentUserIdObject))
            {
                throw new InvalidOperationException("Invalid user ID format.");
            }
            //Kiểm tra xem lead có tồn tại và được assign cho current sale rep ko
            await ValidateLeadAccess(currentUserId, request.leadId);
            //parse priority
            if (!Enum.TryParse<Deal.DealPriority>(request.priority, ignoreCase: true, out var parsedPriority))
            {
                throw new InvalidEnumArgumentException("Invalid deal priority enum");
            }
            Deal mappedDeal = new() { leadId = request.leadId, salesRepId = currentUserId, title = request.title, description = request.description, priority = parsedPriority };

            // Kick off both operations in parallel
            Task<Deal> dealTask = _unitOfWork.Deals.AddAsync(mappedDeal);
            Task<Lead> leadTask = _unitOfWork.Leads.GetByIdAsync(mappedDeal.leadId);
            Task<User> salerTask = _unitOfWork.Users.GetByIdAsync(currentUserId);

            // Wait for both to finish
            await Task.WhenAll(dealTask, leadTask);
            await _unitOfWork.SaveAsync();

            // Retrieve the actual results
            Deal createdDeal = dealTask.Result;
            Lead customer = leadTask.Result;
            User saler = salerTask.Result ?? throw new InvalidDataException("The sales representative of this deal currently not found");
            return Mapper.ToDealResponseDto(createdDeal, customer, saler);
        }

        public async Task<IEnumerable<DealResponse>> TakeAllDealsAsync()
        {
            IEnumerable<Deal> deals = await _unitOfWork.Deals.GetAllAsync();
            if (deals == null || !deals.Any())
            {
                throw new InvalidDataException("No deals found.");
            }
            List<DealResponse> result = new List<DealResponse>();

            foreach (var deal in deals)
            {
                Lead customer = await _unitOfWork.Leads.GetByIdAsync(deal.leadId) ?? throw new InvalidDataException($"Lead with ID '{deal.leadId}' does not exist.");
                User? saler = await _unitOfWork.Users.GetByIdAsync(deal.salesRepId) ?? throw new InvalidDataException($"Sales representative with ID '{deal.salesRepId}' does not exist.");
                DealResponse dealResponse = Mapper.ToDealResponseDto(deal, customer, saler);
                result.Add(dealResponse);
            }
            return result;
        }

        /// <summary>
        /// Builds a filter for deals based on query parameters and user role
        /// </summary>
        /// <param name="query">Query parameters for filtering</param>
        /// <param name="salesRepId">Optional sales rep ID for filtering deals by sales rep</param>
        /// <returns>MongoDB filter definition</returns>
        private static FilterDefinition<Deal> BuildDealFilter(QueryDeal query, string? salesRepId = null)
        {
            var builder = Builders<Deal>.Filter;
            var combinedFilter = builder.Empty;

            // Base filter to exclude deleted deals (only show active deals)
            combinedFilter &= builder.Or(
                builder.Eq(d => d.isDeleted, false),
                builder.Exists(d => d.isDeleted, false)); // Handle cases where isDeleted field doesn't exist

            // Base filter for sales rep (if provided)
            if (!string.IsNullOrEmpty(salesRepId))
            {
                combinedFilter &= builder.Eq(d => d.salesRepId, salesRepId);
            }
            else
            {
                //filter salesRep for Admin
                if (!string.IsNullOrEmpty(query.salesRepId))
                {
                    combinedFilter &= builder.Eq(d => d.salesRepId, query.salesRepId);
                }

            }

            // Search term filter (applies to both admin and sales rep)
            if (!string.IsNullOrEmpty(query.searchTerm))
            {
                var regex = new BsonRegularExpression(query.searchTerm, "i");
                combinedFilter &= builder.Or(
                    builder.Regex(d => d.leadId, regex),
                    builder.Regex(d => d.title, regex),
                    builder.Regex(d => d.salesRepId, regex)
                );
            }

            // Lead ID filter
            if (!string.IsNullOrEmpty(query.leadId))
            {
                combinedFilter &= builder.Eq(d => d.leadId, query.leadId);
            }

            // Date filters
            if (query.createdAt.HasValue)
            {
                combinedFilter &= builder.Gte(d => d.createdAt, query.createdAt.Value.Date);
            }

            if (query.updatedAt.HasValue)
            {
                combinedFilter &= builder.Gte(d => d.updatedAt, query.updatedAt.Value.Date);
            }

            // Created date part filters
            if (query.createdYear.HasValue)
            {
                combinedFilter &= builder.Eq(d => d.createdAt.Year, query.createdYear.Value);
            }
            if (query.createdMonth.HasValue)
            {
                combinedFilter &= builder.Eq(d => d.createdAt.Month, query.createdMonth.Value);
            }
            if (query.createdDay.HasValue)
            {
                combinedFilter &= builder.Eq(d => d.createdAt.Day, query.createdDay.Value);
            }

            // Updated date part filters
            if (query.updatedYear.HasValue)
            {
                combinedFilter &= builder.Eq(d => d.updatedAt.Year, query.updatedYear.Value);
            }
            if (query.updatedMonth.HasValue)
            {
                combinedFilter &= builder.Eq(d => d.updatedAt.Month, query.updatedMonth.Value);
            }
            if (query.updatedDay.HasValue)
            {
                combinedFilter &= builder.Eq(d => d.updatedAt.Day, query.updatedDay.Value);
            }

            // Status filter
            if (!string.IsNullOrEmpty(query.status))
            {
                if (!Enum.TryParse<Deal.DealStatus>(query.status, ignoreCase: true, out var parsedStatus))
                {
                    throw new ArgumentException("Invalid deal status enum");
                }
                combinedFilter &= builder.Eq(d => d.status, parsedStatus);
            }

            // Priority filter
            if (!string.IsNullOrEmpty(query.priority))
            {
                if (!Enum.TryParse<Deal.DealPriority>(query.priority, ignoreCase: true, out var parsedPriority))
                {
                    throw new ArgumentException("Invalid deal priority enum");
                }
                combinedFilter &= builder.Eq(d => d.priority, parsedPriority);
            }

            return combinedFilter;
        }

        /// <summary>
        /// Builds sort definition for deals based on query parameters
        /// </summary>
        /// <param name="query">Query parameters containing sort information</param>
        /// <returns>MongoDB sort definition</returns>
        private static SortDefinition<Deal> BuildDealSort(QueryDeal query)
        {
            return query.sortBy switch
            {
                QueryDeal.DealEnumSortBy.createdAt => query.isAscending ?? true
                    ? Builders<Deal>.Sort.Ascending(d => d.createdAt)
                    : Builders<Deal>.Sort.Descending(d => d.createdAt),
                QueryDeal.DealEnumSortBy.updatedAt => query.isAscending ?? true
                    ? Builders<Deal>.Sort.Ascending(d => d.updatedAt)
                    : Builders<Deal>.Sort.Descending(d => d.updatedAt),
                _ => Builders<Deal>.Sort.Ascending(d => d.createdAt)
            };
        }

        public async Task<PagedResult<DealResponse>> GetAllDealsByCurrentUserAsync(QueryDeal query)
        {
            var currentUserId = _httpContextAccessor.HttpContext?.User?.FindFirstValue("UserID");
            if (string.IsNullOrEmpty(currentUserId))
                throw new InvalidDataException("Current user ID not found.");

            var currentUser = await _unitOfWork.Users.GetByIdAsync(currentUserId);
            if (currentUser == null)
                throw new InvalidDataException("Current user not found.");

            if (currentUser.role == UserRole.Admin)
                return await GetAllDealsAsyncs(query);
            else if (currentUser.role == UserRole.Saler)
                return await GetAllDealsBySaleRepIdAsync(currentUserId, query);
            else
                throw new UnauthorizedAccessException("You do not have permission to access deals.");

        }

        /// <summary>
        /// Gets all deals with pagination (admin access)
        /// </summary>
        public async Task<PagedResult<DealResponse>> GetAllDealsAsyncs(QueryDeal query)
        {
            var filter = BuildDealFilter(query); // No salesRepId filter for admin
            var sort = BuildDealSort(query);

            int skip = (query.pageNumber - 1) * query.pageSize;
            int limit = query.pageSize;

            var getDealsTask = _unitOfWork.Deals.GetDealsByFilterAsync(filter, sort, skip, limit);
            var countDealsTask = _unitOfWork.Deals.CountDealsWithFilterAsync(filter);

            await Task.WhenAll(getDealsTask, countDealsTask);

            var dealsResponse = await MapToDealsResponse(getDealsTask.Result);

            return new PagedResult<DealResponse>
            {
                data = dealsResponse,
                totalCount = (int?)countDealsTask.Result, // Explicitly cast 'long' to 'int?'
                currentPage = query.pageNumber,
                pageSize = query.pageSize
            };
        }



        /// <summary>
        /// Gets all deals for a specific sales representative with pagination
        /// </summary>
        public async Task<PagedResult<DealResponse>> GetAllDealsBySaleRepIdAsync(string currentUserId, QueryDeal query)
        {
            await ValidateLeadAccess(currentUserId, query.leadId);

            var filter = BuildDealFilter(query, currentUserId); // Filter by salesRepId
            var sort = BuildDealSort(query);

            int skip = (query.pageNumber - 1) * query.pageSize;
            int limit = query.pageSize;

            var getDealsTask = _unitOfWork.Deals.GetDealsByFilterAsync(filter, sort, skip, limit);
            var countDealsTask = _unitOfWork.Deals.CountDealsWithFilterAsync(filter);

            await Task.WhenAll(getDealsTask, countDealsTask);

            var dealsResponse = await MapToDealsResponse(getDealsTask.Result);

            return new PagedResult<DealResponse>
            {
                data = dealsResponse,
                totalCount = (int?)countDealsTask.Result,
                currentPage = query.pageNumber,
                pageSize = query.pageSize
            };
        }

        // Retrieve a single Deal by ID
        public async Task<DealResponse> GetDealByIdAsync(string id)
        {
            var deal = await _unitOfWork.Deals.GetByIdAsync(id) ?? throw new InvalidDataException("Deal not found");
            var customer = await _unitOfWork.Leads.GetByIdAsync(deal.leadId) ?? throw new InvalidDataException("The lead of this deal currently not found");
            var saler = await _unitOfWork.Users.GetByIdAsync(deal.salesRepId);
            return Mapper.ToDealResponseDto(deal, customer, saler);
        }

        public async Task<DealResponse> GetDealByIdByCurrentUserAsync(string id)
        {
            var currentUserId = _httpContextAccessor.HttpContext?.User?.FindFirstValue("UserID");
            if (string.IsNullOrEmpty(currentUserId))
                throw new InvalidDataException("Current user ID not found.");

            var currentUser = await _unitOfWork.Users.GetByIdAsync(currentUserId);
            if (currentUser == null)
                throw new InvalidDataException("Current user not found.");

            if (currentUser.role == UserRole.Admin)
                return await GetDealByIdAsync(id);
            else if (currentUser.role == UserRole.Saler)
                return await GetDealByIdByCurrentSalerAsync(id, currentUserId);
            else
                throw new UnauthorizedAccessException("You do not have permission to access this deal.");

        }

        public async Task<DealResponse> GetDealByIdByCurrentSalerAsync(string id, string salesRepId)
        {
            var deal = await _unitOfWork.Deals.GetByIdAsync(id) ?? throw new InvalidDataException("Deal not found");
            if (deal.salesRepId != salesRepId)
            {
                throw new UnauthorizedAccessException("You do not have permission to access this deal.");
            }
            var customer = await _unitOfWork.Leads.GetByIdAsync(deal.leadId) ?? throw new InvalidDataException("The lead of this deal currently not found");
            var saler = await _unitOfWork.Users.GetByIdAsync(deal.salesRepId) ?? throw new InvalidDataException("The sales representative of this deal currently not found");
            return Mapper.ToDealResponseDto(deal, customer, saler);

        }

        public async Task<DealResponse> UpdateDealByCurrentUserAsync(string id, DealUpdateRequest request)
        {
            var currentUserId = _httpContextAccessor.HttpContext?.User?.FindFirstValue("UserID");
            if (string.IsNullOrEmpty(currentUserId))
                throw new InvalidDataException("Current user ID not found.");

            var currentUser = await _unitOfWork.Users.GetByIdAsync(currentUserId);
            if (currentUser == null)
                throw new InvalidDataException("Current user not found.");

            if (currentUser.role == UserRole.Admin)
                return await UpdateDealAsync(id, request, currentUserId, true);
            else if (currentUser.role == UserRole.Saler)
                return await UpdateDealAsync(id, request, currentUserId, false);
            else
                throw new UnauthorizedAccessException("You do not have permission to access this deal.");
        }
        public async Task<DealResponse> UpdateDealAsync(string id, DealUpdateRequest request, string userId, bool isAdminUpdate)
        {
            Deal existingDeal = await _unitOfWork.Deals.GetByIdAsync(id) ?? throw new InvalidDataException($"Deal with ID '{id}' does not exist.");
            var originalStatus = existingDeal.status;

            // Only validate for admin updates
            if (isAdminUpdate)
            {
                var validationResult = await IsValidAsync((IDealBaseRequest)request);
                if (!validationResult.isValid)
                {
                    throw new ArgumentException(validationResult.message);
                }
            }

            // Admin-only fields
            if (isAdminUpdate)
            {
                if (!string.IsNullOrEmpty(request.leadId))
                {
                    existingDeal.leadId = request.leadId;
                }

                if (!string.IsNullOrEmpty(request.salesRepId))
                {
                    existingDeal.salesRepId = request.salesRepId;
                }
            }

            // Common update logic for both admin and sales rep
            //validate saler access

            // Sales rep can only update their own deals
            if (existingDeal.salesRepId != userId && !isAdminUpdate)
            {
                throw new UnauthorizedAccessException("You do not have permission to update this deal.");
            }

            await UpdateCommonFieldsAsync(existingDeal, request, userId);

            existingDeal.updatedAt = DateTime.UtcNow;
            if (originalStatus != existingDeal.status)
            {
                existingDeal.statusUpdateAt = DateTime.UtcNow;
            }
            existingDeal = await _unitOfWork.Deals.UpdateAsync(id, existingDeal);

            await LogStatusChangeAsync(id, originalStatus, existingDeal.status, userId);
            await _unitOfWork.SaveAsync();

            var customer = await _unitOfWork.Leads.GetByIdAsync(existingDeal.leadId) ?? throw new InvalidDataException("The lead of this deal currently not found");
            var saler = await _unitOfWork.Users.GetByIdAsync(existingDeal.salesRepId) ?? throw new InvalidDataException("The sales representative of this deal currently not found");

            return Mapper.ToDealResponseDto(existingDeal, customer, saler);
        }

        private async Task UpdateCommonFieldsAsync(Deal existingDeal, DealUpdateRequest request, string userId)
        {
            if (!string.IsNullOrEmpty(request.status))
            {
                if (!Enum.TryParse<DealStatus>(request.status, ignoreCase: true, out var parsedStatus))
                {
                    throw new InvalidEnumArgumentException("Invalid deal status enum");
                }

                if (!IsValidTransition(existingDeal.status, parsedStatus))
                {
                    throw new InvalidOperationException($"Invalid status transition from {existingDeal.status} to {parsedStatus}");
                }

                existingDeal.status = parsedStatus;
            }

            if (request.position.HasValue)
            {
                var newPosition = await CalculateNewPosition(existingDeal.id, existingDeal.status, request.status, request.position.Value);
                existingDeal.position = newPosition;
            }

            if (!string.IsNullOrEmpty(request.priority))
            {
                if (!Enum.TryParse<DealPriority>(request.priority, ignoreCase: true, out var parsedPriority))
                {
                    throw new InvalidEnumArgumentException("Invalid deal priority enum");
                }
                existingDeal.priority = parsedPriority;
            }

            if (!string.IsNullOrEmpty(request.title))
            {
                existingDeal.title = request.title;
            }

            if (!string.IsNullOrEmpty(request.description))
            {
                existingDeal.description = request.description;
            }
            if (request.note != null)
            {
                DealNote newDealNote = new()
                {
                    title = request.note.title,
                    content = request.note.content,
                    updatedAt = DateTime.UtcNow,
                    createdBy = userId

                };
                existingDeal.notes.Add(newDealNote);
            }
            if (request.propertyId != null)
            {
                // Check if the property already exists
                var existingProperty = await _unitOfWork.Properties.GetByIdAsync(request.propertyId) ?? throw new InvalidDataException("Property not found");
                //parse to object id 
                if (!ObjectId.TryParse(request.propertyId, out ObjectId propertyIdObject))
                {
                    throw new InvalidOperationException("Invalid property ID format.");

                }
                //DealProperty? property = new() { id = propertyIdObject, type = existingProperty.type };
                //if (existingDeal.properties == null || existingDeal.properties.Count == 0)
                //{
                //    existingDeal.properties.Add(property);
                //    return;
                //}

                //if (existingDeal.properties.Any(p => p.id == propertyIdObject))
                //{
                //    throw new InvalidDataException("This property already exists in the deal.");
                //}
                //existingDeal.properties.Add(property);

            }
        }

        private async Task LogStatusChangeAsync(string dealId, DealStatus originalStatus, DealStatus newStatus, string userId)
        {
            if (newStatus != originalStatus)
            {
                var transition = new DealsActivityLog
                {
                    dealId = dealId,
                    fromStatus = originalStatus,
                    toStatus = newStatus,
                    timestamp = DateTime.UtcNow,
                    triggeredBy = userId
                };

                await _unitOfWork.DealsActivityLogs.AddAsync(transition);
            }
        }
        // Delete a Deal
        public async Task<bool> DeleteDealAsync(string id, string adminId)
        {
            var validation = await IsExistingDealAsync(id);
            if (!validation.isValid)
            {
                throw new ArgumentException(validation.message);
            }

            return await PerformSoftDeleteAsync(id, adminId);
        }

        public async Task<bool> DeleteDealByCurrentUserAsync(string id)
        {
            var currentUserId = _httpContextAccessor.HttpContext?.User?.FindFirstValue("UserID");
            if (string.IsNullOrEmpty(currentUserId))
                throw new InvalidDataException("Current user ID not found.");
            var currentUser = await _unitOfWork.Users.GetByIdAsync(currentUserId);
            if (currentUser == null)
                throw new InvalidDataException("Current user not found.");
            if (currentUser.role == UserRole.Admin)
                return await DeleteDealAsync(id, currentUserId);
            else if (currentUser.role == UserRole.Saler)
                return await DeleteDealByCurrentSalerAsync(id, currentUserId);
            else
                throw new UnauthorizedAccessException("You do not have permission to access this deal.");
        }

        // Delete a Deal (Sales Rep - only their own deals)
        public async Task<bool> DeleteDealByCurrentSalerAsync(string id, string salesRepId)
        {
            // Validation function to check if sales rep owns the deal
            async Task<bool> ValidateSalesRepOwnership(Deal deal)
            {
                if (deal.salesRepId != salesRepId)
                {
                    throw new UnauthorizedAccessException("You do not have permission to delete this deal.");
                }
                return true;
            }

            return await PerformSoftDeleteAsync(id, salesRepId, ValidateSalesRepOwnership);
        }

        // Base method for soft delete logic
        private async Task<bool> PerformSoftDeleteAsync(string dealId, string triggeredBy, Func<Deal, Task<bool>>? additionalValidation = null)
        {
            try
            {
                // Get the deal with tracking enabled
                var currentDeal = await _unitOfWork.Deals.GetByIdAsync(dealId)
                    ?? throw new ArgumentException("Deal not found");

                // Run additional validation if provided
                if (additionalValidation != null)
                {
                    var isValid = await additionalValidation(currentDeal);
                    if (!isValid)
                    {
                        throw new UnauthorizedAccessException("Validation failed for this operation.");
                    }
                }

                // Soft delete
                currentDeal.isDeleted = true;
                currentDeal.updatedAt = DateTime.UtcNow;

                // Update the entity 
                await _unitOfWork.Deals.UpdateAsync(dealId, currentDeal);

                // Log the activity
                var activityLog = new DealsActivityLog
                {
                    dealId = dealId,
                    timestamp = DateTime.UtcNow,
                    isDeleteAction = true,
                    triggeredBy = triggeredBy
                };

                await _unitOfWork.DealsActivityLogs.AddAsync(activityLog);

                // Save all changes
                await _unitOfWork.SaveAsync();


                return true;
            }
            catch (Exception ex)
            {
                throw new ArgumentException($"Failed to delete deal: {ex.Message}", ex);
            }
        }

        public async Task<PagedResult<DealsLogResponse>> GetDealsLogByDealIdAsync(string dealId, QueryDealsLog query)
        {
            var builder = Builders<DealsActivityLog>.Filter;
            var filter = builder.Empty;

            if (!string.IsNullOrEmpty(dealId))
            {
                filter &= builder.Eq(d => d.dealId, dealId);
            }

            if (query.fromStatus.HasValue)
            {
                filter &= builder.Eq(d => d.fromStatus, query.fromStatus.Value);
            }

            if (query.toStatus.HasValue)
            {
                filter &= builder.Eq(d => d.toStatus, query.toStatus.Value);
            }

            if (query.timestampFrom.HasValue)
            {
                filter &= builder.Gte(d => d.timestamp, query.timestampFrom.Value);
            }

            if (query.timestampTo.HasValue)
            {
                filter &= builder.Lte(d => d.timestamp, query.timestampTo.Value);
            }

            SortDefinition<DealsActivityLog> sortDefinition = query.sortBy switch
            {
                QueryDealsLog.QueryDealsLogSortBy.timestamp => query.isAscending
                    ? Builders<DealsActivityLog>.Sort.Ascending(d => d.timestamp)
                    : Builders<DealsActivityLog>.Sort.Descending(d => d.timestamp),
                QueryDealsLog.QueryDealsLogSortBy.fromStatus => query.isAscending
                    ? Builders<DealsActivityLog>.Sort.Ascending(d => d.fromStatus)
                    : Builders<DealsActivityLog>.Sort.Descending(d => d.fromStatus),
                QueryDealsLog.QueryDealsLogSortBy.toStatus => query.isAscending
                    ? Builders<DealsActivityLog>.Sort.Ascending(d => d.toStatus)
                    : Builders<DealsActivityLog>.Sort.Descending(d => d.toStatus),
                _ => Builders<DealsActivityLog>.Sort.Descending(d => d.timestamp) // Default to timestamp descending
            };

            int skip = (query.pageNumber - 1) * query.pageSize;
            int limit = query.pageSize;

            var logs = await _unitOfWork.DealsActivityLogs.GetDealsActivityLogByFilterAsync(filter, null, skip, limit);
            var logsResponse = await MapToDealsLogResponse(logs);

            return new PagedResult<DealsLogResponse>
            {
                data = logsResponse,
                totalCount = (int?)await _unitOfWork.DealsActivityLogs.CountDealsActivityLogByFilterAsync(filter), // Explicitly cast 'long' to 'int?'
                currentPage = query.pageNumber,
                pageSize = query.pageSize
            };
        }

        public async Task<DealNoteResponse> UpdateDealNoteAsync(string dealId, DealNoteRequest request, string dealNoteId)
        {
            var userId = _httpContextAccessor.HttpContext?.User?.FindFirstValue("UserID");
            var userRole = _httpContextAccessor.HttpContext?.User?.FindFirstValue("role");
            Deal existingDeal = await _unitOfWork.Deals.GetByIdAsync(dealId);
            var parsedDealNoteId = ObjectId.Parse(dealNoteId);
            if (userRole == null)
            {
                var user = await _unitOfWork.Users.GetByIdAsync(userId);
                if (user == null)
                {
                    throw new InvalidDataException("Current user not found.");
                }
                userRole = user.role.ToString();
            }
            // Sales rep can only update their own deals note
            if (existingDeal.salesRepId != userId && !userRole.Equals(UserRole.Admin.ToString(), StringComparison.OrdinalIgnoreCase))
            {
                throw new UnauthorizedAccessException("You do not have permission to update this deal.");
            }

            if (existingDeal == null)
            {
                throw new InvalidDataException($"Deal with ID '{dealId}' does not exist.");
            }
            var note = existingDeal.notes.FirstOrDefault(n => n.id == parsedDealNoteId);
            if (note == null)
            {
                throw new InvalidDataException($"Deal note with ID '{dealNoteId}' does not exist in deal '{dealId}'.");
            }

            // Update the note fields
            note.title = request.title;
            note.content = request.content;
            note.updatedAt = DateTime.UtcNow;

            // Save the updated deal
            existingDeal = await _unitOfWork.Deals.UpdateAsync(dealId, existingDeal);
            await _unitOfWork.SaveAsync();
            // Map to response DTO
            DealNote? updatedNote = existingDeal.notes.FirstOrDefault(n => n.id == parsedDealNoteId) ?? throw new InvalidDataException($"Deal note with ID '{dealNoteId}' does not exist in deal '{dealId}'.");
            DealNoteResponse dealNoteResponse = new()
            {
                id = updatedNote.id.ToString(),
                title = updatedNote.title,
                content = updatedNote.content,
                updatedAt = updatedNote.updatedAt
            };
            return dealNoteResponse;
        }

        public async Task<bool> AddDealPropertyAsync(string dealId, string propertyId)
        {
            var property = await _unitOfWork.Properties.GetByIdAsync(propertyId) ?? throw new InvalidDataException($"Property with ID '{propertyId}' does not exist.");
            var (userId, role) = _currentUserService.GetCurrentUser();
            // Check if the user is authorized to add the property
            var existingDeal = await _authorizationHelper.GetDealWithAuthorizationAsync(dealId, userId, role);
            //DealProperty propertyToAdd = new() { id = ObjectId.Parse(propertyId), type = property.type };
            if (existingDeal.properties.Count == 0 || existingDeal.properties == null)
            {
                // If the deal has no properties, add the new property directly
                //existingDeal.properties = [propertyToAdd];
                // Update the deal in the database
                await _unitOfWork.Deals.UpdateAsync(dealId, existingDeal);
                await _unitOfWork.SaveAsync();
                return true;
            }
            // Check if the property already exists in the deal
            if (existingDeal.properties.Any(p => p.id.ToString() == propertyId))
            {
                throw new InvalidDataException($"Property with ID '{propertyId}' already exists in deal '{dealId}'.");
            }
            // Add the property to the deal
            //existingDeal.properties.Add(propertyToAdd);
            // Update the deal in the database
            await _unitOfWork.Deals.UpdateAsync(dealId, existingDeal);
            await _unitOfWork.SaveAsync();
            return true;
        }

        public async Task<bool> DeleteDealPropertyAsync(string dealId, string propertyId)
        {
            var (userId, role) = _currentUserService.GetCurrentUser();
            // Check if the user is authorized to delete the property and get deal
            var existingDeal = await _authorizationHelper.GetDealWithAuthorizationAsync(dealId, userId, role);
            // Check if the property exists in the deal
            var propertyToDelete = existingDeal.properties.FirstOrDefault(p => p.id.ToString() == propertyId) ?? throw new InvalidDataException($"Property with ID '{propertyId}' does not exist in deal '{dealId}'.");
            // Remove the property from the deal
            existingDeal.properties.Remove(propertyToDelete);
            // Update the deal in the database
            await _unitOfWork.Deals.UpdateAsync(dealId, existingDeal);
            await _unitOfWork.SaveAsync();
            return true;
        }

        public async Task<bool> UpdateDealPropertyAsync(string dealId, string propertyId, DealPropertyUpdateRequest request)
        {
            var (userId, role) = _currentUserService.GetCurrentUser();
            // Check if the user is authorized to update the property and get deal
            var existingDeal = await _authorizationHelper.GetDealWithAuthorizationAsync(dealId, userId, role);
            // Check if the property exists in the deal
            var propertyToUpdate = existingDeal.properties.FirstOrDefault(p => p.id.ToString() == propertyId) ?? throw new InvalidDataException($"Property with ID '{propertyId}' does not exist in deal '{dealId}'.");
            // Update the property fields
            if (propertyToUpdate.value != request.value)
                propertyToUpdate.value = (double)request.value;
            if (request.isFinalize != null)
            {
                propertyToUpdate.isFinalized = request.isFinalize.Value;
            }
            // Update the deal in the database
            await _unitOfWork.Deals.UpdateAsync(dealId, existingDeal);
            await _unitOfWork.SaveAsync();
            return true;
        }


        public async Task<int> GetDealsInProgressCountAsync(string salerId)
        {
            try
            {
                var inProgressStatuses = new[]
                {
                Deal.DealStatus.New,
                Deal.DealStatus.Contacted,
                Deal.DealStatus.Negotiation,
                Deal.DealStatus.Closing
            };

                var filter = Builders<Deal>.Filter.And(
                    Builders<Deal>.Filter.Eq(d => d.salesRepId, salerId),
                    Builders<Deal>.Filter.In(d => d.status, inProgressStatuses),
                    Builders<Deal>.Filter.Eq(d => d.isDeleted, false)
                );

                return (int)await _unitOfWork.Deals.CountAsync(filter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting deals in progress count for saler {SalerId}", salerId);
                throw;
            }
        }

        public async Task<int> GetDealsClosedThisMonthCountAsync(string salerId)
        {
            try
            {
                var (startOfMonth, endOfMonth) = GetCurrentMonthRange();

                var closedStatuses = new[]
                {
                Deal.DealStatus.Won,
                Deal.DealStatus.Lost
            };

                var filter = Builders<Deal>.Filter.And(
                    Builders<Deal>.Filter.Eq(d => d.salesRepId, salerId),
                    Builders<Deal>.Filter.In(d => d.status, closedStatuses),
                    Builders<Deal>.Filter.Gte(d => d.statusUpdateAt, startOfMonth),
                    Builders<Deal>.Filter.Lte(d => d.statusUpdateAt, endOfMonth),
                    Builders<Deal>.Filter.Eq(d => d.isDeleted, false)
                );

                return (int)await _unitOfWork.Deals.CountAsync(filter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting deals closed this month count for saler {SalerId}", salerId);
                throw;
            }
        }

        public async Task<int> GetDealsWonThisMonthCountAsync(string salerId)
        {
            try
            {
                var (startOfMonth, endOfMonth) = GetCurrentMonthRange();

                var filter = Builders<Deal>.Filter.And(
                    Builders<Deal>.Filter.Eq(d => d.salesRepId, salerId),
                    Builders<Deal>.Filter.Eq(d => d.status, Deal.DealStatus.Won),
                    Builders<Deal>.Filter.Gte(d => d.statusUpdateAt, startOfMonth),
                    Builders<Deal>.Filter.Lte(d => d.statusUpdateAt, endOfMonth),
                    Builders<Deal>.Filter.Eq(d => d.isDeleted, false)
                );

                return (int)await _unitOfWork.Deals.CountAsync(filter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting deals won this month count for saler {SalerId}", salerId);
                throw;
            }
        }

        public async Task<int> GetDealsLostThisMonthCountAsync(string salerId)
        {
            try
            {
                var (startOfMonth, endOfMonth) = GetCurrentMonthRange();

                var filter = Builders<Deal>.Filter.And(
                    Builders<Deal>.Filter.Eq(d => d.salesRepId, salerId),
                    Builders<Deal>.Filter.Eq(d => d.status, Deal.DealStatus.Lost),
                    Builders<Deal>.Filter.Gte(d => d.statusUpdateAt, startOfMonth),
                    Builders<Deal>.Filter.Lte(d => d.statusUpdateAt, endOfMonth),
                    Builders<Deal>.Filter.Eq(d => d.isDeleted, false)
                );

                return (int)await _unitOfWork.Deals.CountAsync(filter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting deals lost this month count for saler {SalerId}", salerId);
                throw;
            }
        }

        public async Task<Dictionary<string, int>> GetDealsByStatusCountAsync(string salerId)
        {
            try
            {
                if (string.IsNullOrEmpty(salerId))
                {
                    throw new ArgumentException("SalerId cannot be null or empty", nameof(salerId));
                }

                // Convert string to ObjectId
                if (!ObjectId.TryParse(salerId, out ObjectId salerObjectId))
                {
                    throw new ArgumentException($"Invalid ObjectId format: {salerId}", nameof(salerId));
                }

                var pipeline = new BsonDocument[]
                {
                    new BsonDocument("$match", new BsonDocument
                        {
                            { "salesRepId", salerObjectId }, // Sử dụng ObjectId thay vì string
                            { "isDeleted", false }
                        }),
                    new BsonDocument("$group", new BsonDocument
                        {
                            { "_id", "$status" },
                            { "count", new BsonDocument("$sum", 1) }
                        })
                };

                var results = await _unitOfWork.Deals.AggregateAsync<BsonDocument>(pipeline);
                var resultsList = results.ToList();

                var statusCounts = new Dictionary<string, int>();

                // Initialize all statuses with 0
                foreach (Deal.DealStatus status in Enum.GetValues<Deal.DealStatus>())
                {
                    statusCounts[status.ToString()] = 0;
                }

                // Update with actual counts
                foreach (var result in resultsList)
                {
                    var status = result["_id"].AsString;
                    var count = result["count"].AsInt32;
                    if (statusCounts.ContainsKey(status))
                    {
                        statusCounts[status] = count;
                    }
                }

                return statusCounts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting deals by status count for saler {SalerId}", salerId);
                throw;
            }
        }


        public async Task<decimal> GetConversionRateAsync(string salerId, int totalLeads)
        {
            try
            {
                if (totalLeads == 0) return 0;

                var filter = Builders<Deal>.Filter.And(
                    Builders<Deal>.Filter.Eq(d => d.salesRepId, salerId),
                    Builders<Deal>.Filter.Eq(d => d.status, Deal.DealStatus.Won),
                    Builders<Deal>.Filter.Eq(d => d.isDeleted, false)
                );

                var wonDeals = await _unitOfWork.Deals.CountAsync(filter);
                return (decimal)wonDeals / totalLeads * 100;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating conversion rate for saler {SalerId}", salerId);
                throw;
            }
        }

        // Additional methods using AggregateAsync
        public async Task<Dictionary<string, int>> GetDealsCountByMonthAsync(string salerId, int months = 6)
        {
            try
            {
                var startDate = DateTime.UtcNow.AddMonths(-months).Date;

                var pipeline = new BsonDocument[]
                {
                new BsonDocument("$match", new BsonDocument
                {
                    { "salesRepId", salerId },
                    { "isDeleted", false },
                    { "createdAt", new BsonDocument("$gte", startDate) }
                }),
                new BsonDocument("$group", new BsonDocument
                {
                    { "_id", new BsonDocument
                        {
                            { "year", new BsonDocument("$year", "$createdAt") },
                            { "month", new BsonDocument("$month", "$createdAt") }
                        }
                    },
                    { "count", new BsonDocument("$sum", 1) }
                }),
                new BsonDocument("$sort", new BsonDocument("_id", 1))
                };

                var results = await _unitOfWork.Deals.AggregateAsync<BsonDocument>(pipeline);

                var monthCounts = new Dictionary<string, int>();

                foreach (var result in results)
                {
                    var id = result["_id"].AsBsonDocument;
                    var year = id["year"].AsInt32;
                    var month = id["month"].AsInt32;
                    var count = result["count"].AsInt32;

                    var monthKey = $"{year}-{month:D2}";
                    monthCounts[monthKey] = count;
                }

                return monthCounts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting deals count by month for saler {SalerId}", salerId);
                throw;
            }
        }

        public async Task<Dictionary<string, object>> GetDealPerformanceMetricsAsync(string salerId)
        {
            try
            {
                var pipeline = new BsonDocument[]
                {
                new BsonDocument("$match", new BsonDocument
                {
                    { "salesRepId", salerId },
                    { "isDeleted", false }
                }),
                new BsonDocument("$group", new BsonDocument
                {
                    { "_id", "$status" },
                    { "count", new BsonDocument("$sum", 1) },
                    { "avgDaysToClose", new BsonDocument("$avg",
                        new BsonDocument("$divide", new BsonArray
                        {
                            new BsonDocument("$subtract", new BsonArray { "$updatedAt", "$createdAt" }),
                            86400000 // milliseconds in a day
                        })
                    )}
                })
                };

                var results = await _unitOfWork.Deals.AggregateAsync<BsonDocument>(pipeline);

                var metrics = new Dictionary<string, object>();

                foreach (var result in results)
                {
                    var status = result["_id"].AsString;
                    var count = result["count"].AsInt32;
                    var avgDays = result.Contains("avgDaysToClose") && !result["avgDaysToClose"].IsBsonNull
                        ? Math.Round(result["avgDaysToClose"].AsDouble, 2)
                        : 0.0;

                    metrics[status] = new
                    {
                        count = count,
                        averageDaysToClose = avgDays
                    };
                }

                return metrics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting deal performance metrics for saler {SalerId}", salerId);
                throw;
            }
        }

        public async Task<List<DealValueByPropertyTypeDto>> GetDealValueByPropertyTypeAsync(
        string salesRepId = null,
        DateTime? fromDate = null,
        DateTime? toDate = null)
        {
            try
            {
                var pipeline = BuildDealValueByPropertyTypePipeline(salesRepId, fromDate, toDate);

                // Sử dụng AggregateAsync với PipelineDefinition
                var results = await _unitOfWork.Deals.AggregateAsync(pipeline);

                return results.Select(doc => new DealValueByPropertyTypeDto
                {
                    propertyType = Enum.Parse<PropertyType>(doc["_id"].AsString),
                    totalDeals = doc["totalDeals"].AsInt32,
                    totalValue = doc["totalValue"].AsDouble,
                    averageValue = doc["averageValue"].AsDouble,
                    minValue = doc["minValue"].AsDouble,
                    maxValue = doc["maxValue"].AsDouble
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting deal value by property type");
                throw;
            }
        }

        private PipelineDefinition<Deal, BsonDocument> BuildDealValueByPropertyTypePipeline(string salesRepId, DateTime? fromDate, DateTime? toDate)
        {
            var pipeline = new List<BsonDocument>();

            // Match stage - filter deals
            var matchConditions = new BsonDocument
                {
                    { "isDeleted", false }
                };

            if (!string.IsNullOrEmpty(salesRepId))
            {
                matchConditions.Add("salesRepId", salesRepId);
            }

            if (fromDate.HasValue || toDate.HasValue)
            {
                var dateFilter = new BsonDocument();
                if (fromDate.HasValue)
                    dateFilter.Add("$gte", fromDate.Value);
                if (toDate.HasValue)
                    dateFilter.Add("$lte", toDate.Value);

                matchConditions.Add("createdAt", dateFilter);
            }

            pipeline.Add(new BsonDocument("$match", matchConditions));

            // Unwind properties array
            pipeline.Add(new BsonDocument("$unwind", "$properties"));

            // Match only finalized properties
            pipeline.Add(new BsonDocument("$match", new BsonDocument("properties.isFinalized", true)));

            // Group by property type với explicit double conversion
            pipeline.Add(new BsonDocument("$group", new BsonDocument
            {
                { "_id", "$properties.type" },
                { "totalDeals", new BsonDocument("$sum", 1) },
                { "totalValue", new BsonDocument("$sum", new BsonDocument("$toDouble", "$properties.value")) },
                { "averageValue", new BsonDocument("$avg", new BsonDocument("$toDouble", "$properties.value")) },
                { "minValue", new BsonDocument("$min", new BsonDocument("$toDouble", "$properties.value")) },
                { "maxValue", new BsonDocument("$max", new BsonDocument("$toDouble", "$properties.value")) }
            }));

            // Sort by total value descending
            pipeline.Add(new BsonDocument("$sort", new BsonDocument("totalValue", -1)));

            return PipelineDefinition<Deal, BsonDocument>.Create(pipeline);
        }



        // Helper methods
        private (DateTime startOfMonth, DateTime endOfMonth) GetCurrentMonthRange()
        {
            var now = DateTime.UtcNow;
            var startOfMonth = new DateTime(now.Year, now.Month, 1);
            var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1).Date.AddHours(23).AddMinutes(59).AddSeconds(59);

            return (startOfMonth, endOfMonth);
        }

        private (DateTime startDate, DateTime endDate) GetDateRange(DateTime? startDate = null, DateTime? endDate = null)
        {
            var start = startDate ?? DateTime.UtcNow.AddMonths(-1).Date;
            var end = endDate ?? DateTime.UtcNow.Date.AddHours(23).AddMinutes(59).AddSeconds(59);

            return (start, end);
        }

        private async Task<ValidationResultDTO> IsValidAsync(IDealBaseRequest deal)
        {
            if (deal == null)
            {
                return ValidationResultDTO.Fail("Deal request is null.");
            }

            if (!string.IsNullOrEmpty(deal.salesRepId))
            {
                User salesRepExists = await _unitOfWork.Users.GetByIdAsync(deal.salesRepId);
                if (salesRepExists == null)
                {
                    return ValidationResultDTO.Fail($"Saler with ID '{deal.salesRepId}' does not exist.");
                }
                if (salesRepExists.role != UserRole.Saler)
                {
                    return ValidationResultDTO.Fail($"Saler with ID  '{deal.salesRepId}' does not exist.");
                }

            }

            if (!string.IsNullOrEmpty(deal.leadId))
            {
                bool leadExists = await _unitOfWork.Leads.ExistsAsync(deal.leadId);
                if (!leadExists)
                {
                    return ValidationResultDTO.Fail($"Lead with ID '{deal.leadId}' does not exist.");
                }
            }

            return ValidationResultDTO.Success();
        }

        private bool IsValidTransition(DealStatus current, DealStatus next)
        {
            return allowedTransitions.TryGetValue(current, out var allowedNextStates) && allowedNextStates.Contains(next);
        }

        private static Deal CreateToDeal(DealCreateRequest deal)
        {
            if (string.IsNullOrEmpty(deal.priority))
            {
                deal.priority = DealPriority.Low.ToString();
            }
            //enum try parse priority
            if (!Enum.TryParse<DealPriority>(deal.priority, ignoreCase: true, out var parsedPriority))
            {
                throw new ArgumentException("Invalid deal priority enum");
            }

            var dealConvert = new Deal
            {
                leadId = deal.leadId,
                salesRepId = deal.salesRepId,
                title = deal.title,
                description = deal.description,
                priority = parsedPriority,
                position = 0

            };
            return dealConvert;
        }

        private async Task<ValidationResultDTO> IsExistingDealAsync(string id)
        {
            bool exists = await _unitOfWork.Deals.ExistsAsync(id);
            if (!exists)
            {
                return ValidationResultDTO.Fail($"Deal with ID '{id}' does not exist.");
            }
            return ValidationResultDTO.Success();
        }


        private async Task<List<DealResponse>> MapToDealsResponse(IEnumerable<Deal> deals)
        {
            var dealResponses = new List<DealResponse>();
            foreach (var deal in deals)
            {
                Lead lead = await _unitOfWork.Leads.GetByIdAsync(deal.leadId) ?? throw new InvalidDataException($"Lead with ID '{deal.leadId}' does not exist.");
                User? saler = await _unitOfWork.Users.GetByIdAsync(deal.salesRepId) ?? throw new InvalidDataException($"Sales representative with ID '{deal.salesRepId}' does not exist.");
                dealResponses.Add(Mapper.ToDealResponseDto(deal, lead, saler));
            }
            return dealResponses;

        }

        private Task<List<DealsLogResponse>> MapToDealsLogResponse(IEnumerable<DealsActivityLog> logs)
        {
            //use linq ,trigger by use _unitOfWork.User.FindByIdAsync
            return Task.FromResult(logs.Where(log => log != null)
                .Select(async log =>
                {
                    var user = await _unitOfWork.Users.GetByIdAsync(log.triggeredBy);
                    return Mapper.ToDealsLogResponse(log, user);
                })
                .Select(task => task.Result)
                .ToList());
        }

        private async Task ValidateLeadAccess(string currentUserId, string? leadId)
        {
            if (!string.IsNullOrEmpty(leadId))
            {
                var lead = await _unitOfWork.Leads.GetByIdAsync(leadId) ?? throw new InvalidDataException($"Lead with ID '{leadId}' does not exist.");
                string? containedLeadId = null;
                foreach (var assignedTo in lead.assignedTo)
                {
                    if (assignedTo.id == currentUserId)
                    {
                        containedLeadId = assignedTo.id;
                        break;
                    }
                }
                if (containedLeadId == null)
                    throw new InvalidDataException($"Lead with ID '{leadId}' is not assigned to the current sales representative.");
            }
        }

        private async Task<decimal> CalculateNewPosition(ObjectId dealId, DealStatus currentStatus, string newStatusString, int targetIndex)
        {
            var newStatus = string.IsNullOrEmpty(newStatusString) ? currentStatus : Enum.Parse<DealStatus>(newStatusString);
            // Get all deals in target column except the one being moved

            var otherDealsInColumn = await _unitOfWork.Deals.GetManyAsync(
                deal => deal.status == newStatus && deal.id != dealId,
                orderBy: deals => deals.OrderBy(d => d.position)
            );

            if (otherDealsInColumn.Count == 0)
            {
                return 1000; // Only deal in column
            }

            if (targetIndex <= 0)
            {
                // Insert at beginning
                return otherDealsInColumn.First().position - 1000;
            }

            if (targetIndex >= otherDealsInColumn.Count)
            {
                // Insert at end
                return otherDealsInColumn.Last().position + 1000;
            }

            // Insert between two items
            var prevPosition = otherDealsInColumn[targetIndex - 1].position;
            var nextPosition = otherDealsInColumn[targetIndex].position;

            return (prevPosition + nextPosition) / 2;
        }


    }
}
