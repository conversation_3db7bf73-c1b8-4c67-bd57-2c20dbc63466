﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.TransactionHistoryDTO;
using BusinessObjects.Models;
using Microsoft.AspNetCore.Mvc;
using Services.Interface;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace API.Controllers
{
    [Route("api/transaction-histories")]
    [ApiController]
    public class TransactionHistoryController : ControllerBase
    {
        private readonly ITransactionHistoryService _transService;

        public TransactionHistoryController(ITransactionHistoryService transService)
        {
            _transService = transService;
        }

        private ApiResponse CreateResponse(int code, bool status, string message, object data = null)
        {
            return new ApiResponse { code = code, status = status, message = message, data = data };
        }

        [HttpGet]
        public async Task<ActionResult<List<TransactionHistoryCreateResponse>>> GetAll()
        {
            var transactions = await _transService.GetAllTransactionHistoriesAsync();
            return Ok(CreateResponse(200, true, "Fetched transaction histories successfully", transactions));
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<TransactionHistoryCreateResponse>> GetById(string id)
        {
            try
            {
                var transaction = await _transService.GetTransactionHistoryByIdAsync(id);

                return Ok(CreateResponse(200, true, "Transaction history retrieved successfully", transaction));
            }
            catch (InvalidDataException ex)
            {
                return NotFound(CreateResponse(404, false, ex.Message));
            }


        }

        [HttpPost]
        public async Task<ActionResult<TransactionHistoryCreateResponse>> Create([FromForm] TransactionHistoryCreateRequest transaction)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(CreateResponse(400, false, "Invalid transaction data", ModelState));
            }

            try
            {
                var trans = await _transService.AddTransactionHistoryAsync(transaction);
                return CreatedAtAction(nameof(GetById), new { id = trans.id },
                    CreateResponse(201, true, "Transaction history created successfully", trans));
            }
            catch (InvalidOperationException ex)
            {
                return Conflict(CreateResponse(404, false, ex.Message));
            }


        }

        [HttpPatch("{id}")]
        public async Task<ActionResult<TransactionHistoryUpdateResponse>> Update(string id, [FromForm] TransactionHistoryUpdateRequest updatedTransaction)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(CreateResponse(400, false, "Invalid update data", ModelState));
            }
            try
            {
                var existing = await _transService.GetTransactionHistoryByIdAsync(id);

                var result = await _transService.UpdateTransactionHistoryAsync(id, updatedTransaction);
                return Ok(CreateResponse(200, true, "Transaction history updated successfully"));
            }
            catch (InvalidDataException ex)
            {
                return NotFound(CreateResponse(404, false, ex.Message));
            }
            catch (InvalidOperationException ex)
            {
                return Conflict(CreateResponse(409, false, ex.Message));
            }

        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(string id)
        {
            try
            {
                var existing = await _transService.GetTransactionHistoryByIdAsync(id);

                await _transService.DeleteTransactionHistoryAsync(id);
                return Ok(CreateResponse(204, true, "Transaction history deleted successfully"));
            }
            catch (InvalidDataException ex)
            {
                return NotFound(CreateResponse(404, false, ex.Message));
            }
        }
    }
}
