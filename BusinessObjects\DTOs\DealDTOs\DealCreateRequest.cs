﻿using BusinessObjects.Validations;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace BusinessObjects.DTOs.DealDTOs
{
    public class DealCreateRequest : IDealBaseRequest
    {

        [BsonRepresentation(BsonType.ObjectId)]
        [ValidObjectId(ErrorMessage = "leadId must be a valid ObjectId")]
        public string? leadId { get; set; }

        [BsonRepresentation(BsonType.ObjectId)]
        [ValidObjectId(ErrorMessage = "salesRepId must be a valid ObjectId")]
        public string? salesRepId { get; set; }


        public string? title { get; set; }

        public string? description { get; set; }

        /// <summary>
        /// Filter by priority , e.g. "Low", "Medium", "High"
        /// </summary>
        public string? priority { get; set; }
    }
}
