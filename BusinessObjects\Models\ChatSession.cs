﻿using FirebaseAdmin.Messaging;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson;

namespace BusinessObjects.Models
{
    public class ChatSession
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string sessionId { get; set; }
        public string? userId { get; set; }
        public string? anonymousSessionId { get; set; }
        public List<Message> messages { get; set; } = new List<Message>();
        public string status { get; set; } = "anonymous";
        [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
        public DateTime createdAt { get; set; } = DateTime.UtcNow;
        [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
        public DateTime updatedAt { get; set; } = DateTime.UtcNow;
    }

    public class AnonymousSession
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string sessionId { get; set; }
        public string? userId { get; set; }
        public string? name { get; set; } 
        public string? phoneNumber { get; set; }
        public string? updatedBy { get; set; }
        [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
        public DateTime createdAt { get; set; } = DateTime.UtcNow;
        [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
        public DateTime expiresAt { get; set; }
    }

    public class Message
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string messageId { get; set; } = ObjectId.GenerateNewId().ToString();
        public string? senderId { get; set; }
        [BsonRequired]
        public string content { get; set; }
        [BsonRepresentation(BsonType.String)]
        public SenderType senderType { get; set; } // "User" or "Admin" or "Saler"
        public bool isRead { get; set; } = false;
        [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
        public DateTime timestamp { get; set; } = DateTime.UtcNow;
    }

    public enum SenderType
    {
        Saler,
        User,
        Admin
    }

}
