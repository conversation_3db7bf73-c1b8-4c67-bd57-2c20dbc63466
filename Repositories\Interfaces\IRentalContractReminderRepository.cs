using BusinessObjects.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Repositories.Interfaces
{
    public interface IRentalContractReminderRepository
    {
        Task<List<RentalContractReminder>> GetRemindersToNotifyAsync();
        Task<RentalContractReminder> CreateReminderAsync(RentalContractReminder reminder);
        Task SendReminderAsync(RentalContractReminder reminder);
        Task<List<RentalContractReminder>> GetRemindersByUserIdAsync(string userId);
    }
} 