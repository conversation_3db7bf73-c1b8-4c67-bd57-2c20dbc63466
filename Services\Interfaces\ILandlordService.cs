﻿using BusinessObjects.DTOs.LandlordDTOs;
using BusinessObjects.Models;
using BusinessObjects.QueryObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Services.Interfaces
{
    public interface ILandlordService
    {
        Task<Landlord?> AddLandlordAsync(Landlord landlord);
        Task<Landlord?> GetLandlordByIdAsync(string email);
        Task<Landlord?> UpdateLandlordByIdAsync(string id, LandlordUpdateRequest updatedLandlord);
        Task<List<Landlord>> GetAllLandlordsAsync(QueryLandlord queryLandlord);
        Task<Landlord?> DeleteLandlordByIdAsync(string id);
    }
}
