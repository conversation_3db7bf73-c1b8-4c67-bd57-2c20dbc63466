﻿using BusinessObjects.DTOs.TenantDTOs;
using BusinessObjects.Models;
using BusinessObjects.QueryObject;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Services.Interfaces
{
    public interface ITenantService
    {
        Task<IEnumerable<Tenant>> GetAllTenantsAsync();
        Task<Tenant> GetTenantByIdAsync(string id);
        Task<Tenant> GetTenantByPhoneAsync(string phone);
        Task CreateUserFromTenantAsync(TenantCreateRequest tenant);
        Task<bool> UpdateTenantAsync(TenantUpdateRequest tenant, List<string?> idVerificationFile);
        Task<Tenant> DeleteTenantAsync(string id);
        Task<List<Tenant>> GetAllTenantsAsync(QueryTenant query);
        Task<string> UploadIdVerificationAsync(IFormFile? file);
        Task<Tenant> AddTenantAsync(TenantCreateRequest tenant, List<string?> idVerificationUrl);
        Task<Tenant> GetTenantByEmailAsync(string email);
    }   
}
