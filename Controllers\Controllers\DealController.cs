﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.DealDTOs;
using BusinessObjects.QueryObject;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Services.Interfaces;

namespace Controllers.Controllers
{
    /// <summary>
    /// Controller for managing deals in the real estate platform
    /// </summary>
    [Route("api/deals")]
    [ApiController]
    public class DealController : ControllerBase
    {
        private readonly IDealService _dealService;

        public DealController(IDealService dealService)
        {
            _dealService = dealService;
        }

        /// <summary>
        /// Creates a standardized API response.
        /// </summary>
        /// <param name="code">HTTP code.</param>
        /// <param name="status">True for success; false for error.</param>
        /// <param name="message">Response message.</param>
        /// <param name="data">Response data.</param>
        /// <returns>An ApiResponse object.</returns>
        private ApiResponse CreateResponse(int code, bool status, string message, object data = null)
        {
            return new ApiResponse
            {
                code = code,
                status = status,
                message = message,
                data = data
            };
        }

        /// <summary>
        /// Creates a new Deal by the current user ( Available for Admin and Salers).
        /// </summary>
        /// <param name="request">The Deal creation data.</param>
        /// <returns>The created Deal as a DealResponse.</returns>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api/deals
        ///     for Admin role : 
        ///     {
        ///        "salesRepId": "67f8d549f61f216b941b62ad",
        ///        "leadId": "507f1f77bcf86cd799439011",
        ///        "title": "Luxury Apartment Deal",
        ///        "description": "Potential deal for luxury apartment in downtown area",
        ///        "priority" : "Low" // Available priority : "Low" , "Medium" , "High" 
        ///     }
        ///     for Saler role : 
        ///     {
        ///        "leadId": "507f1f77bcf86cd799439011",
        ///        "title": "Luxury Apartment Deal",
        ///        "description": "Potential deal for luxury apartment in downtown area",
        ///        "priority" : "Low" // Available priority : "Low" , "Medium" , "High" 
        ///     }
        ///
        /// </remarks>
        [HttpPost]
        [Authorize]
        public async Task<ActionResult> AddDealByCurrentUser([FromBody] DealCreateRequest request)
        {
            try
            {
                var newDeal = await _dealService.AddDealByCurrentUserAsync(request);
                return Ok(CreateResponse(201, true, "Deal created successfully", newDeal));
            }
            catch (UnauthorizedAccessException ex)
            {
                return Forbid();
            }
            catch (ArgumentException ex)
            {
                return BadRequest(CreateResponse(400, false, ex.Message));
            }
            catch (InvalidDataException ex)
            {
                return NotFound(CreateResponse(404, false, ex.Message));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateResponse(500, false, "An error occurred while creating the deal", ex.Message));
            }
        }

        /// <summary>
        /// Gets all deals for the current authenticated user ( Available for Admin and Saler).
        /// </summary>
        /// <param name="query">Query parameters for filtering, sorting and pagination.</param>
        /// <returns>A list of DealResponseDTO objects for the current user.</returns>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/deals?pageNumber=1&amp;pageSize=10
        ///
        /// </remarks>
        [HttpGet]
        [Authorize]
        public async Task<IActionResult> GetAllDealsByCurrentUser([FromQuery] QueryDeal query)
        {
            try
            {
                var deals = await _dealService.GetAllDealsByCurrentUserAsync(query);
                return Ok(CreateResponse(200, true, "Fetched deals for current user successfully", deals));
            }
            catch (UnauthorizedAccessException ex)
            {
                return Forbid();
            }
            catch (ArgumentException ex)
            {
                return BadRequest(CreateResponse(400, false, ex.Message));
            }
            catch (InvalidDataException ex)
            {
                return NotFound(CreateResponse(404, false, ex.Message));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateResponse(500, false, ex.Message, null));
            }
        }

        /// <summary>
        /// Gets status transition history for a specific deal.
        /// </summary>
        /// <param name="id">The Deal ID.</param>
        /// <param name="query">Query parameters for filtering and pagination.</param>
        /// <returns>A list of StatusTransitionDealResponse objects.</returns>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/deals/507f1f77bcf86cd799439011/StatusTransitionDeals?pageNumber=1&amp;pageSize=10
        ///
        /// </remarks>
        [HttpGet("{id:length(24)}/log")]
        public async Task<IActionResult> GetDealsLog(string id, [FromQuery] QueryDealsLog query)
        {
            try
            {
                var transitions = await _dealService.GetDealsLogByDealIdAsync(id, query);
                return Ok(CreateResponse(200, true, "Fetched deals log successfully", transitions));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateResponse(500, false, ex.Message, null));
            }
        }

        /// <summary>
        /// Gets a specific deal by its ID ( Available for Admin and Saler).
        /// </summary>
        /// <param name="id">The Deal ID.</param>
        /// <returns>The DealResponse matching the specified ID of current user </returns>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/deals/507f1f77bcf86cd799439011
        ///
        /// </remarks>
        [HttpGet("{id:length(24)}")]
        [Authorize]
        public async Task<IActionResult> GetDealByIdByCurrentUser(string id)
        {
            try
            {
                var deal = await _dealService.GetDealByIdByCurrentUserAsync(id);
                return Ok(CreateResponse(200, true, "Fetched deal successfully", deal));
            }
            catch (UnauthorizedAccessException ex)
            {
                return Forbid();
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateResponse(500, false, ex.Message, null));
            }
        }


        /// <summary>
        /// Updates a specific deal ( Available for Admin and Saler).
        /// </summary>
        /// <param name="id">The ID of the deal to update.</param>
        /// <param name="deal">The updated deal data.</param>
        /// <returns>The updated DealResponseDTO.</returns>
        /// <remarks>
        /// Sample request:
        ///
        ///     PUT /api/deals/507f1f77bcf86cd799439011
        ///     {
        ///        "leadId": "507f1f77bcf86cd799439011",
        ///        "salesRepId": "507f1f77bcf86cd799439012",
        ///        "title": "Updated Luxury Apartment Deal",
        ///        "description": "Updated description for luxury apartment deal",
        ///        "status": "Negotiation",
        ///        "priority" : "Low" // Available priority : "Low" , "Medium" , "High" 
        ///     }
        ///
        /// </remarks>
        [HttpPut("{id:length(24)}")]
        [Authorize]
        public async Task<IActionResult> UpdateDealByCurrentUser(string id, [FromBody] DealUpdateRequest deal)
        {
            try
            {
                var updatedDeal = await _dealService.UpdateDealByCurrentUserAsync(id, deal);
                return Ok(CreateResponse(200, true, "Deal updated successfully", updatedDeal));
            }
            catch (ArgumentException ex)
            {
                return BadRequest(CreateResponse(400, false, ex.Message));
            }
            catch (InvalidDataException ex)
            {
                return NotFound(CreateResponse(404, false, ex.Message));
            }
            catch (UnauthorizedAccessException ex)
            {
                return Forbid();
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateResponse(500, false, ex.Message, null));
            }
        }
        /// <summary>
        /// Deletes a specific deal ( Available for Admin and Saler).
        /// </summary>
        /// <param name="id">The ID of the deal to delete.</param>
        /// <returns>A success message.</returns>
        /// <remarks>
        /// Sample request:
        ///
        ///     DELETE /api/deals/507f1f77bcf86cd799439011
        ///
        /// </remarks>
        [HttpDelete("{id:length(24)}")]
        [Authorize]
        public async Task<IActionResult> DeleteDeal(string id)
        {
            try
            {
                await _dealService.DeleteDealByCurrentUserAsync(id);
                return Ok(CreateResponse(200, true, "Deal deleted successfully", null));
            }
            catch (ArgumentException ex)
            {
                return BadRequest(CreateResponse(400, false, ex.Message));
            }
            catch (InvalidDataException ex)
            {
                return NotFound(CreateResponse(404, false, ex.Message));
            }
            catch (UnauthorizedAccessException ex)
            {
                return Forbid();
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateResponse(500, false, ex.Message, null));
            }
        }

        /// <summary>
        /// Update a deal note by its ID ( Available for Admin and Saler).
        /// </summary>
        /// 
        [HttpPatch("{id:length(24)}/notes/{noteId:length(24)}")]
        [Authorize(Roles = "Admin,Saler")]
        public async Task<IActionResult> UpdateDealNote(string id, string noteId, DealNoteRequest dealNoteRequest)
        {
            try
            {
                var updatedNote = await _dealService.UpdateDealNoteAsync(id, dealNoteRequest, noteId);
                return Ok(CreateResponse(200, true, "Deal note updated successfully", updatedNote));
            }
            catch (ArgumentException ex)
            {
                return BadRequest(CreateResponse(400, false, ex.Message));
            }
            catch (InvalidDataException ex)
            {
                return NotFound(CreateResponse(404, false, ex.Message));
            }
            catch (UnauthorizedAccessException ex)
            {
                return Forbid();
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateResponse(500, false, ex.Message, null));
            }

        }

        [HttpPost("{dealId:length(24)}/properties")]
        [Authorize(Roles = "Admin,Saler")]
        public async Task<IActionResult> AddDealProperty(string dealId, [FromBody] AddDealPropertyRequest request)
        {
            try
            {
                var result = await _dealService.AddDealPropertyAsync(dealId, request.propertyId);
                if (result)
                {
                    return Ok(CreateResponse(200, true, "Deal property added successfully", null));
                }
                else
                {
                    return NotFound(CreateResponse(400, false, "Add failed"));
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                return Forbid();
            }
            catch (ArgumentException ex)
            {
                return BadRequest(CreateResponse(400, false, ex.Message));
            }
            catch (InvalidDataException ex)
            {
                return NotFound(CreateResponse(404, false, ex.Message));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateResponse(500, false, ex.Message, null));
            }
        }

        [HttpDelete("{dealId:length(24)}/properties/{propertyId:length(24)}")]
        [Authorize(Roles = "Admin,Saler")]
        public async Task<IActionResult> DeleteDealProperty(string dealId, string propertyId)
        {
            try
            {
                var result = await _dealService.DeleteDealPropertyAsync(dealId, propertyId);
                if (result)
                {
                    return Ok(CreateResponse(200, true, "Deal property deleted successfully", null));
                }
                else
                {
                    return NotFound(CreateResponse(404, false, "Deal property not found"));
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                return Forbid();
            }
            catch (ArgumentException ex)
            {
                return BadRequest(CreateResponse(400, false, ex.Message));
            }
            catch (InvalidDataException ex)
            {
                return NotFound(CreateResponse(404, false, ex.Message));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateResponse(500, false, ex.Message, null));
            }
        }

        [HttpPatch("{dealId:length(24)}/properties/{propertyId:length(24)}")]
        [Authorize(Roles = "Admin,Saler")]
        public async Task<IActionResult> UpdateDealProperty(string dealId, string propertyId, [FromBody] DealPropertyUpdateRequest request)
        {
            try
            {
                var result = await _dealService.UpdateDealPropertyAsync(dealId, propertyId, request);
                if (result)
                {
                    return Ok(CreateResponse(200, true, "Deal property updated successfully", null));
                }
                else
                {
                    return NotFound(CreateResponse(404, false, "Deal property not found"));
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                return Forbid();
            }
            catch (ArgumentException ex)
            {
                return BadRequest(CreateResponse(400, false, ex.Message));
            }
            catch (InvalidDataException ex)
            {
                return NotFound(CreateResponse(404, false, ex.Message));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateResponse(500, false, ex.Message, null));
            }
        }

    }
}
