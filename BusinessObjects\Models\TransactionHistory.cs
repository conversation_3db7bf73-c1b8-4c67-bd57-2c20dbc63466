﻿using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Runtime.InteropServices.JavaScript;

namespace BusinessObjects.Models
{
    public class TransactionHistory
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        [BsonElement("transaction_id")]
        public ObjectId transactionId { get; set; }
        [BsonElement("transaction_date")]
        public DateTime transactionDate { get; set; }
        [BsonElement("transaction_type")]
        public TransactionType transactionType { get; set; } // forSale, forRent
        [BsonElement("price")]
        public double price { get; set; }
        [BsonElement("buyer_id")]
        public ObjectId? buyerId { get; set; }
        [BsonElement("seller_id")]
        public ObjectId? sellerId { get; set; }
    }
    public enum TransactionType
    {
        [BsonElement("forRent")] forRent,
        [BsonElement("forSale")] forSale,
    }
}   
