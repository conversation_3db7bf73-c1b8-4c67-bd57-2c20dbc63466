﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.UserDTOs;
using BusinessObjects.DTOs.UserDTOs.UserDTO;
using BusinessObjects.Models;
using BusinessObjects.Settings;
using Google.Apis.Auth;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.Data;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using Services.Implements;
using Services.Interfaces;
using Services.Tools;
using System.IdentityModel.Tokens.Jwt;
using System.Text;
using System.Text.RegularExpressions;


namespace Controllers.Controllers
{
    [Route("api/auth")]
    [ApiController]
    public class AuthController : ControllerBase
    {
        private readonly IEmailService _emailService;
        private readonly IAuthService _authService;
        private readonly IZaloApiService _zaloApiService;
        private readonly IUserService _userService;
        private readonly UserPasswordHasher _userPasswordHasher;
        private readonly TokenTools _token;
        private readonly FirebaseService _firebaseService;
        private readonly KeySettings _keySettings;
        private readonly IRefreshTokenService _refreshTokenService;
        private readonly IPasswordResetService _passwordResetService;
        private readonly IConfiguration _configuration;

        private ApiResponse CreateResponse(int code, bool status, string message, object data = null)
        {
            return new ApiResponse { code = code, status = status, message = message, data = data };
        }
        public AuthController(IUserService userService, UserPasswordHasher userPasswordHasher, TokenTools token, FirebaseService firebaseService, IOptionsMonitor<KeySettings> keySettings,
                              IRefreshTokenService refreshTokenService, IPasswordResetService passwordResetService, IEmailService emailService, IAuthService authService, IZaloApiService zaloApiService, IConfiguration configuration)
        {
            _userService = userService;
            _userPasswordHasher = userPasswordHasher;
            _token = token;
            _firebaseService = firebaseService;
            _keySettings = keySettings.CurrentValue;
            _refreshTokenService = refreshTokenService;
            _passwordResetService = passwordResetService;
            _emailService = emailService;
            _authService = authService;
            _zaloApiService = zaloApiService;
            _configuration = configuration;
        }
        [HttpPost("login")]
        public async Task<IActionResult> Login(UserLoginRequest model)
        {
            try
            {
                var token = await _authService.LoginAsync(model);

                if (token == null)
                {
                    return BadRequest(CreateResponse(401, false, "Tài khoản này chưa được xác thực"));
                }

                return Ok(CreateResponse(200, true, "Đăng nhập thành công", token));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(400, false, "Đăng nhập thất bại: " + ex.Message));
            }
        }
        [HttpPost("register")]
        public async Task<IActionResult> Register(UserRegister model)
        {
            try
            {
                await _authService.RegisterAsync(model);
                return Ok(CreateResponse(200, true, "Vui lòng nhập mã OTP trong email hoặc Zalo để xác thực tài khoản"));
            }
            catch (Exception e)
            {
                return BadRequest(CreateResponse(400, false, "Đăng kí thất bại: " + e.Message));
            }
        }
        [HttpPost("renew-token")]
        public async Task<IActionResult> RenewToken(TokenSetting model)
        {
            var jwtTokenHandler = new JwtSecurityTokenHandler();
            var secretKeyBytes = Encoding.UTF8.GetBytes(_keySettings.SecretKey);
            var tokenValidateParam = new TokenValidationParameters
            {
                //tự cấp token
                ValidateIssuer = false,
                ValidateAudience = false,

                //ký vào token
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(secretKeyBytes),

                ClockSkew = TimeSpan.Zero,

                ValidateLifetime = false //ko kiểm tra token hết hạn
            };
            try
            {
                //check 1: AccessToken valid format
                var tokenInVerification = jwtTokenHandler.ValidateToken(model.AccessToken, tokenValidateParam, out var validatedToken);

                //check 2: Check alg
                if (validatedToken is JwtSecurityToken jwtSecurityToken)
                {
                    var result = jwtSecurityToken.Header.Alg.Equals(SecurityAlgorithms.HmacSha512, StringComparison.InvariantCultureIgnoreCase);
                    if (!result)//false
                    {
                        return BadRequest(CreateResponse(400, false, "Token không hợp lệ"));

                    }
                }

                //check 3: Check accessToken expire?
                var utcExpireDate = long.Parse(tokenInVerification.Claims.FirstOrDefault(x => x.Type == JwtRegisteredClaimNames.Exp).Value);

                var expireDate = _token.ConvertUnixTimeToDateTime(utcExpireDate);
                if (expireDate > DateTime.UtcNow)
                {
                    return BadRequest(CreateResponse(400, false, "Access token vẫn chưa hết hạn"));

                }

                //check 4: Check refreshtoken exist in DB
                var storedToken = await _refreshTokenService.GetRefreshToken(model.RefreshToken);
                if (storedToken == null)
                {
                    return NotFound(CreateResponse(404, false, "Refresh token không tồn tại"));

                }

                //check 5: check refreshToken is used/revoked?
                if (storedToken.isUsed)
                {
                    return BadRequest(CreateResponse(400, false, "Refresh token không hợp lệ (đã được sử dụng)"));

                }
                if (storedToken.isRevoked)
                {
                    return BadRequest(CreateResponse(400, false, "Refresh token không hợp lệ (đã bị thu hồi)"));

                }

                //check 6: AccessToken id == JwtId in RefreshToken
                var jti = tokenInVerification.Claims.FirstOrDefault(x => x.Type == JwtRegisteredClaimNames.Jti).Value;
                if (storedToken.jwtId != jti)
                {
                    return BadRequest(CreateResponse(400, false, "Token không khớp"));

                }

                //Update token is used
                storedToken.isRevoked = true;
                storedToken.isUsed = true;
                await _refreshTokenService.Update(storedToken.id, storedToken);

                //create new token
                var user = await _userService.FindUserByIdAsync(storedToken.userId);
                var usermodel = new UserDTO
                {
                    Id = user.id,
                    userName = user.userName,
                    about = user.about,
                    phoneNumber = user.phoneNumber,
                    avatar = user.avatar,
                    birthdate = user.birthdate,
                    fullName = user.fullName,
                    email = user.email,
                    joinedAt = user.joinedAt,
                    role = user.role,
                    status = user.status,
                    password = user.password,
                };
                var token = await _token.GenerateToken(usermodel);
                return Ok(CreateResponse(200, true, "Thành công", new
                {
                    accessToken = token.AccessToken,
                    refreshToken = token.RefreshToken
                }));

            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(400, false, "Thất bại: " + ex.Message));

            }
        }
        [Authorize]
        [HttpPut("change-password")]
        public async Task<IActionResult> ResetPassword(ChangePasswordRequest newpassword)
        {
            try
            {
                var currentUserId = User.Claims.FirstOrDefault(c => c.Type == "UserID")?.Value;
                if (string.IsNullOrEmpty(currentUserId))
                {
                    return BadRequest(CreateResponse(400, false, "User ID not found in token"));
                }
                var user = await _userService.FindUserByIdAsync(currentUserId);
                if (user == null)
                {
                    return NotFound(CreateResponse(404, false, "User does not exist"));
                }

                var usermodel = new UserDTO
                {
                    userName = user.userName,
                    about = user.about,
                    phoneNumber = user.phoneNumber,
                    birthdate = user.birthdate,
                    fullName = user.fullName,
                    email = user.email,
                    password = user.password,
                };
                var verifypassword = _userPasswordHasher.VerifyHashedPassword(usermodel, user.password, newpassword.oldPassword);
                if (verifypassword == PasswordVerificationResult.Failed)
                {
                    return BadRequest(CreateResponse(400, false, "Old password is wrong"));
                }
                if (newpassword.newPassword != newpassword.confirmPassword)
                {
                    return BadRequest(CreateResponse(400, false, "Confirm password doesn't match new password"));
                }
                var passwordPattern = @"^(?=.*[A-Z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,}$";
                if (!Regex.IsMatch(newpassword.newPassword, passwordPattern))
                {
                    return BadRequest(CreateResponse(400, false, "Password must have at least 8 characters, one capital letter and one digit"));
                }
                var n_password = _userPasswordHasher.HashPassword(usermodel, newpassword.newPassword);
                user.password = n_password;
                await _userService.UpdateUserAsync(currentUserId, user);
                return Ok(CreateResponse(200, true, "Password updated successfully"));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(400, false, "Failed to update password: " + ex.Message));
            }

        }
        [HttpPut("renew-password")]
        public async Task<IActionResult> ResetPassword([FromBody] RenewPasswordRequest request)
        {
            try
            {
                var userId = await _passwordResetService.GetUserIdByTokenAsync(request.token);
                if (userId == null)
                {
                    return BadRequest(CreateResponse(400, false, "Invalid or expired token"));
                }

                var user = await _userService.FindUserByIdAsync(userId);
                if (user == null)
                {
                    return NotFound(CreateResponse(404, false, "User not found"));
                }
                var usermodel = new UserDTO
                {
                    userName = user.userName,
                    about = user.about,
                    phoneNumber = user.phoneNumber,
                    birthdate = user.birthdate,
                    fullName = user.fullName,
                    email = user.email,
                    password = user.password,
                };
                user.password = _userPasswordHasher.HashPassword(usermodel, request.newPassword);
                await _userService.UpdateUserAsync(user.id, user);

                await _passwordResetService.DeleteTokenAsync(request.token);

                return Ok(CreateResponse(200, true, "Password reset successfully"));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(400, false, "Failed to reset password: " + ex.Message));
            }
        }


        [HttpPost("forgot-password")]
        public async Task<IActionResult> ForgotPassword([FromBody] ForgotPasswordRequest request)
        {
            var user = await _userService.FindUserByEmailAsync(request.Email);
            if (user == null)
                return NotFound(CreateResponse(404, false, "User with this email does not exist"));


            var token = Guid.NewGuid().ToString();
            var expiration = TimeSpan.FromHours(1);


            await _passwordResetService.SaveTokenAsync(token, user.id, expiration);


            var resetLink = $"http://localhost:3000/renew-password?token={token}";


            var emailContent = EmailTemplate.GetPasswordResetEmail(user.fullName, resetLink);


            await _emailService.SendEmailAsync(user.email, "Password Reset Request", emailContent);
            return Ok(CreateResponse(200, true, "Password reset email sent", token));

        }
        [HttpPost("google-login")]
        public async Task<IActionResult> GoogleLogin([FromBody] GoogleLoginRequest request)
        {
            try
            {
                var clientId = _configuration["GoogleAuthCalendar:ClientId"];
                var settings = new GoogleJsonWebSignature.ValidationSettings
                {
                    Audience = new[] { clientId }
                };
                var payload = await GoogleJsonWebSignature.ValidateAsync(request.idToken, settings);
                var user = await _userService.FindUserByEmailAsync(payload.Email);
                if (user == null)
                {
                    user = new User
                    {
                        userName = payload.Name ?? payload.Email.Split('@')[0],
                        fullName = payload.Name ?? "User",
                        email = payload.Email,
                        password = "",
                        phoneNumber = null,
                        avatar = payload.Picture,
                        status = Status.Online,
                        role = UserRole.User,
                        about = null,
                        birthdate = null,
                        joinedAt = DateTime.UtcNow,
                        isVerified = true
                    };
                    await _userService.SaveUserAsync(user);
                }
                var usermodel = new UserDTO
                {
                    Id = user.id,
                    userName = user.userName,
                    about = user.about,
                    phoneNumber = user.phoneNumber,
                    avatar = user.avatar,
                    birthdate = user.birthdate,
                    fullName = user.fullName,
                    email = user.email,
                    joinedAt = user.joinedAt,
                    role = user.role,
                    status = user.status,
                    password = user.password
                };
                var token = await _token.GenerateToken(usermodel);
                return Ok(CreateResponse(200, true, "Google login successful", token));
            }
            catch (InvalidJwtException)
            {
                return BadRequest(CreateResponse(400, false, "Invalid Google Token"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateResponse(500, false, "An error occurred: " + ex.Message));
            }
        }

        [HttpPost("verify-otp")]
        public async Task<IActionResult> VerifyOTP([FromBody] VerifyOTPRequest request)
        {
            try
            {
                await _authService.VerifyOTPAsync(request.otp, request.verifyKey);
                return Ok(CreateResponse(200, true, "Xác thực OTP thành công"));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(400, false, "Xác thực OTP thất bại: " + ex.Message));
            }
        }

        [HttpPost("request-otp")]
        public async Task<IActionResult> RequestOTP([FromBody] OTPRequest request)
        {
            try
            {
                await _authService.RequestOTPAsync(request.verifyKey);
                return Ok(CreateResponse(200, true, "Đã gửi mã xác thực OTP"));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(400, false, "Gửi mã xác thực OTP thất bại: " + ex.Message));
            }
        }

    }
}
