﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using BusinessObjects.QueryObject;
using MongoDB.Driver;
using Repositories.Interfaces;

namespace Repositories.Implements
{
    public class ChatMessageRepository : GenericRepository<ChatMessage>, IChatMessageRepository
    {
        private readonly IMongoCollection<ChatMessage> _chatMessages;

        public ChatMessageRepository(RevoLandDbContext context) : base(context, "ChatMessages")
        {
            _chatMessages = context.GetCollection<ChatMessage>("ChatMessages");
        }

        public async Task<ChatMessage?> FindByMsgIdAsync(string mid)
        {
            if (string.IsNullOrEmpty(mid))
                return null;
            var filter = Builders<ChatMessage>.Filter.Eq("payload.msg_id", mid);
            return await _chatMessages
                .Find(filter)
                .FirstOrDefaultAsync();
        }

        public async Task<List<ChatMessage>> GetChatMessagesByConversationIdAsync(string conversationId, QueryChatMessage query)
        {
            int skip = (query.pageNumber - 1) * query.pageSize;

            var filter = Builders<ChatMessage>.Filter.Eq(msg => msg.conversationId, conversationId);

            var messages = await _chatMessages
                .Find(filter)
                .SortByDescending(msg => msg.createdAt)
                .Skip(skip)
                .Limit(query.pageSize)
                .ToListAsync();

            return messages;
        }


    }
}
