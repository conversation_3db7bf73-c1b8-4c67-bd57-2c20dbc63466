﻿using BusinessObjects.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Repositories.Interfaces
{
    public interface ITenantRepository:IGenericRepository<Tenant>
    {
        public Task<Tenant> GetTenantByPhone(string phone);
        Task<bool> IsPhoneExist(string phone);
        Task<bool> IsEmailExist(string email);
    }
}
