﻿using BusinessObjects.Models;
using Microsoft.Extensions.Logging;
using Repositories.Interfaces;
using Services.Interfaces;
using static BusinessObjects.DTOs.CalendarDTOs.GoogleCalendar;
using static BusinessObjects.DTOs.GoogleDTOs.GoogleCredentialsDTOs;

namespace Services.Implements
{
    public class GoogleCredentialService : IGoogleCredentialService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IGoogleCrendentialRepository _googleCrendentialRepository;
        private readonly ILogger<GoogleCredentialService> _logger;
        private readonly ICustomCalendarService _customCalendarService;
        private readonly IGoogleOAuthService _googleOAuthService;

        public GoogleCredentialService(IUnitOfWork unitOfWork, IGoogleCrendentialRepository googleCrendentialRepository, ILogger<GoogleCredentialService> logger, ICustomCalendarService customCalendarService, IGoogleOAuthService googleOAuthService)
        {
            _unitOfWork = unitOfWork;
            _customCalendarService = customCalendarService;
            _googleCrendentialRepository = googleCrendentialRepository;
            _logger = logger;
            _googleOAuthService = googleOAuthService;
        }
        //unused
        public async Task<GoogleCredentialResponse> SaveCredentialsAsync(string userId, SaveGoogleCredentialsRequest request, string? userAgent = null, string? ipAddress = null)
        {
            try
            {
                // ✅ Validate inputs
                if (string.IsNullOrEmpty(userId))
                    throw new ArgumentException("User ID is required", nameof(userId));

                if (request == null)
                    throw new ArgumentNullException(nameof(request));

                if (string.IsNullOrEmpty(request.accessToken))
                    throw new ArgumentException("Access token is required", nameof(request.accessToken));

                _logger.LogInformation($"Saving Google credentials for user: {userId}");

                // Deactivate existing credentials
                var deactivatedCount = await _googleCrendentialRepository.DeactivateByUserIdAsync(userId);
                _logger.LogInformation($"Deactivated {deactivatedCount} existing credentials for user: {userId}");

                var credential = new GoogleCredentialModel
                {
                    userId = userId,
                    accessToken = request.accessToken,
                    refreshToken = request.refreshToken,
                    calendarId = request.calendarId,
                    tokenExpiry = request.tokenExpiry,
                    scope = request.scope,
                    tokenType = request.tokenType,
                    userAgent = userAgent,
                    ipAddress = ipAddress,
                    isActive = true,
                    createdAt = DateTime.UtcNow, // ✅ Add timestamp
                    updatedAt = DateTime.UtcNow
                };

                var savedCredential = await _googleCrendentialRepository.AddAsync(credential);
                await _unitOfWork.SaveAsync(); // ✅ Ensure changes are saved

                _logger.LogInformation($"Google credentials saved successfully for user: {userId}, ID: {savedCredential.id}");

                return ToGoogleCredentialResponse(savedCredential);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error saving Google credentials for user: {userId}");
                throw;
            }
        }

        public async Task<GoogleCredentialResponse> SaveOrUpdateCredentialsAsync(
                                                                                string userId,
                                                                                SaveGoogleCredentialsRequest request,
                                                                                string? userAgent = null,
                                                                                string? ipAddress = null)
        {
            try
            {
                if (string.IsNullOrEmpty(userId))
                    throw new ArgumentException("User ID is required", nameof(userId));

                if (request == null)
                    throw new ArgumentNullException(nameof(request));

                if (string.IsNullOrEmpty(request.accessToken))
                    throw new ArgumentException("Access token is required", nameof(request.accessToken));

                _logger.LogInformation($"Saving Google credentials for user: {userId}");

                // ✅ Map to CreateCalendarRequest với đầy đủ thông tin
                var createCalendarRequest = new CreateCalendarRequest
                {
                    accessToken = request.accessToken,
                    summary = $"Real Estate Calendar ( RevoLand )", // ✅ Tên calendar có ý nghĩa
                    description = "Calendar for managing real estate appointments and property viewings for RevoLand's users",
                    timeZone = "Asia/Ho_Chi_Minh", // ✅ Timezone phù hợp với VN
                    location = null, // ✅ Optional
                    backgroundColor = "#4285F4", // ✅ Google Blue
                    foregroundColor = "#FFFFFF", // ✅ White text
                    isPublic = false, // ✅ Private calendar
                    shareRules = new List<CalendarShareRule>() // ✅ Empty share rules
                };

                // ✅ Tìm credential hiện có
                var existingCredential = await _googleCrendentialRepository.GetByUserIdAsync(userId);

                GoogleCredentialModel credential;
                string calendarId;



                if (existingCredential != null)
                {
                    // ✅ UPDATE credential hiện có
                    _logger.LogInformation($"Updating existing credential for user: {userId}");

                    // Check if existing calendar is still valid
                    if (!string.IsNullOrEmpty(existingCredential.calendarId) &&
                        existingCredential.calendarId != "primary" &&
                        await _customCalendarService.CalendarExistsAsync(request.accessToken, existingCredential.calendarId))
                    {
                        // Use existing calendar
                        calendarId = existingCredential.calendarId;
                        _logger.LogInformation($"Using existing calendar for user {userId}: {calendarId}");
                    }
                    else
                    {
                        // Create new calendar (existing one might be deleted)
                        calendarId = await _customCalendarService.CreateCalendarAsyncReturnId(createCalendarRequest);
                        _logger.LogInformation($"Created new calendar for user {userId}: {calendarId}");
                    }

                    existingCredential.accessToken = request.accessToken;
                    existingCredential.refreshToken = request.refreshToken ?? existingCredential.refreshToken;
                    existingCredential.calendarId = calendarId;
                    existingCredential.tokenExpiry = request.tokenExpiry;
                    existingCredential.scope = request.scope ?? existingCredential.scope;
                    existingCredential.tokenType = request.tokenType ?? existingCredential.tokenType;
                    existingCredential.userAgent = userAgent;
                    existingCredential.ipAddress = ipAddress;
                    existingCredential.isActive = true;
                    existingCredential.updatedAt = DateTime.UtcNow;
                    existingCredential.lastUsed = DateTime.UtcNow;

                    credential = await _googleCrendentialRepository.UpdateAsync(existingCredential.id, existingCredential);
                }
                else
                {
                    // ✅ CREATE mới nếu chưa có
                    _logger.LogInformation($"Creating new credential for user: {userId}");

                    // Create unique calendar for this user
                    calendarId = await _customCalendarService.CreateCalendarAsyncReturnId(createCalendarRequest);

                    credential = new GoogleCredentialModel
                    {
                        userId = userId,
                        accessToken = request.accessToken,
                        refreshToken = request.refreshToken,
                        calendarId = calendarId,
                        //calendarCreatedAt = DateTime.UtcNow,
                        tokenExpiry = request.tokenExpiry,
                        scope = request.scope,
                        tokenType = request.tokenType,
                        userAgent = userAgent,
                        ipAddress = ipAddress,
                        isActive = true,
                        createdAt = DateTime.UtcNow,
                        updatedAt = DateTime.UtcNow,
                        lastUsed = DateTime.UtcNow
                    };

                    credential = await _googleCrendentialRepository.AddAsync(credential);
                }

                await _unitOfWork.SaveAsync();

                _logger.LogInformation($"Google credentials saved successfully for user: {userId}, Calendar ID: {credential.calendarId}");

                return ToGoogleCredentialResponse(credential);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error saving Google credentials for user: {userId}");
                throw;
            }
        }




        public async Task<GoogleCredentialResponse?> GetCredentialsByUserIdAsync(string userId)
        {
            var credential = await _googleCrendentialRepository.GetActiveByUserIdAsync(userId);
            if (credential == null) return null;

            var response = ToGoogleCredentialResponse(credential);
            response.isTokenValid = await _googleCrendentialRepository.IsTokenValidAsync(userId);

            return response;
        }

        public async Task<GoogleCredentialModel?> GetActiveCredentialsAsync(string userId)
        {
            var credential = await _googleCrendentialRepository.GetActiveByUserIdAsync(userId);
            if (credential != null)
            {
                // Update last used
                await _googleCrendentialRepository.UpdateLastUsedAsync(userId);
            }
            return credential;
        }

        public async Task<bool> DeactivateCredentialsAsync(string userId)
        {
            var result = await _googleCrendentialRepository.DeactivateByUserIdAsync(userId);
            if (result)
            {
                _logger.LogInformation($"Google credentials deactivated for user: {userId}");
            }
            return result;
        }

        public async Task<bool> UpdateCredentialsAsync(string userId, UpdateGoogleCredentialsRequest request)
        {
            var credential = await _googleCrendentialRepository.GetActiveByUserIdAsync(userId);
            if (credential == null) return false;

            if (!string.IsNullOrEmpty(request.calendarId))
                credential.calendarId = request.calendarId;

            if (!string.IsNullOrEmpty(request.scope))
                credential.scope = request.scope;

            credential.updatedAt = DateTime.UtcNow;

            await _googleCrendentialRepository.UpdateAsync(credential.id, credential);

            _logger.LogInformation($"Google credentials updated for user: {userId}");
            return true;
        }

        public async Task<bool> IsTokenValidAsync(string userId)
        {
            return await _googleCrendentialRepository.IsTokenValidAsync(userId);
        }

        public async Task<GoogleCredentialModel?> RefreshTokenAsync(string userId, string newAccessToken, DateTime? newExpiry)
        {
            try
            {
                var result = await _googleCrendentialRepository.RefreshTokenAsync(userId, newAccessToken, newExpiry);
                if (result != null)
                {
                    _logger.LogInformation($"✅ Google token updated in database for user: {userId}");
                }
                else
                {
                    _logger.LogError($"❌ Failed to update token in database for user: {userId}");
                }
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"❌ Error updating token in database for user {userId}");
                throw;
            }
        }


        public async Task<List<GoogleCredentialModel>> GetExpiredTokensAsync()
        {
            return await _googleCrendentialRepository.GetExpiredTokensAsync();
        }

        public async Task<GoogleCredentialModel?> GetValidCredentialsAsync(string userId)
        {
            try
            {
                // ✅ Step 1: Get credentials
                var credentials = await GetActiveCredentialsAsync(userId);
                if (credentials == null)
                {
                    _logger.LogWarning($"❌ No Google credentials found for user {userId}");
                    return null;
                }

                // ✅ Step 2: Check validity
                var isValid = await IsTokenValidAsync(userId);
                if (isValid)
                {
                    _logger.LogInformation($"✅ Valid credentials obtained for user {userId}");
                    return credentials;
                }

                // ✅ Step 3: Refresh if needed
                if (string.IsNullOrEmpty(credentials.refreshToken)) // ✅ Add null check
                {
                    _logger.LogError($"❌ No refresh token available for user {userId}");
                    return null;
                }

                _logger.LogInformation($"🔄 Token invalid for user {userId}, attempting refresh...");

                var refreshedCredentialsResponse = await _googleOAuthService.RefreshAccessTokenAsync(credentials.refreshToken);
                if (refreshedCredentialsResponse != null)
                {
                    // ✅ Calculate new expiry
                    var newExpiry = DateTime.UtcNow.AddSeconds(refreshedCredentialsResponse.ExpiresInSeconds ?? 3600);

                    // ✅ Update credentials in database
                    var updatedCredential = await RefreshTokenAsync(userId, refreshedCredentialsResponse.AccessToken, newExpiry);

                    if (updatedCredential != null)
                    {
                        _logger.LogInformation($"✅ Token refreshed successfully for user {userId}");
                        return updatedCredential;
                    }
                    else
                    {
                        _logger.LogError($"❌ Failed to save refreshed token for user {userId}");
                        return null;
                    }
                }
                else
                {
                    _logger.LogError($"❌ Failed to refresh token for user {userId}");
                    return null;
                }
            }
            catch (InvalidOperationException ex) // ✅ Handle specific OAuth exceptions
            {
                _logger.LogError(ex, $"❌ Invalid refresh token for user {userId}");

                // ✅ Deactivate invalid credentials
                await DeactivateCredentialsAsync(userId);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"❌ Error getting valid credentials for user {userId}");
                return null;
            }
        }



        private GoogleCredentialResponse ToGoogleCredentialResponse(GoogleCredentialModel credential)
        {
            return new GoogleCredentialResponse
            {
                id = credential.id,
                userId = credential.userId,
                calendarId = credential.calendarId,
                tokenExpiry = credential.tokenExpiry,
                scope = credential.scope,
                tokenType = credential.tokenType,
                isActive = credential.isActive,
                createdAt = credential.createdAt,
                updatedAt = credential.updatedAt,
                lastUsed = credential.lastUsed
            };
        }
    }
}
