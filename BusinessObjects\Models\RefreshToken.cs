﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static BusinessObjects.Models.User;
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson;

namespace BusinessObjects.Models
{
    public class RefreshToken
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string id { get; set; }= ObjectId.GenerateNewId().ToString();
        public string userId { get; set; }
        [ForeignKey(nameof(userId))]
        public User user { get; set; }
        public string token { get; set; }
        public string jwtId { get; set; }
        public bool isUsed { get; set; }
        public bool isRevoked { get; set; }
        public DateTime issuedAt { get; set; }
        public DateTime expiredAt { get; set; }
    }
}
