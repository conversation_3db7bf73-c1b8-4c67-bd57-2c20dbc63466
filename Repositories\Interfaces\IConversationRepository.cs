﻿using BusinessObjects.Models;
using BusinessObjects.QueryObject;

namespace Repositories.Interfaces
{
    public interface IConversationRepository : IGenericRepository<Conversation>
    {
        Task<Conversation> GetOrCreateAsync(string platform, string platformUserId);
        Task UpdateLastMessageAsync(string conversationId, string message);

        Task<List<Conversation>> GetAllConversationsAsync(QueryConversation query);
        Task<Conversation> GetByUserIdAsync(string userID);
        Task<Conversation> GetByUserIdIdAndSellerIdAsync(string userId, string sellerId);
        Task<List<Conversation>> GetConversationsByCurrentSellerAsync(string currentSellerId, QueryConversation query);
    }
}
