﻿using BusinessObjects.Models;

namespace Repositories.Interfaces
{
    public interface IUnitOfWork
    {
        IAmenityRepository Amenities { get; }
        ILocationRepository Locations { get; }
        IOwnerRepository Owners { get; }
        ISwipeRepository Swipes { get; }
        IMatchingRepository Matchings { get; }
        IPriceDetailRepository PriceDetails { get; }
        IPropertyRepository Properties { get; }
        IPropertyDetailRepository PropertyDetails { get; }
        ITransactionHistoryRepository TransactionHistories { get; }
        ITenantRepository Tenants { get; }
        ILeadRepository Leads { get; }
        ILandlordRepository Landlords { get; }
        ISearchRepository Search { get; }
        IAppointmentRepository Appointments { get; }
        IChatMessageRepository ChatMessages { get; }
        IConversationRepository Conversations { get; }
        ICustomerProfileRepository CustomerProfiles { get; }
        IZaloTokenRepository ZaloTokens { get; }
        IAttachmentRepository Attachments { get; }
        IContractRepository Contracts { get; }
        IRentalContractReminderRepository RentalContractReminder { get; }
        IRentalContractNotificationRepository RentalContractNotification { get; }
        IDealRepository Deals { get; }
        IFavoritePropertyRepository FavoriteProperties { get; }
        IDealsActivityLogRepository DealsActivityLogs { get; }
        IGenericRepository<User> Users { get; }
        IGenericRepository<RefreshToken> RefreshTokens { get; }
        IChatSessionRepository ChatSessions { get; }
        IGenericRepository<AnonymousSession> AnonymousSessions { get; }
        ISurveyRepository Surveys { get; }

        IGoogleCrendentialRepository GoogleCredentials { get; }

        IPropertyViewHistoryRepository PropertyViewHistories { get; }

        IDraftPropertyRepository DraftProperties { get; }
        IBuyerRepository Buyers { get; }
        IGenericRepository<Company> Companies { get; }
        ITeamRepository Teams { get; }
        IAgentRepository Agents { get; }
        IAgentInviteRepository AgentInvites { get; }
        IAgentCompanyLinkRepository AgentCompanyLinks { get; }
        IAgentActivityLogRepository AgentActivityLogs { get; }
        IRoommatePreferenceRepository RoommatePreferences { get; }
        ICollectionPropertyRepository CollectionProperties { get; }
        Task SaveAsync();

    }
}
