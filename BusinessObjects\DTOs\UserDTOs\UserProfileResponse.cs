﻿using BusinessObjects.Models;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.DTOs.UserDTOs
{
    public class UserProfileResponse
    {
        public string userName { get; set; }
     
        public string fullName { get; set; }
        
        [BsonElement("email")]
        public string email { get; set; }
        
        [BsonElement("phoneNumber")]
        public string phoneNumber { get; set; }
        public string? avatar { get; set; }
   
        public Status? status { get; set; }

        public UserRole role { get; set; }
        public string? about { get; set; }
        
        public DateTime? birthdate { get; set; }
       
        public DateTime joinedAt { get; set; }
    }
}
