using BusinessObjects.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Services.Interfaces
{
    public interface IRentalContractReminderService
    {
        Task CheckExpiringContractsAsync(int daysBeforeExpiry);
        Task CreateReminderAsync(Contract contract, string userId, string message, DateTime remindAt);
        Task<List<RentalContractReminder>> GetRemindersToNotifyAsync();
        Task SendReminderAsync(RentalContractReminder reminder);
        Task<List<RentalContractReminder>> GetRemindersByUserIdAsync(string userId);
        Task CheckRentDueRemindersAsync(int daysBeforeDue);
        Task ResetRentRemindersAsync();
    }
} 