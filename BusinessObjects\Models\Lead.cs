using BusinessObjects.Validations;
using BusinessObjects.DTOs.LeadDTOs;
using BusinessObjects.Validations;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System.ComponentModel.DataAnnotations;

namespace BusinessObjects.Models
{
    public class Lead
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        [BsonElement("leadId")]
        public string id { get; set; } = ObjectId.GenerateNewId().ToString();

        [Required]
        public string? name { get; set; }

        [EmailAddress]
        [Required]
        public string? email { get; set; }

        [Required]
        [BsonElement("phone")]
        [PhoneVN]
        public string? phone { get; set; }

        public string? address { get; set; }
        [BsonRepresentation(BsonType.String)]
        public LeadSource source { get; set; }

        [BsonRepresentation(BsonType.String)]
        public LeadScore? score { get; set; }

        public List<AssignedTo> assignedTo { get; set; } = new();
        public DateTime createdAt { get; set; } = DateTime.UtcNow;
        public string? userId { get; set; }

        [BsonElement("favoriteProperties")]
        public List<AddFavoritePropertyRequest> favoriteProperties { get; set; } = new List<AddFavoritePropertyRequest>();

        public DateTime updatedAt { get; set; } = DateTime.UtcNow;

    }
    public enum LeadSource
    {
        Form,
        Message,
        Appointment,
        ExternalSeller,
        Admin,
        Zalo,
        Facebook,
        Web
    }

    public enum LeadScore
    {
        Hot,
        Warm,
        Cold
    }

    public class AssignedTo
    {
        public string id { get; set; }
        public DateOnly assignedAt { get; set; } = DateOnly.FromDateTime(DateTime.UtcNow);
    }

}
