﻿using BusinessObjects.DTOs;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Services.Implements;
using Services.Interfaces;

namespace Controllers.Controllers
{
    [ApiController]
    [Route("api/webhook/zalo")]
    public class ZaloWebhookController : ControllerBase
    {
        private readonly IChatMessageService _messageService;

        public ZaloWebhookController(IChatMessageService messageService)
        {
            _messageService = messageService;
        }

        [HttpPost]
        public async Task<IActionResult> ReceiveWebhook([FromBody] ZaloWebhookPayload payload)
        {
                await _messageService.HandleIncomingZaloMessageAsync(payload);
            return Ok();
        }

        [HttpGet]
        public async Task<IActionResult> ZaloOAuthCallback([FromQuery] string code, [FromQuery] string state)
        {
            return Ok($"Code: {code}");
        }
    }
}
