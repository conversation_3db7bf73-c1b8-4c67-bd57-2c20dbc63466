﻿using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.Models
{
    public class Tenant
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        [BsonElement("tenantId")]
        public string id { get; set; } = ObjectId.GenerateNewId().ToString();

        [Required]
        public string? name { get; set; }

        [Required]
        [BsonElement("phone")]
        public string? phone { get; set; }

        [EmailAddress]
        public string? email { get; set; }
        [Required]
        public string? address { get; set; }
        [Required]
        public List<string> idVerification { get; set; }

        [Required]
        public DateTime? leaseStartDate { get; set; }

        [Required]
        public DateTime? leaseEndDate { get; set; }

      public DateTime createdAt { get; set; }

       public DateTime updatedAt { get; set; }
    }
}
