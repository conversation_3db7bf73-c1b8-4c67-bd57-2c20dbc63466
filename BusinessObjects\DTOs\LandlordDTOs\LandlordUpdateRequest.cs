﻿using BusinessObjects.Validations;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.DTOs.LandlordDTOs
{
    public class LandlordUpdateRequest
    {
        [ValidName]

        public string? name { get; set; }
        [EmailAddress]
        public string? email { get; set; }
        [PhoneVN]
        public string? phone { get; set; }
        public string? address { get; set; }
    }
}
