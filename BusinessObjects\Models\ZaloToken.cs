﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.Models
{
    public class ZaloToken
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string id { get; set; }
        [Required]
        [MaxLength(500)]
        public string accessToken { get; set; } = string.Empty;

        [Required]
        [MaxLength(500)]
        public string refreshToken { get; set; } = string.Empty;

        [Required]
        public DateTime accessTokenExpiresAt { get; set; } // thời điểm token hết hạn (UTC)

        [Required]
        public DateTime refreshTokenExpiresAt { get; set; } // thời điểm refresh token hết hạn (UTC)

        public DateTime createdAt { get; set; } = DateTime.UtcNow; // thời điểm tạo bản ghi

        public DateTime updatedAt { get; set; } = DateTime.UtcNow; // thời điểm update bản ghi
    }
}
