﻿using BusinessObjects.DTOs.AgentDTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Services.Interfaces
{
	public interface IAgentService
	{
		Task<IEnumerable<AgentResponse>> GetAllAgentsAsync();
		Task<AgentResponse> GetAgentByIdAsync(string id);
		Task AssignAgentToTeam(AddAgentToTeamRequest request);
		Task DeleteAgentFromTeam(string teamId, string agentId);
		Task<AgentResponse> CreateAgentAsync(CreateAgentRequest request);
		Task<List<AgentResponse>> GetAllAgentsByCompanyIdAsync(string companyId);
	}
}
