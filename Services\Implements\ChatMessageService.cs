﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.ChatMessageDTOs;
using BusinessObjects.DTOs.FacebookDTOs;
using BusinessObjects.Models;
using BusinessObjects.QueryObject;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.SignalR;
using MongoDB.Bson;
using Repositories.Interfaces;
using Services.Implements.HubService;
using Services.Interfaces;
using System.Security.Claims;
using System.Text.Json;

namespace Services.Implements
{
    public class ChatMessageService : IChatMessageService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IZaloApiService _zaloApi;
        private readonly IFacebookMessengerService _facebookMessengerService;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IHubContext<ChatMessageHub> _hubContext;
        private readonly ICustomerProfileService _customerProfileService;
        private readonly IRedisService _redisService;
        private readonly IConversationService _conversationService;

        public ChatMessageService(IUnitOfWork unitOfWork, IZaloApiService zaloApi, IFacebookMessengerService facebookMessengerService, IHttpContextAccessor httpContextAccessor, IHubContext<ChatMessageHub> hubContext, ICustomerProfileService customerProfileService, IRedisService redisService, IConversationService conversationService)
        {
            _unitOfWork = unitOfWork;
            _zaloApi = zaloApi;
            _facebookMessengerService = facebookMessengerService;
            _httpContextAccessor = httpContextAccessor;
            _hubContext = hubContext;
            _customerProfileService = customerProfileService;
            _redisService = redisService;
            _conversationService = conversationService;
        }

        public async Task<List<ChatMessageResponse>> GetChatMessagesByConversationIdAsync(string conversationId, QueryChatMessage query)
        {
            var responses = new List<ChatMessageResponse>();
            var conversation = await _unitOfWork.Conversations.GetByIdAsync(conversationId) ?? throw new Exception("Không tìm thấy conversation");
            if (conversation.platform == "system")
            {
                var chatMessages = await _unitOfWork.ChatMessages.GetChatMessagesByConversationIdAsync(conversationId, query);
                responses = chatMessages.Select(msg => new ChatMessageResponse
                {
                    id = msg.id,
                    senderId = msg.senderId,
                    direction = msg.direction,
                    replyToMessageId = msg.replyToMessageId,
                    content = msg.content,
                    createdAt = msg.createdAt,
                    isDeleted = msg.isDeleted,
                    attachments = msg.attachments.Select(attachment => new ChatMessageAttachments
                    {
                        fileName = attachment.fileName,
                        type = attachment.fileType,
                        url = attachment.fileUrl
                    }).ToList(),
                }).ToList();
            }
            else if (conversation.platform == "facebook")
            {
                var chatMessages = await _unitOfWork.ChatMessages.GetChatMessagesByConversationIdAsync(conversationId, query);
                responses = chatMessages.Select(msg => new ChatMessageResponse
                {
                    id = msg.id,
                    senderId = msg.senderId,
                    direction = msg.direction,
                    replyToMessageId = msg.replyToMessageId,
                    content = msg.content,
                    createdAt = msg.createdAt,
                    isDeleted = msg.isDeleted,
                    attachments = msg.payload?
        .GetValue("attachments")?
        .AsBsonArray
        .Select(a =>
        {
            var attDoc = a.AsBsonDocument;
            var type = attDoc.GetValue("type").AsString;
            var payloadDoc = attDoc.GetValue("payload").AsBsonDocument;
            var url = payloadDoc.GetValue("url").AsString;
            // Meta gọi tên trường trong fallback là "title"
            var title = payloadDoc.Contains("title")
                                ? payloadDoc.GetValue("title").AsString
                                : null;

            return new ChatMessageAttachments
            {
                type = type,
                url = url,
                fileName = title
            };
        })
                .ToList()

                }).ToList();
            }
            else if (conversation.platform == "zalo")
            {
                //chua sua xong phan zalo
                var chatMessages = await _unitOfWork.ChatMessages.GetChatMessagesByConversationIdAsync(conversationId, query);
                responses = chatMessages.Select(msg => new ChatMessageResponse
                {
                    id = msg.id,
                    senderId = msg.senderId,
                    direction = msg.direction,
                    replyToMessageId = msg.replyToMessageId,
                    content = msg.content,
                    createdAt = msg.createdAt,
                    isDeleted = msg.isDeleted,
                    attachments = msg.attachments.Select(attachment => new ChatMessageAttachments
                    {
                        fileName = attachment.fileName,
                        type = attachment.fileType,
                        url = attachment.fileUrl
                    }).ToList(),

                }).ToList();
            }
            return responses;
        }
        public async Task SendFromCrmToZaloAsync(string customerProfileId, string text, string conversationId)
        {

            var userId = _httpContextAccessor.HttpContext?.User?.FindFirstValue("UserID");
            var customer = await _unitOfWork.CustomerProfiles.GetByIdAsync(customerProfileId);
            if (customer == null)
                throw new Exception("Customer profile not found.");
            await _zaloApi.SendTextMessageAsync(customer.platformUserId, text);
            var message = new ChatMessage
            {
                senderId = userId,
                recipientId = customerProfileId,
                platformUserId = customer.platformUserId,
                content = text,
                messageType = "text",
                direction = "outbound",
                platform = "zalo",
                conversationId = conversationId,
                createdAt = DateTime.UtcNow
            };

            await _unitOfWork.ChatMessages.AddAsync(message);
            await _hubContext.Clients.Group(conversationId)
                .SendAsync("ReceiveMessage", new
                {
                    conversationId = conversationId,
                    content = message.content,
                    senderId = message.senderId,
                    direction = message.direction,
                    platform = message.platform,
                    timestamp = message.createdAt
                });
        }

        public async Task SendFromCrmToMessengerAsync(string customerProfileId, string text, string conversationId)
        {
            var platformUserProfile = await _unitOfWork.CustomerProfiles.GetByIdAsync(customerProfileId);

            await _facebookMessengerService.SendTextMessageAsync(platformUserProfile.platformUserId, text);
            //var message = new ChatMessage
            //{
            //    senderId = null,
            //    recipientId = userId,
            //    platformUserId = userId,
            //    content = text,
            //    messageType = "text",
            //    direction = "outbound",
            //    platform = "messenger",
            //    conversationId = conversationId,
            //    createdAt = DateTime.UtcNow
            //};
            //await _unitOfWork.ChatMessages.AddAsync(message);
            //// Push realtime
            await _hubContext.Clients.Group(conversationId)
                .SendAsync("ReceiveMessage", new
                {
                    conversationId = conversationId,
                    content = text,
                    direction = "outbound",
                    platform = "messenger",
                    timestamp = DateTime.UtcNow
                });
        }


        public async Task HandleIncomingZaloMessageAsync(ZaloWebhookPayload payload)
        {
            string platform = "zalo";
            string userId = payload.sender.id;
            string direction = userId == "1655735604103741820" ? "outbound" : "inbound";
            var id = direction == "outbound" ? payload.recipient.id : userId;

            var customer = await _customerProfileService.GetOrCreateZaloProfileAsync(id);
            var conversation = await _unitOfWork.Conversations.GetOrCreateAsync(platform, customer.id);

            string messageType = "text";
            string? content = payload.message?.text;
            BsonDocument? attachmentsPayload = null;

            if (payload.message?.attachments is { Count: > 0 } attachments)
            {
                messageType = attachments[0].type ?? "unknown";
                content = payload.message?.text;

                var payloadObj = new
                {
                    attachments,
                    msg_id = payload.message.msgId
                };
                attachmentsPayload = BsonDocument.Parse(JsonSerializer.Serialize(payloadObj));
            }

            var timestamp = long.TryParse(payload.timestamp, out var ts)
                ? DateTimeOffset.FromUnixTimeMilliseconds(ts).UtcDateTime
                : DateTime.UtcNow;
            string redisKey = $"zalo_batch_group:{userId}";
            string? groupId = await _redisService.GetStringAsync(redisKey);
            if (string.IsNullOrEmpty(groupId))
            {
                groupId = $"zalo_{Guid.NewGuid()}";
                await _redisService.SetStringAsync(redisKey, groupId, TimeSpan.FromSeconds(2));
            }
            var message = new ChatMessage
            {
                senderId = customer.id,
                recipientId = payload.recipient.id,
                platformUserId = userId,
                content = content,
                messageType = messageType,
                direction = direction,
                conversationId = conversation.id,
                platform = platform,
                createdAt = timestamp,
                payload = attachmentsPayload,
                groupId = groupId
            };

            await _unitOfWork.ChatMessages.AddAsync(message);

            string lastMessagePreview = messageType == "text"
                ? content ?? "[text]"
                : $"[{messageType.ToUpper()}]";

            await _unitOfWork.Conversations.UpdateLastMessageAsync(conversation.id, lastMessagePreview);

            await _hubContext.Clients.Group(conversation.id).SendAsync("ReceiveMessage", new
            {
                conversationId = conversation.id,
                content = message.content,
                senderId = message.senderId,
                direction = message.direction,
                platform = message.platform,
                timestamp = message.createdAt,
                messageType = message.messageType,
                payload = message.payload,
                groupId = message.groupId
            });
        }

        public async Task HandleIncomingMessengerMessageAsync(MessengerWebhookPayload payload)
        {
            var mid = payload.message.mid;
            if (!string.IsNullOrEmpty(mid))
            {
                var exists = await _unitOfWork.ChatMessages.FindByMsgIdAsync(mid);
                if (exists != null)
                    return;
            }
            string platform = "messenger";

            var isEcho = payload.message.isEcho == true;
            var direction = isEcho ? "outbound" : "inbound";
            var cusomerIdProfile = isEcho ? payload.recipient.id : payload.sender.id;

            var fromId = payload.sender.id;
            var toId = payload.recipient.id;

            string messageType = "text";
            string? content = payload.message?.text;
            string lastMessagePreview = messageType == "text"
               ? content ?? "[image]"
               : $"[{messageType.ToUpper()}]";

            BsonDocument? attachmentsPayload = null;


            if (payload.message?.attachments is { Count: > 0 } attachments)
            {
                messageType = attachments[0].type ?? "unknown";
                content = payload.message?.text;
                var payloadObj = new
                {
                    attachments,
                    msg_id = payload.message.mid
                };
                attachmentsPayload = BsonDocument.Parse(JsonSerializer.Serialize(payloadObj));
            }
            else
            {
                attachmentsPayload = new BsonDocument
                {
                    { "msg_id", payload.message.mid }
                };
            }

            // 1. Tạo hoặc cập nhật thông tin khách hàng
            CustomerProfile customer = await _customerProfileService.GetOrCreateMessengerProfileAsync(cusomerIdProfile);

            // 2. Tạo hoặc lấy cuộc hội thoại đang mở
            var conversation = await _unitOfWork.Conversations.GetOrCreateAsync(platform, customer.id);

            var message = new ChatMessage
            {
                senderId = fromId,
                recipientId = toId,
                platformUserId = fromId,
                content = content,
                messageType = messageType,
                direction = direction,
                conversationId = conversation.id,
                platform = "messenger",
                createdAt = DateTimeOffset.FromUnixTimeMilliseconds(payload.timestamp).UtcDateTime,
                payload = attachmentsPayload
            };
            await _unitOfWork.ChatMessages.AddAsync(message);
            await _unitOfWork.Conversations.UpdateLastMessageAsync(conversation.id, lastMessagePreview);

            // 6. Broadcast realtime qua SignalR
            //    => map payload về simple DTO
            var attachmentsDto = new List<object>();
            if (message.payload != null
                && message.payload.Contains("attachments"))
            {
                var arr = message.payload["attachments"].AsBsonArray;
                attachmentsDto = arr.Select(a =>
                {
                    var doc = a.AsBsonDocument;
                    var payloadDoc = doc["payload"].AsBsonDocument;
                    return new
                    {
                        type = doc["type"].AsString,
                        url = payloadDoc.GetValue("url", BsonNull.Value).AsString,
                        title = payloadDoc.Contains("title")
                                      ? payloadDoc["title"].AsString
                                      : null,
                        msgId = message.payload.GetValue("msg_id", BsonNull.Value).AsString
                    };
                }).ToList<object>();
            }

            await _hubContext.Clients.Group(conversation.id)
                .SendAsync("ReceiveMessage", new
                {
                    conversationId = conversation.id,
                    content = message.content,
                    senderId = message.senderId,
                    direction = message.direction,
                    platform = message.platform,
                    timestamp = message.createdAt,
                    messageType = message.messageType,
                    attachments = attachmentsDto
                });
        }


        public async Task<(bool isNewConversation, string userId, string conversationId)> HandleIncomingSystemMessageAsync(ChatMessageCreateRequest request)
        {
            var httpContext = _httpContextAccessor.HttpContext;
            var userId = httpContext?.User?.FindFirstValue("UserID");
            var role = httpContext?.User?.FindFirst(ClaimTypes.Role)?.Value ?? string.Empty;
            var direction = (role == "Admin") ? "outbound" : "inbound";
            Conversation conversation = null;
            bool isNewConversation = false;

            //check length of request.attachmentIds
            if (request.content == null && (request.attachmentIds == null || request.attachmentIds.Count() == 0))
            {
                throw new BadHttpRequestException("Lỗi khi gửi tin nhắn");
            }

            if (string.IsNullOrEmpty(userId))
            {
                if (string.IsNullOrEmpty(request.senderId) && string.IsNullOrEmpty(request.conversationId))
                {
                    (userId, conversation) = await CreateAnonymousConversationAsync(request.content);
                    isNewConversation = true;
                }
                else if (!string.IsNullOrEmpty(request.senderId) && !string.IsNullOrEmpty(request.conversationId))
                {
                    userId = request.senderId;
                    var customerProfile = await _unitOfWork.CustomerProfiles.GetByIdAsync(userId) ?? throw new Exception("Customer profile not found");
                    conversation = await _unitOfWork.Conversations.GetByIdAsync(request.conversationId);
                    if (conversation == null)
                    {
                        throw new Exception("Conversation not found");
                    }
                    conversation.lastMessage = request.content;
                    conversation.lastUpdated = DateTime.UtcNow;
                    await _unitOfWork.Conversations.UpdateAsync(conversation.id, conversation);
                }
                else
                {
                    throw new Exception("Invalid request. Either senderId or conversationId must be provided.");
                }
            }
            else
            {
                if (role == "User")
                {
                    conversation = await _unitOfWork.Conversations.GetByUserIdAsync(userId);
                    if (conversation == null)
                    {
                        conversation = new Conversation
                        {
                            id = ObjectId.GenerateNewId().ToString(),
                            userId = userId,
                            platform = "system",
                            lastMessage = request.content,
                            lastUpdated = DateTime.UtcNow
                        };
                        await _unitOfWork.Conversations.AddAsync(conversation);
                        isNewConversation = true;
                    }
                    else
                    {
                        conversation.lastMessage = request.content;
                        conversation.lastUpdated = DateTime.UtcNow;
                        await _unitOfWork.Conversations.UpdateAsync(conversation.id, conversation);
                    }
                }
                else
                {
                    conversation = await _unitOfWork.Conversations.GetByIdAsync(request.conversationId);
                    if (conversation == null)
                    {
                        throw new Exception("Conversation not found");
                    }
                    conversation.lastMessage = request.content;
                    conversation.lastUpdated = DateTime.UtcNow;
                    await _unitOfWork.Conversations.UpdateAsync(conversation.id, conversation);
                }
            }

            var message = new ChatMessage
            {
                senderId = userId,
                platform = "system",
                recipientId = role == "Admin" ? request.recipientId : null,
                platformUserId = userId,
                content = request.content,
                messageType = request.messageType,
                conversationId = conversation.id,
                direction = direction,
                replyToMessageId = request.replyToMessageId,
                createdAt = DateTime.UtcNow
            };

            //FIXED: Changed from request.attachmentIds.Any() to request.attachmentIds?.Any() == true
            if (request.attachmentIds?.Any() == true)
            {
                message.attachments = new List<Attachment>();
                foreach (var attachmentId in request.attachmentIds)
                {
                    var attachment = await _unitOfWork.Attachments.GetByIdAsync(attachmentId);
                    if (attachment != null)
                    {
                        message.attachments.Add(new Attachment
                        {
                            id = attachment.id,
                            fileType = attachment.fileType,
                            fileUrl = attachment.fileUrl,
                            fileName = attachment.fileName
                        });
                    }
                }
            }

            string redisKey = $"system_batch_group:{userId}";
            string? groupId = await _redisService.GetStringAsync(redisKey);
            if (string.IsNullOrEmpty(groupId))
            {
                groupId = $"system_{Guid.NewGuid()}";
                await _redisService.SetStringAsync(redisKey, groupId, TimeSpan.FromSeconds(2));
            }

            message.groupId = groupId;

            var newMessage = await _unitOfWork.ChatMessages.AddAsync(message);


            await _hubContext.Clients.Group(conversation.id).SendAsync("ReceiveMessage", new
            {
                messageId = newMessage.id,
                conversationId = conversation.id,
                replyToMessageId = message.replyToMessageId,
                content = message.content,
                senderId = message.senderId,
                direction = message.direction,
                platform = message.platform,
                timestamp = message.createdAt,
                attachments = message.attachments?.Select(a => new
                {
                    a.id,
                    a.fileType,
                    a.fileUrl,
                    a.fileName
                }).ToList(),
            });

            return (isNewConversation, userId, conversation.id);
        }


        private async Task<(string userId, Conversation conversation)> CreateAnonymousConversationAsync(string content)
        {
            var customerProfile = new CustomerProfile
            {
                id = ObjectId.GenerateNewId().ToString(),
                platform = "system",
                lastInteraction = DateTime.UtcNow,
                isAnonymous = true
            };
            await _unitOfWork.CustomerProfiles.AddAsync(customerProfile);

            var conversation = new Conversation
            {
                id = ObjectId.GenerateNewId().ToString(),
                userId = customerProfile.id,
                platform = "system",
                lastMessage = content,
                lastUpdated = DateTime.UtcNow
            };
            await _unitOfWork.Conversations.AddAsync(conversation);

            return (customerProfile.id, conversation);
        }

        public async Task SendFromCrmToSystemAsync(ChatMessageCreateRequest request)
        {
            var userId = _httpContextAccessor.HttpContext?.User?.FindFirstValue("UserID");
            var message = new ChatMessage
            {
                senderId = userId,
                platform = "system",
                platformUserId = userId,
                content = request.content,
                messageType = request.messageType,
                conversationId = request.conversationId,
                direction = "outbound",
                replyToMessageId = request.replyToMessageId
            };

            if (request.attachmentIds.Any())
            {
                foreach (var attachmentId in request.attachmentIds)
                {
                    var attachment = await _unitOfWork.Attachments.GetByIdAsync(attachmentId);
                    if (attachment != null)
                    {
                        message.attachments.Add(new Attachment
                        {
                            id = attachment.id,
                            fileType = attachment.fileType,
                            fileUrl = attachment.fileUrl,
                            fileName = attachment.fileName
                        });
                    }
                }
            }

            var newMessage = await _unitOfWork.ChatMessages.AddAsync(message);

            await _hubContext.Clients
                .Group(request.conversationId)
                .SendAsync("ReceiveMessage", new
                {
                    messageId = newMessage.id,
                    conversationId = request.conversationId,
                    content = message.content,
                    senderId = message.senderId,
                    direction = message.direction,
                    platform = message.platform,
                    timestamp = DateTime.UtcNow,
                    replyToMessageId = message.replyToMessageId,
                    attachments = message.attachments?.Select(a => new
                    {
                        a.id,
                        a.fileType,
                        a.fileUrl,
                        a.fileName
                    }).ToList()
                });
        }

        public async Task DeleteByIdAsync(string id)
        {
            var userId = _httpContextAccessor.HttpContext?.User?.FindFirstValue("UserID");
            var message = await _unitOfWork.ChatMessages.GetByIdAsync(id);
            if (message == null)
            {
                throw new Exception("Message not found.");
            }
            if (message.senderId != userId)
            {
                throw new UnauthorizedAccessException("You do not have permission to delete this message.");
            }
            message.isDeleted = true;
            await _unitOfWork.ChatMessages.UpdateAsync(id, message);
            // Push realtime
            await _hubContext.Clients.Group(message.conversationId)
                .SendAsync("MessageDeleted", new { messageId = id, conversationId = message.conversationId });
        }

        public async Task PinMessageAsync(string messageId, string conversationId)
        {
            //var userId = _httpContextAccessor.HttpContext?.User?.FindFirstValue("UserID");
            var message = await _unitOfWork.ChatMessages.GetByIdAsync(messageId);
            if (message == null)
            {
                throw new Exception("Message not found.");
            }
            //if (message.senderId != senderId)
            //{
            //    throw new UnauthorizedAccessException("You do not have permission to pin this message.");
            //}
            var conversation = await _unitOfWork.Conversations.GetByIdAsync(conversationId);
            if (conversation == null)
            {
                throw new Exception("Conversation not found.");
            }
            if (conversation.pinMessages.Any(m => m.id == messageId))
            {
                throw new Exception("Message is already pinned.");
            }
            // Add message to pinned messages
            conversation.pinMessages.Add(message);
            // Update conversation
            await _unitOfWork.Conversations.UpdateAsync(conversationId, conversation);
            await _unitOfWork.ChatMessages.UpdateAsync(messageId, message);
            // Push realtime
            await _hubContext.Clients.Group(conversationId)
                .SendAsync("MessagePinned", new { messageId, conversationId });
        }

        public async Task UnpinMessageAsync(string messageId, string conversationId)
        {

            var conversation = await _unitOfWork.Conversations.GetByIdAsync(conversationId); ;
            var message = await _unitOfWork.ChatMessages.GetByIdAsync(messageId);
            if (conversation == null)
            {
                throw new Exception("Conversation not found.");
            }
            if (message == null)
            {
                throw new Exception("Message not found.");
            }
            if (!conversation.pinMessages.Any(m => m.id == messageId))
            {
                throw new Exception("Message is not pinned.");
            }
            // Remove message from pinned messages
            conversation.pinMessages.RemoveAll(m => m.id == messageId);
            // Update conversation
            await _unitOfWork.Conversations.UpdateAsync(conversationId, conversation);
            await _unitOfWork.ChatMessages.UpdateAsync(messageId, message);
            // Push realtime
            await _hubContext.Clients.Group(conversationId)
                .SendAsync("MessageUnpinned", new { messageId, conversationId });
        }


        public async Task CreateMessageAsync(string senderId, ChatMessageCreateRequest request)
        {
            string userId = "", sellerId = "";
            if (request.direction == "inbound")
            {
                userId = senderId;
                sellerId = request.recipientId;
            }
            else if (request.direction == "outbound")
            {
                userId = request.recipientId;
                sellerId = senderId;
            }
            else
            {
                throw new Exception("Có lỗi xảy ra khi kiểm tra conversation");
            }
            var foundUser = await _unitOfWork.Users.GetByIdAsync(userId) ?? throw new Exception("Không tìm thấy người gửi tin nhắn");
            var foundSeller = await _unitOfWork.Users.GetByIdAsync(sellerId) ?? throw new Exception("Không tìm thấy người nhận tin nhắn");
            Conversation foundConversation = await _unitOfWork.Conversations.GetByUserIdIdAndSellerIdAsync(userId, sellerId);

            if (foundConversation == null)
            {
                foundConversation = await _conversationService.CreateConversationAsync(userId, sellerId, request.attachmentIds != null && request.attachmentIds.Any() ? "[FILE]" : request.content);
            }

            var newMessage = new ChatMessage
            {
                conversationId = foundConversation.id,
                messageType = request.messageType,
                direction = request.direction,
                senderId = senderId,
                recipientId = request.recipientId,
                content = request.content,
                replyToMessageId = request.replyToMessageId,
            };

            if (request.attachmentIds?.Any() == true)
            {
                newMessage.attachments = new List<Attachment>();
                foreach (var attachmentId in request.attachmentIds)
                {
                    var attachment = await _unitOfWork.Attachments.GetByIdAsync(attachmentId);
                    if (attachment != null)
                    {
                        newMessage.attachments.Add(new Attachment
                        {
                            id = attachment.id,
                            fileType = attachment.fileType,
                            fileUrl = attachment.fileUrl,
                            fileName = attachment.fileName
                        });
                    }
                    newMessage.content = "[FILE]";
                }
            }

            string redisKey = $"system_batch_group:{userId}";
            string? groupId = await _redisService.GetStringAsync(redisKey);
            if (string.IsNullOrEmpty(groupId))
            {
                groupId = $"system_{Guid.NewGuid()}";
                await _redisService.SetStringAsync(redisKey, groupId, TimeSpan.FromSeconds(2));
            }

            newMessage.groupId = groupId;

            var message = await _unitOfWork.ChatMessages.AddAsync(newMessage);

            await _hubContext.Clients.Group(foundConversation.id).SendAsync("ReceiveMessage", new
            {
                messageId = message.id,
                conversationId = foundConversation.id,
                content = message.content,
                senderId = message.senderId,
                direction = message.direction,
                timestamp = DateTime.UtcNow,
                replyToMessageId = message.replyToMessageId,
                attachments = message.attachments?.Select(a => new
                {
                    a.id,
                    a.fileType,
                    a.fileUrl,
                    a.fileName
                }).ToList()
            });
        }

    }
}
