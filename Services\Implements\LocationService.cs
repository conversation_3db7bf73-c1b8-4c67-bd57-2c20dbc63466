﻿using BusinessObjects.Models;
using Repositories.Interfaces;
using Services.Interface;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Services.Implements
{
    public class LocationService : ILocationService
    {
        private readonly IUnitOfWork _unitOfWork;

        public LocationService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<Location>> GetAllLocationsAsync()
        {
            return await _unitOfWork.Locations.GetAllAsync();
        }

        public async Task<Location> GetLocationByIdAsync(string id)
        {
            return await _unitOfWork.Locations.GetByIdAsync(id);
        }

        public async Task<Location> AddLocationAsync(Location location)
        {
            await _unitOfWork.Locations.AddAsync(location);
            await _unitOfWork.SaveAsync();
            return location;
        }

        public async Task<Location> UpdateLocationAsync(string id, Location location)
        {
            await _unitOfWork.Locations.UpdateAsync(id, location);
            await _unitOfWork.SaveAsync();
            return location;
        }

        public async Task<bool> DeleteLocationAsync(string id)
        {
            await _unitOfWork.Locations.DeleteAsync(id);
            await _unitOfWork.SaveAsync();
            return true;
        }
    }
}
