﻿using BusinessObjects.DTOs;
using Microsoft.AspNetCore.Mvc;
using Services.Interfaces;

namespace Controllers.Controllers
{
    [Route("api/attachments")]
    [ApiController]
    public class AttachmentController : ControllerBase
    {
        private readonly IAttachmentService _attachmentService;

        public AttachmentController(IAttachmentService attachmentService)
        {
            _attachmentService = attachmentService;
        }

        private ApiResponse CreateResponse(int code, bool status, string message, object data = null)
        {
            return new ApiResponse { code = code, status = status, message = message, data = data };
        }
        [HttpPost]
        public async Task<IActionResult> UploadAttachmentsAsync(List<IFormFile> files)
        {
            try
            {
                if (files == null || files.Count == 0)
                {
                    return BadRequest("Không tìm thấy tệp nào");
                }

                var attachments = await _attachmentService.UploadAttachmentsAsync(files);

                return Ok(CreateResponse(201, true, "Thành công", attachments));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(400, false, "Thất bại: " + ex.Message, null));
            }

        }

        [HttpPost("property/floor-plan")]
        public async Task<IActionResult> UploadFloorPlanAsync(List<IFormFile> files)
        {
            try
            {
                if (files == null || files.Count == 0)
                {
                    return BadRequest("Không tìm thấy tệp nào.");
                }
                var attachments = await _attachmentService.UploadFloorPlanAsync(files);
                return Ok(CreateResponse(201, true, "Thành công", attachments));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(400, false, "Thất bại: " + ex.Message, null));

            }

        }

        [HttpPost("property/video")]
        public async Task<IActionResult> UploadPropertyVideoAsync(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest("Không tìm thấy tệp nào.");
                }
                var attachment = await _attachmentService.UploadPropertyVideoAsync(file);
                return Ok(CreateResponse(201, true, "Thành công", attachment));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(400, false, "Thất bại: " + ex.Message, null));

            }

        }

        [HttpPost("property/image")]
        public async Task<IActionResult> UploadPropertyImageAsync(List<IFormFile> files)
        {

            try
            {
                if (files == null || files.Count == 0)
                {
                    return BadRequest("No file provided for upload.");
                }
                var attachments = await _attachmentService.UploadPropertyImagesAsync(files);
                return Ok(CreateResponse(201, true, "Upload property images successfully", attachments));

            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(400, false, "Upload property images fail: " + ex.Message, null));

            }

        }
        [HttpPost("property/legal-document")]
        public async Task<IActionResult> UploadPropertyLegalDocumentAsync(List<IFormFile> files)
        {
            try
            {
                if (files == null || files.Count == 0)
                {
                    return BadRequest("No files provided for upload.");
                }
                var attachments = await _attachmentService.UploadPropertyLegalDocumentAsync(files);
                return Ok(CreateResponse(201, true, "Upload property legal documents successfully", attachments));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(400, false, "Upload property legal documents fail: " + ex.Message, null));
            }

        }
    }
}
