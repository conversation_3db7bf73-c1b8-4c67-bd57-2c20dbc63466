﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace BusinessObjects.Models
{
	public class Matching
	{
		[BsonId]
		[BsonRepresentation(BsonType.ObjectId)]
		public string id { get; set; } = ObjectId.GenerateNewId().ToString();
		public string userAId { get; set; }
		public string userBId { get; set; }
		public DateTime matchedAt { get; set; }
		public bool isActive { get; set; } = true;
	}
}
