﻿using BusinessObjects.DTOs.ReminderDTO;
using BusinessObjects.Models;


namespace Services.Interfaces
{
    public interface IReminderService
    {
        Task CreateSampleReminderAsync();
        Task<List<Reminder>> GetRemindersToNotifyAsync();
        Task SendReminderAsync(Reminder reminder);
        Task ScheduleReminder(ReminderDTO dto);
        DateTime CalculateScheduledTime(DateTime deadline);
        Task<List<Reminder>> GetUpcomingFollowUpsAsync(string userId, DateTime from, DateTime to);
    }
}
