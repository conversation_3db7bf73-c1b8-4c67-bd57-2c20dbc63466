﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using BusinessObjects.QueryObject;
using MongoDB.Bson;
using MongoDB.Driver;
using Repositories.Interfaces;

namespace Repositories.Implements
{
    public class PropertyRepository : GenericRepository<Property>, IPropertyRepository
    {
        private readonly RevoLandDbContext _context;
        private readonly IMongoCollection<Property> _collection;

        public PropertyRepository(RevoLandDbContext context) : base(context, "Properties")
        {
            _context = context;
            _collection = _context.GetCollection<Property>("Properties");
        }

        private class PropertyWithDistance
        {
            public Property Property { get; set; }
            public double Distance { get; set; }
        }

        public async Task<(List<Property> properties, int totalCount)> GetAllPropertiesAsync(QueryProperty query, string? userRole = null)
        {
            var filterBuilder = Builders<Property>.Filter;
            var filters = new List<FilterDefinition<Property>>();

            // === VERIFICATION FILTER ===
            // Chỉ admin mới thấy tất cả properties (kể cả chưa verified)
            if (userRole?.ToLower() != "admin")
            {
                filters.Add(filterBuilder.Eq(p => p.isVerified, true));
            }

            // === SEARCH FILTERS ===
            ApplySearchFilters(filters, filterBuilder, query);

            // === BASIC FILTERS ===
            ApplyBasicFilters(filters, filterBuilder, query);

            // === PROPERTY DETAIL FILTERS ===
            ApplyPropertyDetailFilters(filters, filterBuilder, query);

            // === PRICE FILTERS ===
            ApplyPriceFilters(filters, filterBuilder, query);

            // Combine all filters
            var combinedFilter = filters.Any() ? filterBuilder.And(filters) : filterBuilder.Empty;

            // === LOCATION FILTER ===
            if (query.HasLocationCoordinates)
            {
                return await GetPropertiesWithLocationFilter(combinedFilter, query);
            }

            // === COUNT TOTAL ===
            var totalCount = (int)await _collection.CountDocumentsAsync(combinedFilter);

            // === SORTING ===
            var sortDefinition = ApplySorting(query);

            // === PAGINATION ===
            var properties = await _collection
                .Find(combinedFilter)
                .Sort(sortDefinition)
                .Skip((query.pageNumber - 1) * query.pageSize)
                .Limit(query.pageSize)
                .ToListAsync();

            return (properties, totalCount);
        }

        public async Task<(List<Property> properties, int totalCount)> GetPropertiesBySellerAsync(QueryProperty query, string sellerId)
        {
            try
            {
                //Validate seller ID
                if (string.IsNullOrEmpty(sellerId))
                {
                    throw new ArgumentException("Seller ID is required");
                }

                var filterBuilder = Builders<Property>.Filter;
                var filters = new List<FilterDefinition<Property>>();

                // THÊM filter by Seller ID (bắt buộc)
                filters.Add(filterBuilder.Eq(p => p.salerId, sellerId));

                // SỬ DỤNG LẠI tất cả logic filter từ GetAllPropertiesAsync
                // === SEARCH FILTERS ===
                ApplySearchFilters(filters, filterBuilder, query);

                // === BASIC FILTERS ===
                ApplyBasicFilters(filters, filterBuilder, query);

                // === PROPERTY DETAIL FILTERS ===
                ApplyPropertyDetailFilters(filters, filterBuilder, query);

                // === PRICE FILTERS ===
                ApplyPriceFilters(filters, filterBuilder, query);

                // Combine all filters
                var combinedFilter = filters.Any() ? filterBuilder.And(filters) : filterBuilder.Empty;

                // === LOCATION FILTER ===
                if (query.HasLocationCoordinates)
                {
                    return await GetPropertiesWithLocationFilterForSeller(combinedFilter, query, sellerId);
                }

                // === COUNT TOTAL ===
                var totalCount = (int)await _collection.CountDocumentsAsync(combinedFilter);

                // === SORTING ===
                var sortDefinition = ApplySorting(query);

                // === PAGINATION ===
                var properties = await _collection
                    .Find(combinedFilter)
                    .Sort(sortDefinition)
                    .Skip((query.pageNumber - 1) * query.pageSize)
                    .Limit(query.pageSize)
                    .ToListAsync();

                return (properties, totalCount);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error getting properties by seller: {ex.Message}", ex);
            }
        }

        public async Task<(List<Property> properties, int totalCount)> GetPropertiesByOwnerAsync(QueryProperty query, string ownerId)
        {
            try
            {
                // Validate owner ID
                if (string.IsNullOrEmpty(ownerId))
                {
                    throw new ArgumentException("Owner ID is required");
                }

                var filterBuilder = Builders<Property>.Filter;
                var filters = new List<FilterDefinition<Property>>();

                // ✅ SỬA LỖI: Thêm owner filter vào filters list
                filters.Add(filterBuilder.Eq(p => p.ownerId, ownerId));

                // SỬ DỤNG LẠI tất cả logic filter từ GetAllPropertiesAsync
                // === SEARCH FILTERS ===
                ApplySearchFilters(filters, filterBuilder, query);

                // === BASIC FILTERS ===
                ApplyBasicFilters(filters, filterBuilder, query);

                // === PROPERTY DETAIL FILTERS ===
                ApplyPropertyDetailFilters(filters, filterBuilder, query);

                // === PRICE FILTERS ===
                ApplyPriceFilters(filters, filterBuilder, query);

                // Combine all filters
                var combinedFilter = filters.Any() ? filterBuilder.And(filters) : filterBuilder.Empty;

                // === LOCATION FILTER ===
                if (query.HasLocationCoordinates)
                {
                    return await GetPropertiesWithLocationFilterForOwner(combinedFilter, query, ownerId);
                }

                // === COUNT TOTAL ===
                var totalCount = (int)await _collection.CountDocumentsAsync(combinedFilter);

                // === SORTING ===
                var sortDefinition = ApplySorting(query);

                // === PAGINATION ===
                var properties = await _collection
                    .Find(combinedFilter)
                    .Sort(sortDefinition)
                    .Skip((query.pageNumber - 1) * query.pageSize)
                    .Limit(query.pageSize)
                    .ToListAsync();

                return (properties, totalCount);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error getting properties by owner: {ex.Message}", ex);
            }
        }

        //get all draft properties by user
        public async Task<(List<Property> properties, int totalCount)> GetDraftPropertiesByUserAsync(string userId, string role, int pageSize, int currentPage)
        {
            var filterBuilder = Builders<Property>.Filter;
            var filters = new List<FilterDefinition<Property>>();
            // Thêm filter theo userId và role
            if (role.ToLower() == "saler")
            {
                filters.Add(filterBuilder.Eq(p => p.salerId, userId));
            }
            else if (role.ToLower() == "owner")
            {
                filters.Add(filterBuilder.Eq(p => p.ownerId, userId));
            }
            else
            {
                throw new ArgumentException("Invalid role. Must be 'seller' or 'owner'.");
            }
            // Combine all filters
            var combinedFilter = filters.Any() ? filterBuilder.And(filters) : filterBuilder.Empty;
            // Get all draft properties
            int skip = (currentPage - 1) * pageSize;
            var properties = await _collection
                .Find(combinedFilter)
                .Sort(Builders<Property>.Sort.Descending(p => p.createdAt))
                .Skip(skip)
                .Limit(pageSize)
                .ToListAsync();
            // Count total draft properties
            var totalCount = properties.Count;
            // Return the properties and total count
            return (properties, totalCount);
        }

        private async Task<(List<Property> properties, int totalCount)> GetPropertiesWithLocationFilter(
    FilterDefinition<Property> combinedFilter,
    QueryProperty query)
        {
            var filters = new List<FilterDefinition<Property>>();

            // Thêm combined filter hiện tại
            if (combinedFilter != FilterDefinition<Property>.Empty)
            {
                filters.Add(combinedFilter);
            }

            // Thêm location bounding box filter
            var locationFilter = CreateLocationBoundingBoxFilter(
                query.latitude.Value,
                query.longitude.Value,
                query.maxRadius
            );
            filters.Add(locationFilter);

            // Combine tất cả filters
            var finalFilter = filters.Any()
                ? Builders<Property>.Filter.And(filters)
                : Builders<Property>.Filter.Empty;

            // Get tất cả properties trong bounding box trước
            var propertiesFromDb = await _collection
                .Find(finalFilter)
                .ToListAsync();

            var propertiesWithDistance = propertiesFromDb
                .Where(p => p.location != null &&
                           p.location.latitude != 0.0 &&
                           p.location.longitude != 0.0)
                .Select(p => new PropertyWithDistance
                {
                    Property = p,
                    Distance = CalculateDistance(
                        query.latitude.Value, query.longitude.Value,
                        p.location.latitude,
                        p.location.longitude
                    )
                })
                .Where(x => x.Distance <= query.maxRadius)
                .ToList();

            // Apply sorting
            propertiesWithDistance = ApplySortingWithDistance(propertiesWithDistance, query);

            // Apply pagination sau khi filter distance
            var totalCount = propertiesWithDistance.Count;
            var properties = propertiesWithDistance
                .Skip((query.pageNumber - 1) * query.pageSize)
                .Take(query.pageSize)
                .Select(x => x.Property)
                .ToList();

            return (properties, totalCount);
        }


        private async Task<(List<Property> properties, int totalCount)> GetPropertiesWithLocationFilterForSeller(
            FilterDefinition<Property> combinedFilter,
            QueryProperty query,
            string sellerId)
        {
            var filters = new List<FilterDefinition<Property>>();

            // Thêm combined filter hiện tại (đã chứa seller filter)
            if (combinedFilter != FilterDefinition<Property>.Empty)
            {
                filters.Add(combinedFilter);
            }

            // Thêm location bounding box filter
            var locationFilter = CreateLocationBoundingBoxFilter(
                query.latitude.Value,
                query.longitude.Value,
                query.maxRadius
            );
            filters.Add(locationFilter);

            // Combine tất cả filters
            var finalFilter = filters.Any()
                ? Builders<Property>.Filter.And(filters)
                : Builders<Property>.Filter.Empty;

            // Get properties với bounding box filter
            var propertiesFromDb = await _collection
                .Find(finalFilter)
                .ToListAsync();

            // Filter chính xác bằng distance calculation
            var propertiesWithDistance = propertiesFromDb
                .Where(p => p.location != null &&
                           p.location.latitude != 0.0 &&
                           p.location.longitude != 0.0)
                .Select(p => new PropertyWithDistance
                {
                    Property = p,
                    Distance = CalculateDistance(
                        query.latitude.Value, query.longitude.Value,
                        p.location.latitude,
                        p.location.longitude
                    )
                })
                .Where(x => x.Distance <= query.maxRadius)
                .ToList();

            // Apply sorting
            propertiesWithDistance = ApplySortingWithDistance(propertiesWithDistance, query);

            // Apply pagination
            var totalCount = propertiesWithDistance.Count;
            var properties = propertiesWithDistance
                .Skip((query.pageNumber - 1) * query.pageSize)
                .Take(query.pageSize)
                .Select(x => x.Property)
                .ToList();

            return (properties, totalCount);
        }

        private async Task<(List<Property> properties, int totalCount)> GetPropertiesWithLocationFilterForOwner(
            FilterDefinition<Property> combinedFilter,
            QueryProperty query,
            string ownerId)
        {
            var filters = new List<FilterDefinition<Property>>();

            // Thêm combined filter hiện tại (đã chứa owner filter)
            if (combinedFilter != FilterDefinition<Property>.Empty)
            {
                filters.Add(combinedFilter);
            }

            // Thêm location bounding box filter
            var locationFilter = CreateLocationBoundingBoxFilter(
                query.latitude.Value,
                query.longitude.Value,
                query.maxRadius
            );
            filters.Add(locationFilter);

            // Combine tất cả filters
            var finalFilter = filters.Any()
                ? Builders<Property>.Filter.And(filters)
                : Builders<Property>.Filter.Empty;

            // Get properties với bounding box filter
            var propertiesFromDb = await _collection
                .Find(finalFilter)
                .ToListAsync();

            // Filter chính xác bằng distance calculation
            var propertiesWithDistance = propertiesFromDb
     .Where(p => p.location != null &&
                p.location.latitude != 0.0 &&
                p.location.longitude != 0.0)
     .Select(p => new PropertyWithDistance
     {
         Property = p,
         Distance = CalculateDistance(
             query.latitude.Value, query.longitude.Value,
             p.location.latitude,
             p.location.longitude
         )
     })
     .Where(x => x.Distance <= query.maxRadius)
     .ToList();

            // Apply sorting
            propertiesWithDistance = ApplySortingWithDistance(propertiesWithDistance, query);

            // Apply pagination
            var totalCount = propertiesWithDistance.Count;
            var properties = propertiesWithDistance
                .Skip((query.pageNumber - 1) * query.pageSize)
                .Take(query.pageSize)
                .Select(x => x.Property)
                .ToList();

            return (properties, totalCount);
        }

        // ============ HELPER METHODS ============

        // Tạo bounding box filter để giảm số documents cần check
        private FilterDefinition<Property> CreateLocationBoundingBoxFilter(
    double centerLat, double centerLng, double radiusKm)
        {
            double latDelta = radiusKm / 111.0;
            double lngDelta = radiusKm / (111.0 * Math.Cos(centerLat * Math.PI / 180));

            var minLat = centerLat - latDelta;
            var maxLat = centerLat + latDelta;
            var minLng = centerLng - lngDelta;
            var maxLng = centerLng + lngDelta;

            var filterBuilder = Builders<Property>.Filter;

            var finalFilter = filterBuilder.And(
                filterBuilder.Gte("location.latitude", minLat),
                filterBuilder.Lte("location.latitude", maxLat),
                filterBuilder.Gte("location.longitude", minLng),
                filterBuilder.Lte("location.longitude", maxLng),
                filterBuilder.Exists("location.latitude", true),
                filterBuilder.Exists("location.longitude", true)
            );

            return finalFilter;
        }

        // Tính toán khoảng cách chính xác bằng Haversine formula
        private double CalculateDistance(double lat1, double lng1, double lat2, double lng2)
        {
            const double R = 6371; // Earth's radius in kilometers

            // Convert degrees to radians
            double dLat = (lat2 - lat1) * Math.PI / 180;
            double dLng = (lng2 - lng1) * Math.PI / 180;
            double radLat1 = lat1 * Math.PI / 180;
            double radLat2 = lat2 * Math.PI / 180;

            // Haversine formula
            double a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                       Math.Cos(radLat1) * Math.Cos(radLat2) *
                       Math.Sin(dLng / 2) * Math.Sin(dLng / 2);

            double c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));

            return R * c; // Distance in kilometers
        }

        // ============ FILTER APPLICATION METHODS ============

        private void ApplySearchFilters(List<FilterDefinition<Property>> filters, FilterDefinitionBuilder<Property> filterBuilder, QueryProperty query)
        {
            // Search term filter
            if (!string.IsNullOrEmpty(query.searchTerm))
            {
                var searchFilter = filterBuilder.Or(
                    filterBuilder.Regex(p => p.name, new BsonRegularExpression(query.searchTerm, "i")),
                    filterBuilder.Regex(p => p.contactName, new BsonRegularExpression(query.searchTerm, "i")),
                    filterBuilder.Regex(p => p.description, new BsonRegularExpression(query.searchTerm, "i")),
                    filterBuilder.Regex(p => p.location.address, new BsonRegularExpression(query.searchTerm, "i")),
                    filterBuilder.Regex(p => p.location.district, new BsonRegularExpression(query.searchTerm, "i")),
                    filterBuilder.Regex(p => p.location.city, new BsonRegularExpression(query.searchTerm, "i")),
                    filterBuilder.Regex(p => p.code, new BsonRegularExpression(query.searchTerm, "i")),
                    filterBuilder.Regex(p => p.title, new BsonRegularExpression(query.searchTerm, "i"))
                );
                filters.Add(searchFilter);
            }
        }

        private void ApplyBasicFilters(List<FilterDefinition<Property>> filters, FilterDefinitionBuilder<Property> filterBuilder, QueryProperty query)
        {
            // Featured filter
            if (query.isFeatured.HasValue)
            {
                filters.Add(filterBuilder.Eq(p => p.isFeatured, query.isFeatured.Value));
            }

            // Verified filter (từ query parameter, khác với auto-filter ở trên)
            if (query.isVerified.HasValue)
            {
                filters.Add(filterBuilder.Eq(p => p.isVerified, query.isVerified.Value));
            }

            // Status filter
            //if (query.status != null && query.status.Any())
            //{
            //    filters.Add(filterBuilder.In(p => p.status, query.status));
            //}

            //// Transaction type filter
            //if (query.transactionType != null && query.transactionType.Any())
            //{
            //    filters.Add(filterBuilder.In(p => p.transactionType, query.transactionType));
            //}

            //// Property type filter
            //if (query.type != null && query.type.Any())
            //{
            //    filters.Add(filterBuilder.In(p => p.type, query.type));
            //}
        }

        private void ApplyPropertyDetailFilters(List<FilterDefinition<Property>> filters, FilterDefinitionBuilder<Property> filterBuilder, QueryProperty query)
        {
            // Property detail bool filters
            if (query.propertyDetailFilters != null && query.propertyDetailFilters.Any())
            {
                foreach (var filter in query.propertyDetailFilters)
                {
                    switch (filter)
                    {
                        case PropDetailBoolFilter.hasBasement:
                            filters.Add(filterBuilder.Eq(p => p.propertyDetails.hasBasement, true));
                            break;
                        case PropDetailBoolFilter.furnished:
                            filters.Add(filterBuilder.Eq(p => p.propertyDetails.furnished, true));
                            break;
                    }
                }
            }

            // Amenity filters
            if (query.amenityFilters != null && query.amenityFilters.Any())
            {
                foreach (var filter in query.amenityFilters)
                {
                    switch (filter)
                    {
                        case AmenityFilter.Parking:
                            filters.Add(filterBuilder.Eq(p => p.amenities.parking, true));
                            break;
                        case AmenityFilter.Elevator:
                            filters.Add(filterBuilder.Eq(p => p.amenities.elevator, true));
                            break;
                        case AmenityFilter.SwimmingPool:
                            filters.Add(filterBuilder.Eq(p => p.amenities.swimmingPool, true));
                            break;
                        case AmenityFilter.Gym:
                            filters.Add(filterBuilder.Eq(p => p.amenities.gym, true));
                            break;
                        case AmenityFilter.SecuritySystem:
                            filters.Add(filterBuilder.Eq(p => p.amenities.securitySystem, true));
                            break;
                        case AmenityFilter.AirConditioning:
                            filters.Add(filterBuilder.Eq(p => p.amenities.airConditioning, true));
                            break;
                        case AmenityFilter.Balcony:
                            filters.Add(filterBuilder.Eq(p => p.amenities.balcony, true));
                            break;
                        case AmenityFilter.Garden:
                            filters.Add(filterBuilder.Eq(p => p.amenities.garden, true));
                            break;
                        case AmenityFilter.Playground:
                            filters.Add(filterBuilder.Eq(p => p.amenities.playground, true));
                            break;
                        case AmenityFilter.BackupGenerator:
                            filters.Add(filterBuilder.Eq(p => p.amenities.backupGenerator, true));
                            break;
                    }
                }
            }

            // Bedroom filters
            if (query.minBedrooms.HasValue)
                filters.Add(filterBuilder.Gte(p => p.propertyDetails.bedrooms, query.minBedrooms.Value));
            if (query.maxBedrooms.HasValue)
                filters.Add(filterBuilder.Lte(p => p.propertyDetails.bedrooms, query.maxBedrooms.Value));

            // Bathroom filters
            if (query.bathrooms.HasValue)
                filters.Add(filterBuilder.Eq(p => p.propertyDetails.bathrooms, query.bathrooms.Value));
            if (query.minBathrooms.HasValue)
                filters.Add(filterBuilder.Gte(p => p.propertyDetails.bathrooms, query.minBathrooms.Value));
            if (query.maxBathrooms.HasValue)
                filters.Add(filterBuilder.Lte(p => p.propertyDetails.bathrooms, query.maxBathrooms.Value));

            // Living room filters
            if (query.livingRooms.HasValue)
                filters.Add(filterBuilder.Eq(p => p.propertyDetails.livingRooms, query.livingRooms.Value));
            if (query.minLivingRooms.HasValue)
                filters.Add(filterBuilder.Gte(p => p.propertyDetails.livingRooms, query.minLivingRooms.Value));
            if (query.maxLivingRooms.HasValue)
                filters.Add(filterBuilder.Lte(p => p.propertyDetails.livingRooms, query.maxLivingRooms.Value));

            // Kitchen filters
            if (query.kitchens.HasValue)
                filters.Add(filterBuilder.Eq(p => p.propertyDetails.kitchens, query.kitchens.Value));
            if (query.minKitchens.HasValue)
                filters.Add(filterBuilder.Gte(p => p.propertyDetails.kitchens, query.minKitchens.Value));
            if (query.maxKitchens.HasValue)
                filters.Add(filterBuilder.Lte(p => p.propertyDetails.kitchens, query.maxKitchens.Value));

            // Land area filters
            if (query.minLandArea.HasValue)
                filters.Add(filterBuilder.Gte(p => p.propertyDetails.landArea, query.minLandArea.Value));
            if (query.maxLandArea.HasValue)
                filters.Add(filterBuilder.Lte(p => p.propertyDetails.landArea, query.maxLandArea.Value));

            // Land width filters
            if (query.landWidth.HasValue)
                filters.Add(filterBuilder.Eq(p => p.propertyDetails.landWidth, query.landWidth.Value));
            if (query.minLandWidth.HasValue)
                filters.Add(filterBuilder.Gte(p => p.propertyDetails.landWidth, query.minLandWidth.Value));
            if (query.maxLandWidth.HasValue)
                filters.Add(filterBuilder.Lte(p => p.propertyDetails.landWidth, query.maxLandWidth.Value));

            // Land length filters
            if (query.landLength.HasValue)
                filters.Add(filterBuilder.Eq(p => p.propertyDetails.landLength, query.landLength.Value));
            if (query.minLandLength.HasValue)
                filters.Add(filterBuilder.Gte(p => p.propertyDetails.landLength, query.minLandLength.Value));
            if (query.maxLandLength.HasValue)
                filters.Add(filterBuilder.Lte(p => p.propertyDetails.landLength, query.maxLandLength.Value));

            // Building area filters
            if (query.buildingArea.HasValue)
                filters.Add(filterBuilder.Eq(p => p.propertyDetails.buildingArea, query.buildingArea.Value));
            if (query.minBuildingArea.HasValue)
                filters.Add(filterBuilder.Gte(p => p.propertyDetails.buildingArea, query.minBuildingArea.Value));
            if (query.maxBuildingArea.HasValue)
                filters.Add(filterBuilder.Lte(p => p.propertyDetails.buildingArea, query.maxBuildingArea.Value));

            // Number of floors filters
            if (query.numberOfFloors.HasValue)
                filters.Add(filterBuilder.Eq(p => p.propertyDetails.numberOfFloors, query.numberOfFloors.Value));
            if (query.minNumberOfFloors.HasValue)
                filters.Add(filterBuilder.Gte(p => p.propertyDetails.numberOfFloors, query.minNumberOfFloors.Value));
            if (query.maxNumberOfFloors.HasValue)
                filters.Add(filterBuilder.Lte(p => p.propertyDetails.numberOfFloors, query.maxNumberOfFloors.Value));

            // Floor number filters
            if (query.floorNumber.HasValue)
                filters.Add(filterBuilder.Eq(p => p.propertyDetails.floorNumber, query.floorNumber.Value));
            if (query.minFloorNumber.HasValue)
                filters.Add(filterBuilder.Gte(p => p.propertyDetails.floorNumber, query.minFloorNumber.Value));
            if (query.maxFloorNumber.HasValue)
                filters.Add(filterBuilder.Lte(p => p.propertyDetails.floorNumber, query.maxFloorNumber.Value));

            // Apartment orientation filters
            if (query.apartmentOrientation != null && query.apartmentOrientation.Any())
            {
                filters.Add(filterBuilder.In(p => p.propertyDetails.apartmentOrientation, query.apartmentOrientation));
            }
        }

        private void ApplyPriceFilters(List<FilterDefinition<Property>> filters, FilterDefinitionBuilder<Property> filterBuilder, QueryProperty query)
        {
            if (query.minPrice.HasValue || query.maxPrice.HasValue)
            {
                // Cần check transaction type để biết dùng salePrice hay rentalPrice
                var priceFilters = new List<FilterDefinition<Property>>();

                if (query.transactionType != null && query.transactionType.Any())
                {
                    foreach (var transType in query.transactionType)
                    {
                        if (transType == PropertyTransactionType.ForSale)
                        {
                            var salePriceFilters = new List<FilterDefinition<Property>>();
                            if (query.minPrice.HasValue)
                                salePriceFilters.Add(filterBuilder.Gte(p => p.priceDetails.salePrice, query.minPrice.Value));
                            if (query.maxPrice.HasValue)
                                salePriceFilters.Add(filterBuilder.Lte(p => p.priceDetails.salePrice, query.maxPrice.Value));

                            if (salePriceFilters.Any())
                                priceFilters.Add(filterBuilder.And(salePriceFilters));
                        }
                        else if (transType == PropertyTransactionType.ForRent)
                        {
                            var rentalPriceFilters = new List<FilterDefinition<Property>>();
                            if (query.minPrice.HasValue)
                                rentalPriceFilters.Add(filterBuilder.Gte(p => p.priceDetails.rentalPrice, query.minPrice.Value));
                            if (query.maxPrice.HasValue)
                                rentalPriceFilters.Add(filterBuilder.Lte(p => p.priceDetails.rentalPrice, query.maxPrice.Value));

                            if (rentalPriceFilters.Any())
                                priceFilters.Add(filterBuilder.And(rentalPriceFilters));
                        }
                    }
                }
                else
                {
                    // Nếu không specify transaction type, check cả hai
                    var allPriceFilters = new List<FilterDefinition<Property>>();

                    // Sale price filters
                    var salePriceFilters = new List<FilterDefinition<Property>>();
                    if (query.minPrice.HasValue)
                        salePriceFilters.Add(filterBuilder.Gte(p => p.priceDetails.salePrice, query.minPrice.Value));
                    if (query.maxPrice.HasValue)
                        salePriceFilters.Add(filterBuilder.Lte(p => p.priceDetails.salePrice, query.maxPrice.Value));

                    if (salePriceFilters.Any())
                        allPriceFilters.Add(filterBuilder.And(salePriceFilters));

                    // Rental price filters
                    var rentalPriceFilters = new List<FilterDefinition<Property>>();
                    if (query.minPrice.HasValue)
                        rentalPriceFilters.Add(filterBuilder.Gte(p => p.priceDetails.rentalPrice, query.minPrice.Value));
                    if (query.maxPrice.HasValue)
                        rentalPriceFilters.Add(filterBuilder.Lte(p => p.priceDetails.rentalPrice, query.maxPrice.Value));

                    if (rentalPriceFilters.Any())
                        allPriceFilters.Add(filterBuilder.And(rentalPriceFilters));

                    if (allPriceFilters.Any())
                        priceFilters.Add(filterBuilder.Or(allPriceFilters));
                }

                if (priceFilters.Any())
                    filters.Add(filterBuilder.Or(priceFilters));
            }
        }

        // ============ SORTING METHODS ============

        // ============ SORTING METHODS ============

        private SortDefinition<Property> ApplySorting(QueryProperty query)
        {
            var sortBuilder = Builders<Property>.Sort;

            return query.sortBy switch
            {
                PropertyEnumSortBy.name => query.isDescending
                    ? sortBuilder.Descending(p => p.name)
                    : sortBuilder.Ascending(p => p.name),

                PropertyEnumSortBy.price => query.isDescending
                    ? sortBuilder.Descending(p => p.priceDetails.salePrice)
                    : sortBuilder.Ascending(p => p.priceDetails.salePrice),

                PropertyEnumSortBy.createdAt => query.isDescending
                    ? sortBuilder.Descending(p => p.createdAt)
                    : sortBuilder.Ascending(p => p.createdAt),

                PropertyEnumSortBy.updatedAt => query.isDescending
                    ? sortBuilder.Descending(p => p.updatedAt)
                    : sortBuilder.Ascending(p => p.updatedAt),

                PropertyEnumSortBy.distance => sortBuilder.Ascending(p => p.createdAt), // Default cho distance, sẽ sort riêng

                _ => sortBuilder.Ascending(p => p.createdAt) // Default sort
            };
        }

        // Thêm method mới để sort PropertyWithDistance
        private List<PropertyWithDistance> ApplySortingWithDistance(
            List<PropertyWithDistance> propertiesWithDistance,
            QueryProperty query)
        {
            return query.sortBy switch
            {
                PropertyEnumSortBy.name => query.isDescending
                    ? propertiesWithDistance.OrderByDescending(x => x.Property.name).ToList()
                    : propertiesWithDistance.OrderBy(x => x.Property.name).ToList(),

                PropertyEnumSortBy.price => query.isDescending
                    ? propertiesWithDistance.OrderByDescending(x => x.Property.priceDetails.salePrice).ToList()
                    : propertiesWithDistance.OrderBy(x => x.Property.priceDetails.salePrice).ToList(),

                PropertyEnumSortBy.createdAt => query.isDescending
                    ? propertiesWithDistance.OrderByDescending(x => x.Property.createdAt).ToList()
                    : propertiesWithDistance.OrderBy(x => x.Property.createdAt).ToList(),

                PropertyEnumSortBy.updatedAt => query.isDescending
                    ? propertiesWithDistance.OrderByDescending(x => x.Property.updatedAt).ToList()
                    : propertiesWithDistance.OrderBy(x => x.Property.updatedAt).ToList(),

                PropertyEnumSortBy.distance => query.isDescending
                    ? propertiesWithDistance.OrderByDescending(x => x.Distance).ToList()
                    : propertiesWithDistance.OrderBy(x => x.Distance).ToList(),

                _ => propertiesWithDistance.OrderBy(x => x.Distance).ToList() // Default sort by distance
            };
        }

        // ============ INDEX INITIALIZATION ============

        public async Task InitializeIndexesAsync()
        {
            try
            {
                // Tạo compound index cho location
                var locationIndexKeys = Builders<Property>.IndexKeys
                    .Ascending("location.latitude")
                    .Ascending("location.longitude");

                var locationIndexOptions = new CreateIndexOptions
                {
                    Name = "location_lat_lng_index",
                    Background = true
                };

                await _collection.Indexes.CreateOneAsync(
                    new CreateIndexModel<Property>(locationIndexKeys, locationIndexOptions)
                );

                // Tạo index cho seller
                var sellerIndexKeys = Builders<Property>.IndexKeys.Ascending(p => p.salerId);
                await _collection.Indexes.CreateOneAsync(
                    new CreateIndexModel<Property>(sellerIndexKeys, new CreateIndexOptions
                    {
                        Name = "seller_id_index",
                        Background = true
                    })
                );

                // Tạo index cho owner
                var ownerIndexKeys = Builders<Property>.IndexKeys.Ascending(p => p.ownerId);
                await _collection.Indexes.CreateOneAsync(
                    new CreateIndexModel<Property>(ownerIndexKeys, new CreateIndexOptions
                    {
                        Name = "owner_id_index",
                        Background = true
                    })
                );

                // Tạo index cho isVerified
                var verifiedIndexKeys = Builders<Property>.IndexKeys.Ascending(p => p.isVerified);
                await _collection.Indexes.CreateOneAsync(
                    new CreateIndexModel<Property>(verifiedIndexKeys, new CreateIndexOptions
                    {
                        Name = "is_verified_index",
                        Background = true
                    })
                );

                // Tạo text index cho search
                var textIndexKeys = Builders<Property>.IndexKeys
                    .Text(p => p.name)
                    .Text(p => p.title)
                    .Text(p => p.description)
                    .Text(p => p.location.address)
                    .Text(p => p.location.district)
                    .Text(p => p.location.city);

                await _collection.Indexes.CreateOneAsync(
                    new CreateIndexModel<Property>(textIndexKeys, new CreateIndexOptions
                    {
                        Name = "text_search_index",
                        Background = true
                    })
                );

                // Tạo compound index cho status và transaction type
                var statusTransactionIndexKeys = Builders<Property>.IndexKeys
                    .Ascending(p => p.status)
                    .Ascending(p => p.transactionType);

                await _collection.Indexes.CreateOneAsync(
                    new CreateIndexModel<Property>(statusTransactionIndexKeys, new CreateIndexOptions
                    {
                        Name = "status_transaction_index",
                        Background = true
                    })
                );

                // Tạo index cho property type
                var typeIndexKeys = Builders<Property>.IndexKeys.Ascending(p => p.type);
                await _collection.Indexes.CreateOneAsync(
                    new CreateIndexModel<Property>(typeIndexKeys, new CreateIndexOptions
                    {
                        Name = "property_type_index",
                        Background = true
                    })
                );

                // Tạo compound index cho price
                var priceIndexKeys = Builders<Property>.IndexKeys
                    .Ascending(p => p.priceDetails.salePrice)
                    .Ascending(p => p.priceDetails.rentalPrice);

                await _collection.Indexes.CreateOneAsync(
                    new CreateIndexModel<Property>(priceIndexKeys, new CreateIndexOptions
                    {
                        Name = "price_index",
                        Background = true
                    })
                );

                // Tạo index cho createdAt và updatedAt
                var dateIndexKeys = Builders<Property>.IndexKeys
                    .Descending(p => p.createdAt)
                    .Descending(p => p.updatedAt);

                await _collection.Indexes.CreateOneAsync(
                    new CreateIndexModel<Property>(dateIndexKeys, new CreateIndexOptions
                    {
                        Name = "date_index",
                        Background = true
                    })
                );

                Console.WriteLine("✅ Property indexes created successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error creating indexes: {ex.Message}");
            }
        }

        // ============ EXISTING METHODS ============

        public async Task<Property> GetByTransactionId(string transactionId)
        {
            if (!ObjectId.TryParse(transactionId, out ObjectId objTransactionId))
                throw new InvalidDataException("Invalid transaction ID format.");

            var filter = Builders<Property>.Filter.ElemMatch(p => p.transactionHistory, t => t.transactionId == objTransactionId);

            return await _collection.Find(filter).FirstOrDefaultAsync();
        }

        public async Task InsertManyAsync(IEnumerable<Property> properties)
        {
            await _collection.InsertManyAsync(properties);
        }

        public async Task<List<Property>> GetByOwnerIdAsync(string ownerId)
        {
            var filter = Builders<Property>.Filter.Eq(p => p.ownerId, ownerId);
            return await _collection.Find(filter).ToListAsync();
        }

        public async Task<List<Property>> GetByIdsAsync(List<string> ids)
        {
            var objectIds = ids.Select(id => ObjectId.Parse(id)).ToList();
            var filter = Builders<Property>.Filter.In(p => p.id, objectIds);
            return await _collection.Find(filter).ToListAsync();
        }

        // ============ ADDITIONAL UTILITY METHODS ============

        /// <summary>
        /// Get properties near a specific location with detailed distance information
        /// </summary>
        /// <summary>
        /// Get properties count by status
        /// </summary>
        public async Task<Dictionary<PropertyStatus, int>> GetPropertiesCountByStatusAsync()
        {
            var pipeline = new[]
            {
                new BsonDocument("$group", new BsonDocument
                {
                    { "_id", "$status" },
                    { "count", new BsonDocument("$sum", 1) }
                })
            };

            var result = await _collection.Aggregate<BsonDocument>(pipeline).ToListAsync();

            var statusCounts = new Dictionary<PropertyStatus, int>();

            foreach (var doc in result)
            {
                if (Enum.TryParse<PropertyStatus>(doc["_id"].AsString, out var status))
                {
                    statusCounts[status] = doc["count"].AsInt32;
                }
            }

            return statusCounts;
        }

        /// <summary>
        /// Get properties count by transaction type
        /// </summary>
        public async Task<Dictionary<PropertyTransactionType, int>> GetPropertiesCountByTransactionTypeAsync()
        {
            var pipeline = new[]
            {
                new BsonDocument("$group", new BsonDocument
                {
                    { "_id", "$transactionType" },
                    { "count", new BsonDocument("$sum", 1) }
                })
            };

            var result = await _collection.Aggregate<BsonDocument>(pipeline).ToListAsync();

            var transactionCounts = new Dictionary<PropertyTransactionType, int>();

            foreach (var doc in result)
            {
                if (Enum.TryParse<PropertyTransactionType>(doc["_id"].AsString, out var transactionType))
                {
                    transactionCounts[transactionType] = doc["count"].AsInt32;
                }
            }

            return transactionCounts;
        }

        /// <summary>
        /// Get average price by property type and transaction type
        /// </summary>
        public async Task<Dictionary<string, double>> GetAveragePriceByTypeAsync()
        {
            var pipeline = new[]
            {
                new BsonDocument("$group", new BsonDocument
                {
                    { "_id", new BsonDocument
                        {
                            { "type", "$type" },
                            { "transactionType", "$transactionType" }
                        }
                    },
                    { "avgSalePrice", new BsonDocument("$avg", "$priceDetails.salePrice") },
                    { "avgRentalPrice", new BsonDocument("$avg", "$priceDetails.rentalPrice") }
                })
            };

            var result = await _collection.Aggregate<BsonDocument>(pipeline).ToListAsync();

            var avgPrices = new Dictionary<string, double>();

            foreach (var doc in result)
            {
                var id = doc["_id"].AsBsonDocument;
                var type = id["type"].AsString;
                var transactionType = id["transactionType"].AsString;

                var key = $"{type}_{transactionType}";

                if (transactionType == "ForSale" && doc.Contains("avgSalePrice") && !doc["avgSalePrice"].IsBsonNull)
                {
                    avgPrices[key] = doc["avgSalePrice"].AsDouble;
                }
                else if (transactionType == "ForRent" && doc.Contains("avgRentalPrice") && !doc["avgRentalPrice"].IsBsonNull)
                {
                    avgPrices[key] = doc["avgRentalPrice"].AsDouble;
                }
            }

            return avgPrices;
        }

        /// <summary>
        /// Update property verification status
        /// </summary>
        public async Task<bool> UpdateVerificationStatusAsync(string propertyId, bool isVerified)
        {
            if (!ObjectId.TryParse(propertyId, out ObjectId objId))
                return false;

            var filter = Builders<Property>.Filter.Eq(p => p.id, objId);
            var update = Builders<Property>.Update
                .Set(p => p.isVerified, isVerified)
                .Set(p => p.updatedAt, DateTime.UtcNow);

            var result = await _collection.UpdateOneAsync(filter, update);
            return result.ModifiedCount > 0;
        }

        /// <summary>
        /// Update property featured status
        /// </summary>
        public async Task<bool> UpdateFeaturedStatusAsync(string propertyId, bool isFeatured)
        {
            if (!ObjectId.TryParse(propertyId, out ObjectId objId))
                return false;

            var filter = Builders<Property>.Filter.Eq(p => p.id, objId);
            var update = Builders<Property>.Update
                .Set(p => p.isFeatured, isFeatured)
                .Set(p => p.updatedAt, DateTime.UtcNow);

            var result = await _collection.UpdateOneAsync(filter, update);
            return result.ModifiedCount > 0;
        }

        /// <summary>
        /// Get featured properties
        /// </summary>
        public async Task<List<Property>> GetFeaturedPropertiesAsync(int limit = 10)
        {
            var filter = Builders<Property>.Filter.And(
                Builders<Property>.Filter.Eq(p => p.isFeatured, true),
                Builders<Property>.Filter.Eq(p => p.isVerified, true)
            );

            return await _collection
                .Find(filter)
                .Sort(Builders<Property>.Sort.Descending(p => p.updatedAt))
                .Limit(limit)
                .ToListAsync();
        }

        /// <summary>
        /// Get recently added properties
        /// </summary>
        public async Task<List<Property>> GetRecentPropertiesAsync(int limit = 10)
        {
            var filter = Builders<Property>.Filter.Eq(p => p.isVerified, true);

            return await _collection
                .Find(filter)
                .Sort(Builders<Property>.Sort.Descending(p => p.createdAt))
                .Limit(limit)
                .ToListAsync();
        }

        /// <summary>
        /// Search properties using text search
        /// </summary>
        public async Task<List<Property>> TextSearchPropertiesAsync(string searchText, int limit = 50)
        {
            var filter = Builders<Property>.Filter.And(
                Builders<Property>.Filter.Text(searchText),
                Builders<Property>.Filter.Eq(p => p.isVerified, true)
            );

            return await _collection
                .Find(filter)
                .Sort(Builders<Property>.Sort.MetaTextScore("score"))
                .Limit(limit)
                .ToListAsync();
        }
    }
}

