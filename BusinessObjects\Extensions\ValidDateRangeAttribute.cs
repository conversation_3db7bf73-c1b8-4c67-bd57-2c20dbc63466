﻿using System.ComponentModel.DataAnnotations;
using static BusinessObjects.DTOs.CalendarDTOs.GoogleCalendar;

namespace BusinessObjects.Extensions
{
    public class ValidDateRangeAttribute : ValidationAttribute
    {
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            var request = (CreateEventRequest)validationContext.ObjectInstance;

            if (request.startTime >= request.endTime)
            {
                return new ValidationResult("Thời gian kết thúc phải sau thời gian bắt đầu");
            }

            if (request.startTime < DateTime.Now.AddMinutes(-5))
            {
                return new ValidationResult("Thời gian bắt đầu không thể ở quá khứ");
            }

            return ValidationResult.Success;
        }
    }
}
