﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace BusinessObjects.Models
{
    public class FavoriteProperty
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public ObjectId id { get; set; }
        public string propertyId { get; set; }
        public string userId { get; set; }
        public DateTime savedAt { get; set; } = DateTime.UtcNow;
    }
}
