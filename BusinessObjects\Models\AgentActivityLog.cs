﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.Models
{
	public class AgentActivityLog
	{
		[BsonId]
		[BsonRepresentation(BsonType.ObjectId)]
		public string id { get; set; } = ObjectId.GenerateNewId().ToString();
		public string agentId { get; set; }
		public string companyId { get; set; }

		[BsonRepresentation(BsonType.String)]
		public AgentActivityAction action { get; set; }
		[BsonRepresentation(BsonType.String)]
		public AgentActivityTargetType? targetType { get; set; }
		public string? targetId { get; set; }   // ID cụ thể của đối tượng bị tác động
		public string? description { get; set; }
		public DateTime createdAt { get; set; } = DateTime.UtcNow.AddHours(7);
	}


	public enum AgentActivityAction
	{
		CreateLead,
		UpdateLead,
		AssignLead,
		ChangeLeadStatus,
		CreateDeal,
		UpdateDealStage,
		CloseDeal,
		CreateNote,
		DeleteNote,
		AssignAgentToTeam,
		RemoveAgentFromTeam,
		Other
	}
	public enum AgentActivityTargetType
	{
		Lead,
		Deal,
		Note,
		Team,
		Agent,
		System
	}
}
