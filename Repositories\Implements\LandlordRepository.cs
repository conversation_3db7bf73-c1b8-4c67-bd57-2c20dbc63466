﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using MongoDB.Driver;
using Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Repositories.Implements
{
    public class LandlordRepository : GenericRepository<Landlord>, ILandlordRepository
    {
        private readonly IMongoCollection<Landlord> _landlords;

        public LandlordRepository(RevoLandDbContext context) : base(context, "Landlords")
        {
            _landlords = context.GetCollection<Landlord>("Landlords");
        }

        public async Task<Landlord?> GetLandlordByEmailAsync(string email)
        {
            var filter = Builders<Landlord>.Filter.Eq(l => l.email, email);
            return await _landlords.Find(filter).FirstOrDefaultAsync();
        }

        public IQueryable<Landlord> GetAll()
        {
            return _landlords.AsQueryable();
        }
    }
}
