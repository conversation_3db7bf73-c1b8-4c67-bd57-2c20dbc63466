﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.Models
{
    public class Reminder
    {
        [BsonId]
        [BsonRepresentation(BsonType.String)]
        public Guid id { get; set; }
        public string title { get; set; }
        public string message { get; set; }
        public DateTime deadlineDate { get; set; }
        public DateTime scheduledTime { get; set; }
        public string email { get; set; }
        public string phoneNumber { get; set; }
        public string userId { get; set; } 
        public DateTime? lastContactDate { get; set; }

    }

    [Flags]
    public enum NotificationChannel
    {
        email = 0,
        sms = 1,
        inApp = 2
    }


}