﻿using BusinessObjects.DTOs.RequestFeatures;
using BusinessObjects.Models;

namespace BusinessObjects.QueryObject
{
    public class QueryDealsLog : RequestParameters
    {
        public Deal.DealStatus? fromStatus { get; set; }
        public Deal.DealStatus? toStatus { get; set; }

        // Date range filters
        public DateTime? timestampFrom { get; set; }
        public DateTime? timestampTo { get; set; }

        public string? triggeredBy { get; set; }
        public bool? isDeleteAction { get; set; }

        // Sorting
        public QueryDealsLogSortBy sortBy { get; set; } = QueryDealsLogSortBy.timestamp;
        public bool isAscending { get; set; } = false;

        public enum QueryDealsLogSortBy
        {
            timestamp,
            fromStatus,
            toStatus
        }
    }
}
