﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using FirebaseAdmin.Auth.Multitenancy;
using MongoDB.Driver;
using Repositories.Interfaces;

namespace Repositories.Implements
{
	public class BuyerRepository : GenericRepository<Buyer>, IBuyerRepository
	{
		private readonly IMongoCollection<Buyer> _buyers;

		public BuyerRepository(RevoLandDbContext context) : base(context, "Buyers")
		{
			_buyers = context.GetCollection<Buyer>("Buyers");
		}

		public async Task<Buyer> GetBuyerByPhone(string phone)
		{
			var filter = Builders<Buyer>.Filter.Eq(t => t.phone, phone);
			return await _buyers.Find(filter).FirstOrDefaultAsync();
		}
        public async Task<bool> IsPhoneExist(string phone)
        {
            var filter = Builders<Buyer>.Filter.Eq(t => t.phone, phone);
            return await _buyers.Find(filter).AnyAsync();
        }

        public async Task<bool> IsEmailExist(string email)
        {
            var filter = Builders<Buyer>.Filter.Eq(t => t.email, email);
            return await _buyers.Find(filter).AnyAsync();
        }
    }
}
