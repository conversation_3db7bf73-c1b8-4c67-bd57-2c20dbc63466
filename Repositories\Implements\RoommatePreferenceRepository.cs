using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using MongoDB.Driver;
using Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Repositories.Implements
{
    public class RoommatePreferenceRepository : GenericRepository<RoommatePreference>, IRoommatePreferenceRepository
    {
        private readonly IMongoCollection<RoommatePreference> _roommatePreferenceCollection;

        public RoommatePreferenceRepository(RevoLandDbContext context) : base(context, "RoommatePreferences")
        {
            _roommatePreferenceCollection = context.GetCollection<RoommatePreference>("RoommatePreferences");
        }

        public async Task<RoommatePreference> GetByUserIdAsync(string userId)
        {
            return await _roommatePreferenceCollection.Find(p => p.userId == userId).FirstOrDefaultAsync();
        }
    }
} 