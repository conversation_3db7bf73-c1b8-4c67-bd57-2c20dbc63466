﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace BusinessObjects.Models
{
    public class Conversation
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string id { get; set; }

        public string userId { get; set; }
        public string sellerId { get; set; }

        public string platform { get; set; }
        public string lastMessage { get; set; }
        public DateTime lastUpdated { get; set; }

        public List<ChatMessage> pinMessages = new List<ChatMessage>();
        public bool isClosed { get; set; } = false;
    }
}
