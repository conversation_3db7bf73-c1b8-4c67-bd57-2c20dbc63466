﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace BusinessObjects.QueryObject
{
    public class QueryTenant
    {
        public string? searchTerm { get; set; }

        [Range(1, int.MaxValue, ErrorMessage = "Page number must be greater than or equal to 1.")]
        public int pageNumber { get; set; } = 1;

        [Range(1, int.MaxValue, ErrorMessage = "Page size must be greater than or equal to 1.")]
        public int pageSize { get; set; } = 10;

        public bool isDescending { get; set; } = false;

        public DateTime? leaseStartDate { get; set; }
        public DateTime? leaseEndDate { get; set; }
        [JsonConverter(typeof(JsonStringEnumConverter))]
        public TenantEnumSortBy sortBy { get; set; } = TenantEnumSortBy.name;
    }

    public enum TenantEnumSortBy
    {
        name,
        leaseStartDate,
        leaseEndDate
    }
}
