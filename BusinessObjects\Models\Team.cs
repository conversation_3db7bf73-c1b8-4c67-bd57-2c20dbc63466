﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.Models
{
	public class Team
	{
		[BsonId]
		[BsonRepresentation(BsonType.ObjectId)]
		public string id { get; set; } = ObjectId.GenerateNewId().ToString();

		[Required]
		public string name { get; set; }

		[Required]
		[BsonRepresentation(BsonType.ObjectId)]
		public string companyId { get; set; } // team thuộc công ty nào

		public string? description { get; set; }

		public DateTime createdAt { get; set; } = DateTime.UtcNow.AddHours(7);
		public DateTime? updatedAt { get; set; }
	}
}
