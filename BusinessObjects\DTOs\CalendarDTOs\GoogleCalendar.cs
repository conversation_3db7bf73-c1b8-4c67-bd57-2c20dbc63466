﻿using BusinessObjects.Models;
using System.ComponentModel.DataAnnotations;

namespace BusinessObjects.DTOs.CalendarDTOs
{
    public class GoogleCalendar
    {
        public class CreateCalendarRequest
        {
            [Required]
            [StringLength(2048, ErrorMessage = "Access token không hợp lệ")]
            public string accessToken { get; set; }

            [Required]
            [StringLength(255, ErrorMessage = "Tên calendar không được vượt quá 255 ký tự")]
            public string summary { get; set; }

            [StringLength(1000, ErrorMessage = "Mô tả không được vượt quá 1000 ký tự")]
            public string? description { get; set; }

            [StringLength(100, ErrorMessage = "Time zone không hợp lệ")]
            public string timeZone { get; set; } = "Asia/Ho_Chi_Minh";

            [StringLength(50, ErrorMessage = "Địa điểm không được vượt quá 50 ký tự")]
            public string? location { get; set; }

            // Màu nền calendar (hex color)
            [StringLength(7, ErrorMessage = "Background color phải là mã hex hợp lệ")]
            [RegularExpression(@"^#[0-9A-Fa-f]{6}$", ErrorMessage = "Background color phải có định dạng #RRGGBB")]
            public string? backgroundColor { get; set; }

            // Màu chữ calendar (hex color)
            [StringLength(7, ErrorMessage = "Foreground color phải là mã hex hợp lệ")]
            [RegularExpression(@"^#[0-9A-Fa-f]{6}$", ErrorMessage = "Foreground color phải có định dạng #RRGGBB")]
            public string? foregroundColor { get; set; }

            // Có được chia sẻ công khai không
            public bool isPublic { get; set; } = false;

            // Danh sách email để chia sẻ calendar
            public List<CalendarShareRule>? shareRules { get; set; } = new List<CalendarShareRule>();
        }

        public class CalendarShareRule
        {
            [Required]
            [EmailAddress(ErrorMessage = "Email không hợp lệ")]
            public string email { get; set; }

            [Required]
            public string role { get; set; } // "owner", "reader", "writer", "freeBusyReader"

            public string type { get; set; } = "user"; // "user", "group", "domain", "default"
        }

        public class CreateCalendarResponse
        {
            public bool success { get; set; }
            public string calendarId { get; set; }
            public string summary { get; set; }
            public string description { get; set; }
            public string timeZone { get; set; }
            public string htmlLink { get; set; }
            public string backgroundColor { get; set; }
            public string foregroundColor { get; set; }
            public string message { get; set; }
            public List<string> sharedWith { get; set; } = new List<string>();
        }
        // Request model cho việc tạo event
        public class CreateEventRequest
        {
            [Required]
            [StringLength(2048, ErrorMessage = "Access token không hợp lệ")]
            public string accessToken { get; set; }

            [StringLength(255, ErrorMessage = "Tiêu đề không được vượt quá 255 ký tự")]
            public string title { get; set; }

            [StringLength(1000, ErrorMessage = "Mô tả không được vượt quá 1000 ký tự")]
            public string description { get; set; }

            [StringLength(255, ErrorMessage = "Địa điểm không được vượt quá 255 ký tự")]
            public string location { get; set; }


            public DateTime startTime { get; set; }


            public DateTime endTime { get; set; }

            [Required]
            [StringLength(255, ErrorMessage = "Calendar ID không hợp lệ")]
            public string calendarId { get; set; }

            // Timezone (mặc định là Asia/Ho_Chi_Minh)
            public string timeZone { get; set; } = "Asia/Ho_Chi_Minh";

            // Có phải là sự kiện cả ngày không
            public bool isAllDay { get; set; } = false;

            // Danh sách email người tham gia
            public List<string> attendees { get; set; } = new List<string>();

            // Màu sắc của event (1-11)
            public int? colorId { get; set; }

            // Có gửi email thông báo không
            public bool sendNotifications { get; set; } = true;

            // Reminder settings
            public List<EventReminder> reminders { get; set; } = new List<EventReminder>();

            // Recurrence rules (cho event lặp lại)
            public List<string> recurrenceRules { get; set; } = new List<string>();
        }

        // Model cho reminder
        public class EventReminder
        {
            [Required]
            public string method { get; set; } // "email" hoặc "popup"

            [Required]
            [Range(0, 40320, ErrorMessage = "Thời gian nhắc nhở phải từ 0 đến 40320 phút")]
            public int minutes { get; set; } // Số phút trước khi nhắc nhở
        }

        // Response model cho việc tạo event
        public class CreateEventResponse
        {
            public bool success { get; set; }
            public string eventId { get; set; }
            public string htmlLink { get; set; }
            public string message { get; set; }
            public DateTime? startTime { get; set; }
            public DateTime? endTime { get; set; }
            public string calendarId { get; set; }
        }

        // Request model cho việc lấy danh sách calendar
        public class GetCalendarsRequest
        {
            [Required]
            public string accessToken { get; set; }
        }

        // Response model cho calendar
        public class CalendarInfo
        {
            public string id { get; set; }
            public string summary { get; set; }
            public string description { get; set; }
            public string timeZone { get; set; }
            public string accessRole { get; set; }
            public bool primary { get; set; }
            public string backgroundColor { get; set; }
            public string foregroundColor { get; set; }
        }

        // Request model cho việc lấy events
        public class GetEventsRequest
        {
            [Required]
            public string accessToken { get; set; }

            [Required]
            public string calendarId { get; set; }

            public DateTime? timeMin { get; set; }
            public DateTime? timeMax { get; set; }

            [Range(1, 2500, ErrorMessage = "MaxResults phải từ 1 đến 2500")]
            public int maxResults { get; set; } = 250;

            public string orderBy { get; set; } = "startTime"; // "startTime" hoặc "updated"

            public bool showDeleted { get; set; } = false;
            public bool singleEvents { get; set; } = true;
        }

        // Response model cho event
        public class EventInfo
        {
            public string id { get; set; }
            public string summary { get; set; }
            public string description { get; set; }
            public string location { get; set; }
            public DateTime? startTime { get; set; }
            public DateTime? endTime { get; set; }
            public bool isAllDay { get; set; }
            public string htmlLink { get; set; }
            public string status { get; set; }
            public List<string> attendees { get; set; } = new List<string>();
            public string creatorEmail { get; set; }
            public string organizerEmail { get; set; }
            public DateTime? created { get; set; }
            public DateTime? updated { get; set; }
        }

        // Request model cho việc cập nhật event
        public class UpdateEventRequest
        {
            [Required]
            [StringLength(2048, ErrorMessage = "Access token không hợp lệ")]
            public string accessToken { get; set; }

            public string calendarId { get; set; }

            public string eventId { get; set; }

            // Các trường optional cho partial update
            [StringLength(255, ErrorMessage = "Tiêu đề không được vượt quá 255 ký tự")]
            public string? title { get; set; }

            [StringLength(1000, ErrorMessage = "Mô tả không được vượt quá 1000 ký tự")]
            public string? description { get; set; }

            [StringLength(255, ErrorMessage = "Địa điểm không được vượt quá 255 ký tự")]
            public string? location { get; set; }

            public DateTime? startTime { get; set; }
            public DateTime? endTime { get; set; }
            public string? timeZone { get; set; }
            public bool? isAllDay { get; set; }
            public List<string>? attendees { get; set; }
            public int? colorId { get; set; }
            public bool? sendNotifications { get; set; }
            public List<EventReminder>? reminders { get; set; }
            public List<string>? recurrenceRules { get; set; }
        }

        // Request model cho việc xóa event
        public class DeleteEventRequest
        {
            [Required]
            public string accessToken { get; set; }

            [Required]
            public string calendarId { get; set; }

            [Required]
            public string eventId { get; set; }

            public bool sendNotifications { get; set; } = true;
        }

        public class DeleteEventResponse
        {
            public bool success { get; set; }
            public string eventId { get; set; }
            public string calendarId { get; set; }
            public string message { get; set; }
            public DateTime? deletedAt { get; set; } = DateTime.UtcNow;
        }


        public class CalendarEventSyncResult
        {
            public bool userEventCreated { get; set; }
            public bool salerEventCreated { get; set; }
            public string? userEventId { get; set; }
            public string? salerEventId { get; set; }
            public List<string> errors { get; set; } = new();
            public CalendarSyncStatus status { get; set; }
        }
    }
}

