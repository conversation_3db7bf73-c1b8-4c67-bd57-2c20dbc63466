﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessObjects.Models
{
    public class CustomerProfile
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string id { get; set; }

        public string platform { get; set; }           // zalo / facebook 
        public string platformUserId { get; set; }     // Zalo user_id / Facebook PSID
        public string displayName { get; set; }
        public string avatarUrl { get; set; }
        public string phoneNumber { get; set; }
        public bool isAnonymous { get; set; } = false;
        public DateTime lastInteraction { get; set; }
    }
}
