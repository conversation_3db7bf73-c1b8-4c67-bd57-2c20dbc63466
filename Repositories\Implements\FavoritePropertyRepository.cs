using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using MongoDB.Driver;
using Repositories.Interfaces;

namespace Repositories.Implements
{
    public class FavoritePropertyRepository : GenericRepository<FavoriteProperty>, IFavoritePropertyRepository
    {
        private readonly RevoLandDbContext _context;
        private readonly IMongoCollection<FavoriteProperty> _collection;

        public FavoritePropertyRepository(RevoLandDbContext context) : base(context, "FavoriteProperties")
        {
            _context = context;
            _collection = _context.GetCollection<FavoriteProperty>("FavoriteProperties");
        }

        public async Task<List<FavoriteProperty>> GetByUserIdAsync(string userId)
        {
            return await _collection.Find(fp => fp.userId == userId).ToListAsync();
        }

        public async Task<FavoriteProperty> GetByUserIdAndPropertyIdAsync(string userId, string propertyId)
        {
            return await _collection.Find(fp => fp.userId == userId && fp.propertyId == propertyId).FirstOrDefaultAsync();
        }
    }
}