﻿using BusinessObjects.Models;
using Repositories.Interfaces;
using Services.Interfaces;
namespace Services.Implements
{
    public class PropertyViewHistoryService : IPropertyViewHistoryService
    {
        private readonly IUnitOfWork _unitOfWork;

        public PropertyViewHistoryService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
        public async Task AddViewHistoryAsync(string userId, string propertyId)
        {
            if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(propertyId))
            {
                throw new ArgumentException("User ID và Property ID không được để trống.");
            }

            var existingHistory = await _unitOfWork.PropertyViewHistories.GetByUserIdAndPropertyId(userId, propertyId);
            if (existingHistory != null)
            {
                return;
            }

            var history = new PropertyViewHistory
            {
                userId = userId,
                propertyId = propertyId,
            };

            await _unitOfWork.PropertyViewHistories.AddAsync(history);
            await _unitOfWork.SaveAsync();
        }
        public async Task<List<string>> GetViewHistoryByUserIdAsync(string userId)
        {
            if (string.IsNullOrEmpty(userId))
            {
                throw new ArgumentException("User ID không được để trống.");
            }
            var histories = await _unitOfWork.PropertyViewHistories.GetByUserIdAsync(userId);
            return histories.Select(h => h.propertyId).ToList();
        }
    }
}
