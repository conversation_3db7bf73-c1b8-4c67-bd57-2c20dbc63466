﻿using BusinessObjects.Models;
using MongoDB.Bson;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Repositories.Interfaces
{
    public interface ICollectionPropertyRepository : IGenericRepository<CollectionProperty>
    {


        IQueryable<CollectionProperty> GetAll();
        Task<IEnumerable<CollectionProperty>> GetAllByUserIdAsync(string id);
        Task<CollectionProperty?> FindExistCollectionProperty(string collectionId);
        Task UpdateCollectionImagesAsync(string collectionId);


    }
}
