﻿using BusinessObjects.DTOs.CalendarDTOs;

namespace Controllers.Controllers
{
    using BusinessObjects.DTOs;
    using Microsoft.AspNetCore.Mvc;
    using Services.Interfaces;
    using static BusinessObjects.DTOs.CalendarDTOs.GoogleCalendar;


    [ApiController]
    [Route("api/calendars")]
    public class CalendarController : ControllerBase
    {
        private readonly ICustomCalendarService _calendarService;
        private readonly ICalendarIntegrationService _calendarIntegrationService;
        private readonly ILogger<CalendarController> _logger;
        private readonly ICurrentUserService _currentUserService;

        public CalendarController(ICustomCalendarService calendarService, ILogger<CalendarController> logger , ICurrentUserService currentUserService , ICalendarIntegrationService calendarIntegrationService)
        {
            _calendarService = calendarService;
            _logger = logger;
            _currentUserService = currentUserService;
            _calendarIntegrationService = calendarIntegrationService;
        }

        private ApiResponse CreateResponse(int code, bool status, string message, object data = null)
        {
            return new ApiResponse { code = code, status = status, message = message, data = data };
        }
        
        /// <summary>
        /// Get current user's calendars
        /// </summary>
        [HttpGet("my-calendars")]
        public async Task<ActionResult<ApiResponse>> GetMyCalendars()
        {
            try
            {
                var (userId, _) = _currentUserService.GetCurrentUser();
                var calendars = await _calendarIntegrationService.GetUserCalendarsAsync(userId);

                return Ok(CreateResponse(200, true, "Calendars retrieved successfully", calendars));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get user calendars");
                return StatusCode(500, CreateResponse(500, false, "Failed to retrieve calendars"));
            }
        }
        
        /// <summary>
        /// Get current user's calendar events
        /// </summary>
        [HttpPost("my-events")]
        public async Task<ActionResult<ApiResponse>> GetMyEvents([FromBody] GoogleCalendarIntegration.GetUserEventsRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                    return BadRequest(CreateResponse(400, false, "Invalid request data", ModelState));

                var (userId, _) = _currentUserService.GetCurrentUser();
                var events = await _calendarIntegrationService.GetUserEventsAsync(userId, request);

                return Ok(CreateResponse(200, true, "Events retrieved successfully", events));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get user events");
                return StatusCode(500, CreateResponse(500, false, "Failed to retrieve events"));
            }
        }

        [HttpPost("create-calendar")]
        public async Task<IActionResult> CreateCalendar([FromBody] CreateCalendarRequest request)
        {
            try
            {
                var result = await _calendarService.CreateCalendarAsync(request);
                return Ok(CreateResponse(201, true, "Calendar đã được tạo thành công", result));
            }
            catch (ArgumentException ex)
            {
                return BadRequest(CreateResponse(400, false, "Dữ liệu đầu vào không hợp lệ"));
            }
            catch (InvalidOperationException ex)
            {
                return StatusCode(500, CreateResponse(500, false, "Có lỗi xảy ra khi tạo calendar"));
            }
        }


        /// <summary>
        /// Tạo event mới trong Google Calendar
        /// </summary>
        [HttpPost("events")]
        public async Task<ActionResult<ApiResponse>> CreateEvent([FromBody] CreateEventRequest request)
        {
            try
            {
                // Validate ModelState
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors.Select(e => e.ErrorMessage))
                        .ToList();

                    return BadRequest(CreateResponse(400, false, "Dữ liệu đầu vào không hợp lệ", errors));
                }

                var result = await _calendarService.CreateEventAsync(request);

                return Ok(CreateResponse(201, true, "Event đã được tạo thành công", result));
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Validation error in CreateEvent");
                return BadRequest(CreateResponse(400, false, ex.Message));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError(ex, "Business logic error in CreateEvent");
                return BadRequest(CreateResponse(400, false, ex.Message));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error in CreateEvent");
                return StatusCode(500, CreateResponse(500, false, "Có lỗi xảy ra khi tạo event"));
            }
        }

        /// <summary>
        /// Lấy danh sách calendars của user
        /// </summary>
        [HttpPost("calendars")]
        public async Task<ActionResult<ApiResponse>> GetCalendars([FromBody] GetCalendarsRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateResponse(400, false, "Dữ liệu đầu vào không hợp lệ"));
                }

                var calendars = await _calendarService.GetCalendarsAsync(request.accessToken);

                return Ok(CreateResponse(200, true, $"Đã tải {calendars.Count} calendars", calendars));
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Validation error in GetCalendars");
                return BadRequest(CreateResponse(400, false, ex.Message));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError(ex, "Business logic error in GetCalendars");
                return BadRequest(CreateResponse(400, false, ex.Message));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error in GetCalendars");
                return StatusCode(500, CreateResponse(500, false, "Có lỗi xảy ra khi tải danh sách calendar"));
            }
        }

        /// <summary>
        /// Lấy danh sách events từ calendar
        /// </summary>
        [HttpPost("calendars/events")]
        public async Task<ActionResult<ApiResponse>> GetEvents([FromBody] GetEventsRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateResponse(400, false, "Dữ liệu đầu vào không hợp lệ"));
                }

                var events = await _calendarService.GetEventsAsync(request);

                return Ok(CreateResponse(200, true, $"Đã tải {events.Count} events", events));
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Validation error in GetEvents");
                return BadRequest(CreateResponse(400, false, ex.Message));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError(ex, "Business logic error in GetEvents");
                return BadRequest(CreateResponse(400, false, ex.Message));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error in GetEvents");
                return StatusCode(500, CreateResponse(500, false, "Có lỗi xảy ra khi tải danh sách events"));
            }
        }

        /// <summary>
        /// Lấy thông tin chi tiết của một event
        /// </summary>
        [HttpPost("calendars/{calendarId}/events/{eventId}")]
        public async Task<ActionResult<ApiResponse>> GetEvent(string calendarId, string eventId,
            [FromQuery] string accessToken)
        {
            try
            {
                if (string.IsNullOrEmpty(accessToken))
                {
                    return BadRequest(CreateResponse(400, false, "Access token không được để trống"));
                }

                var eventInfo = await _calendarService.GetEventByIdAsync(accessToken, calendarId, eventId);

                return Ok(CreateResponse(200, true, "Đã tải thông tin event", eventInfo));
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Validation error in GetEvent");
                return BadRequest(CreateResponse(400, false, ex.Message));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError(ex, "Business logic error in GetEvent");
                return BadRequest(CreateResponse(400, false, ex.Message));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error in GetEvent");
                return StatusCode(500, CreateResponse(500, false, "Có lỗi xảy ra khi tải event"));
            }
        }

        /// <summary>
        /// Cập nhật event
        /// </summary>
        [HttpPut("calendars/{calendarId}/events/{eventId}")]
        public async Task<ActionResult<ApiResponse>> UpdateEvent(string calendarId, string eventId,
            [FromBody] UpdateEventRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateResponse(400, false, "Dữ liệu đầu vào không hợp lệ"));
                }

                var result = await _calendarService.UpdateEventAsync(calendarId, eventId, request);

                return Ok(CreateResponse(200, true, "Event đã được cập nhật thành công", result));
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Validation error in UpdateEvent");
                return BadRequest(CreateResponse(400, false, ex.Message));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError(ex, "Business logic error in UpdateEvent");
                return BadRequest(CreateResponse(400, false, ex.Message));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error in UpdateEvent");
                return StatusCode(500, CreateResponse(500, false, "Có lỗi xảy ra khi cập nhật event"));
            }
        }

        /// <summary>
        /// Xóa event
        /// </summary>
        [HttpDelete("events")]
        public async Task<ActionResult<ApiResponse>> DeleteEvent([FromBody] DeleteEventRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateResponse(400, false, "Dữ liệu đầu vào không hợp lệ"));
                }

                var result =
                    await _calendarService.DeleteEventAsync(request.calendarId, request.eventId, request.accessToken);

                return Ok(CreateResponse(200, true, "Event đã được xóa thành công", result));
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Validation error in DeleteEvent");
                return BadRequest(CreateResponse(400, false, ex.Message));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError(ex, "Business logic error in DeleteEvent");
                return BadRequest(CreateResponse(400, false, ex.Message));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error in DeleteEvent");
                return StatusCode(500, CreateResponse(500, false, "Có lỗi xảy ra khi xóa event"));
            }
        }
    }
}