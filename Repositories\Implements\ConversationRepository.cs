﻿using BusinessObjects.DatabaseSettings;
using BusinessObjects.Models;
using BusinessObjects.QueryObject;
using MongoDB.Driver;
using Repositories.Interfaces;

namespace Repositories.Implements
{
    public class ConversationRepository : GenericRepository<Conversation>, IConversationRepository
    {
        private readonly IMongoCollection<Conversation> _conversations;

        public ConversationRepository(RevoLandDbContext context) : base(context, "Conversations")
        {
            _conversations = context.GetCollection<Conversation>("Conversations");
        }

        public async Task<Conversation> GetOrCreateAsync(string platform, string userId)
        {
            var filter = Builders<Conversation>.Filter.And(
                Builders<Conversation>.Filter.Eq(x => x.platform, platform),
                Builders<Conversation>.Filter.Eq(x => x.userId, userId),
                Builders<Conversation>.Filter.Eq(x => x.isClosed, false)
            );

            var existing = await _conversations.Find(filter).FirstOrDefaultAsync();
            if (existing != null)
                return existing;

            var newConvo = new Conversation
            {
                platform = platform,
                userId = userId,
                lastUpdated = DateTime.UtcNow,
                isClosed = false
            };

            await _conversations.InsertOneAsync(newConvo);
            return newConvo;
        }

        public async Task UpdateLastMessageAsync(string conversationId, string message)
        {
            var filter = Builders<Conversation>.Filter.Eq(x => x.id, conversationId);
            var update = Builders<Conversation>.Update
                .Set(x => x.lastMessage, message)
                .Set(x => x.lastUpdated, DateTime.UtcNow);

            await _conversations.UpdateOneAsync(filter, update);
        }

        public async Task<List<Conversation>> GetAllConversationsAsync(QueryConversation query)
        {
            int skip = (query.pageNumber - 1) * query.pageSize;

            var conversations = await _conversations
                .Find(_ => true)
                .SortByDescending(c => c.lastUpdated)
                .Skip(skip)
                .Limit(query.pageSize)
                .ToListAsync();

            return conversations;
        }

        public async Task<Conversation> GetByUserIdAsync(string userID)
        {
            var filter = Builders<Conversation>.Filter.And(
                Builders<Conversation>.Filter.Eq(x => x.userId, userID),
                Builders<Conversation>.Filter.Eq(x => x.platform, "system")
            );
            var conversation = await _conversations
                .Find(filter)
                .FirstOrDefaultAsync();
            return conversation;
        }

        public async Task<Conversation> GetByUserIdIdAndSellerIdAsync(string userId, string sellerId)
        {
            var filter = Builders<Conversation>.Filter.And(
                Builders<Conversation>.Filter.Eq(x => x.userId, userId),
                Builders<Conversation>.Filter.Eq(x => x.sellerId, sellerId)
            );
            return await _conversations.Find(filter).FirstOrDefaultAsync();
        }

        public async Task<List<Conversation>> GetConversationsByCurrentSellerAsync(string currentSellerId, QueryConversation query)
        {
            int skip = (query.pageNumber - 1) * query.pageSize;

            var filter = Builders<Conversation>.Filter.And(
                Builders<Conversation>.Filter.Eq(x => x.sellerId, currentSellerId)
            );
            var conversations = await _conversations
                .Find(filter)
                .SortByDescending(c => c.lastUpdated)
                .Skip(skip)
                .Limit(query.pageSize)
                .ToListAsync();
            return conversations;
        }
    }
}
