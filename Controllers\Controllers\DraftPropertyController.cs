﻿using BusinessObjects.DTOs;
using BusinessObjects.QueryObject;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Services.Interfaces;

namespace Controllers.Controllers
{
    [Route("api/draft-properties")]
    [ApiController]
    public class DraftPropertyController : ControllerBase
    {
        private readonly IDraftPropertyService _draftPropertyService;

        public DraftPropertyController(IDraftPropertyService draftPropertyService)
        {
            // Constructor logic can be added here if needed
            _draftPropertyService = draftPropertyService;
        }

        private ApiResponse CreateResponse(int code, bool status, string message, object data = null)
        {
            return new ApiResponse { code = code, status = status, message = message, data = data };
        }

        [HttpPost]
        public async Task<IActionResult> CreateDraftProperty([FromBody] CreateDraftPropertyRequest request)
        {
            try
            {
                if (request == null)
                {
                    return BadRequest(CreateResponse(400, false, "Không tìm thấy data"));
                }
                var userId = User.Claims.FirstOrDefault(c => c.Type == "UserID")?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(CreateResponse(401, false, "Không tìm thấy người dùng"));
                }


                await _draftPropertyService.AddDraftProperty(request, userId);
                return Ok(CreateResponse(200, true, "Thành công"));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(500, false, ex.Message));
            }
        }

        [HttpGet]
        [Authorize(Roles = "Saler,Owner")]
        public async Task<IActionResult> GetDraftProperties([FromQuery] QueryDraftProperty query)
        {
            try
            {
                var userId = User.Claims.FirstOrDefault(c => c.Type == "UserID")?.Value;

                const string ClaimsType = "http://schemas.microsoft.com/ws/2008/06/identity/claims/role";
                const string Role = "Role";
                var role = User.Claims.FirstOrDefault(c => c.Type == ClaimsType)?.Value ?? Role;

                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(CreateResponse(401, false, "Người dùng chưa được xác thực"));
                }
                var result = await _draftPropertyService.GetDraftPropertiesByUserAsync(userId, role, query.pageSize, query.currentPage);
                return Ok(CreateResponse(200, true, "Lấy property nháp thành công", result));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(500, false, ex.Message));
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetDraftPropertyById(string id)
        {
            try
            {
                var userId = User.Claims.FirstOrDefault(c => c.Type == "UserID")?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(CreateResponse(401, false, "Người dùng chưa được xác thực"));
                }
                var draftProperty = await _draftPropertyService.GetDraftPropertyByIdAsync(userId, id);
                return Ok(CreateResponse(200, true, "Lấy property nháp thành công", draftProperty));
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(CreateResponse(404, false, ex.Message));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(500, false, ex.Message));
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteDraftProperty(string id)
        {
            try
            {
                var userId = User.Claims.FirstOrDefault(c => c.Type == "UserID")?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(CreateResponse(401, false, "Người dùng chưa được xác thực"));
                }
                await _draftPropertyService.DeleteByIdAsync(userId, id);
                return Ok(CreateResponse(200, true, "Xóa thành công"));
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(CreateResponse(404, false, ex.Message));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateResponse(500, false, ex.Message));
            }
        }


    }
}
