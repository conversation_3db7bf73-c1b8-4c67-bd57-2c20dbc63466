﻿using BusinessObjects.DTOs;
using BusinessObjects.DTOs.TenantDTOs;
using BusinessObjects.Models;
using BusinessObjects.QueryObject;
using MathNet.Numerics.Distributions;
using Microsoft.AspNetCore.Http;
using Repositories.Implements;
using Repositories.Interfaces;
using Services.Interfaces;
using Services.Tools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using System.Text;
using System.Threading.Tasks;

namespace Services.Implements
{
	public class TenantService : ITenantService
	{
		private readonly IUnitOfWork _unitOfWork;
		private readonly FirebaseService _firebaseService;
		private readonly UserPasswordHasher _userPasswordHasher;
		private readonly IUserService _userService;

		public TenantService(IUnitOfWork unitOfWork, FirebaseService firebaseService, UserPasswordHasher userPasswordHasher, IUserService userService)
		{
			_unitOfWork = unitOfWork;
			_firebaseService = firebaseService;
			_userPasswordHasher = userPasswordHasher;
			_userService = userService;
		}

		public async Task<IEnumerable<Tenant>> GetAllTenantsAsync()
		{
			return await _unitOfWork.Tenants.GetAllAsync();
		}

		public async Task<Tenant> GetTenantByIdAsync(string id)
		{
			return await _unitOfWork.Tenants.GetByIdAsync(id);
		}

		public async Task<Tenant> GetTenantByPhoneAsync(string phone)
		{
			return await _unitOfWork.Tenants.GetTenantByPhone(phone);
		}

		public async Task<Tenant> AddTenantAsync(Tenant tenant)
		{
			return await _unitOfWork.Tenants.AddAsync(tenant);
		}
		public async Task<string> UploadIdVerificationAsync(IFormFile? file)
		{
			if (file == null) return null;

			using (var stream = file.OpenReadStream())
			{
				return await _firebaseService.UploadIdVerificationAsync(stream, Guid.NewGuid() + Path.GetExtension(file.FileName));
			}
		}
		public async Task<Tenant> AddTenantAsync(TenantCreateRequest tenant, List<string> idVerificationUrl)
		{
			if (await _unitOfWork.Tenants.IsPhoneExist(tenant.phone))
			{
				throw new ArgumentException("Số điện thoại đã được sử dụng.");
			}

			if (await _unitOfWork.Tenants.IsEmailExist(tenant.email))
			{
				throw new ArgumentException("Email đã được sử dụng.");
			}

			var newTenant = new Tenant
			{
				address = tenant.address,
				createdAt = DateTime.UtcNow,
				email = tenant.email,
				idVerification = idVerificationUrl,
				leaseEndDate = tenant.leaseEndDate,
				leaseStartDate = tenant.leaseStartDate,
				name = tenant.name,
				phone = tenant.phone,
				updatedAt = DateTime.UtcNow
			};

			await _unitOfWork.Tenants.AddAsync(newTenant);

			return newTenant;
		}
		public async Task CreateUserFromTenantAsync(TenantCreateRequest tenant)
		{
			var email_exist = await _userService.FindUserByEmailAsync(tenant.email) != null
				|| await _userService.FindUserByPhonenumberAsync(tenant.phone) != null;

			if (!email_exist)
			{
				var userdto = new UserDTO()
				{
					about = "Buyer",
					email = tenant.email,
					fullName = tenant.name,
					joinedAt = DateTime.UtcNow,
					phoneNumber = tenant.phone,
					userName = tenant.name,
					role = UserRole.User,
					status = Status.Online,
				};
				var password = _userPasswordHasher.HashPassword(userdto, "QuindLand");
				var user = new User()
				{
					fullName = userdto.fullName,
					status = userdto.status,
					role = userdto.role,
					about = userdto.about,
					avatar = userdto.avatar,
					birthdate = userdto.birthdate,
					email = userdto.email,
					joinedAt = userdto.joinedAt,
					password = password,
					phoneNumber = userdto.phoneNumber,
					userName = userdto.userName,
				};
				await _unitOfWork.Users.AddAsync(user);
			}
		}

		public async Task<bool> UpdateTenantAsync(TenantUpdateRequest tenantRequest, List<string?> idVerificationFile)
		{
			var tenant = await _unitOfWork.Tenants.GetByIdAsync(tenantRequest.id);
			if (tenant == null) return false;

			if (await _unitOfWork.Tenants.IsPhoneExist(tenant.phone))
			{
				throw new ArgumentException("Số điện thoại đã được sử dụng.");
			}

			if (await _unitOfWork.Tenants.IsEmailExist(tenant.email))
			{
				throw new ArgumentException("Email đã được sử dụng.");
			}

			bool isUpdated = false;

			if (tenantRequest.leaseEndDate != tenant.leaseEndDate && tenantRequest.leaseEndDate != null)
			{
				tenant.leaseEndDate = tenantRequest.leaseEndDate;
				isUpdated = true;
			}

			if (tenantRequest.address != tenant.address && tenantRequest.address != null)
			{
				tenant.address = tenantRequest.address;
				isUpdated = true;
			}



			if (tenantRequest.name != tenant.name && tenantRequest.name != null)
			{
				tenant.name = tenantRequest.name;
				isUpdated = true;
			}

			if (tenantRequest.phone != tenant.phone && tenantRequest.phone != null)
			{
				tenant.phone = tenantRequest.phone;
				isUpdated = true;
			}

			if (tenantRequest.email != tenant.email && tenantRequest.email != null)
			{
				tenant.email = tenantRequest.email;
				isUpdated = true;
			}

			if (isUpdated)
			{
				tenant.updatedAt = DateTime.UtcNow;
				await _unitOfWork.Tenants.UpdateAsync(tenantRequest.id, tenant);
				return true;
			}

			return false;
		}

		public async Task<Tenant> DeleteTenantAsync(string id)
		{
			return await _unitOfWork.Tenants.DeleteAsync(id);
		}
		public async Task<List<Tenant>> GetAllTenantsAsync(QueryTenant query)
		{
			if (query.searchTerm == null)
			{
				query.searchTerm = "";
			}

			var result = await _unitOfWork.Tenants.GetAllAsync();
			var tenantList = new List<Tenant>();

			if (result == null || !result.Any())
			{
				return new List<Tenant>();
			}

			// Tìm kiếm theo tên, số điện thoại, email
			result = result.Where(t => t.name.Contains(query.searchTerm, StringComparison.OrdinalIgnoreCase) ||
									   t.phone.Contains(query.searchTerm, StringComparison.OrdinalIgnoreCase) ||
									   t.email.Contains(query.searchTerm, StringComparison.OrdinalIgnoreCase) ||
									   t.address.Contains(query.searchTerm, StringComparison.OrdinalIgnoreCase))
						   .ToList();

			// Lọc theo ngày bắt đầu hợp đồng
			if (query.leaseStartDate.HasValue)
			{
				result = result.Where(t => t.leaseStartDate >= query.leaseStartDate.Value).ToList();
			}

			// Lọc theo ngày kết thúc hợp đồng
			if (query.leaseEndDate.HasValue)
			{
				result = result.Where(t => t.leaseEndDate <= query.leaseEndDate.Value).ToList();
			}

			// Sắp xếp
			switch (query.sortBy)
			{
				case TenantEnumSortBy.name:
					result = query.isDescending ? result.OrderByDescending(t => t.name).ToList() : result.OrderBy(t => t.name).ToList();
					break;

				case TenantEnumSortBy.leaseStartDate:
					result = query.isDescending ? result.OrderByDescending(t => t.leaseStartDate).ToList() : result.OrderBy(t => t.leaseStartDate).ToList();
					break;
				case TenantEnumSortBy.leaseEndDate:
					result = query.isDescending ? result.OrderByDescending(t => t.leaseEndDate).ToList() : result.OrderBy(t => t.leaseEndDate).ToList();
					break;

				default:
					result = result.OrderBy(t => t.name).ToList();
					break;
			}

			// Phân trang
			var paginatedTenants = result.Skip((query.pageNumber - 1) * query.pageSize)
										 .Take(query.pageSize)
										 .ToList();

			return paginatedTenants;
		}

		public async Task<Tenant> GetTenantByEmailAsync(string email)
		{
			var tenants = await _unitOfWork.Tenants.GetAllAsync();
			return tenants.FirstOrDefault(t => t.email.Equals(email, StringComparison.OrdinalIgnoreCase));
		}
	}
}
