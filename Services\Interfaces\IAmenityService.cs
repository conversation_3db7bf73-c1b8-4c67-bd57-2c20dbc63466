﻿using BusinessObjects.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Services.Interface
{
    public interface IAmenityService
    {
        Task<IEnumerable<Amenity>> GetAllAmenitiesAsync();
        Task<Amenity> GetAmenityByIdAsync(string id);
        Task<Amenity> AddAmenityAsync(Amenity amenity);
        Task<Amenity> UpdateAmenityAsync(string id, Amenity amenity);
        Task<bool> DeleteAmenityAsync(string id);
    }
}
