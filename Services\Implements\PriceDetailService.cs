﻿using BusinessObjects.Models;
using Repositories.Interfaces;
using Services.Interface;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Services.Implements
{
    public class PriceDetailService : IPriceDetailService
    {
        private readonly IUnitOfWork _unitOfWork;

        public PriceDetailService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<PriceDetail>> GetAllPriceDetailsAsync()
        {
            return await _unitOfWork.PriceDetails.GetAllAsync();
        }

        public async Task<PriceDetail> GetPriceDetailByIdAsync(string id)
        {
            return await _unitOfWork.PriceDetails.GetByIdAsync(id);
        }

        public async Task<PriceDetail> AddPriceDetailAsync(PriceDetail priceDetail)
        {
            await _unitOfWork.PriceDetails.AddAsync(priceDetail);
            await _unitOfWork.SaveAsync();
            return priceDetail;
        }

        public async Task<PriceDetail> UpdatePriceDetailAsync(string id, PriceDetail priceDetail)
        {
            await _unitOfWork.PriceDetails.UpdateAsync(id, priceDetail);
            await _unitOfWork.SaveAsync();
            return priceDetail;
        }

        public async Task<bool> DeletePriceDetailAsync(string id)
        {
            await _unitOfWork.PriceDetails.DeleteAsync(id);
            await _unitOfWork.SaveAsync();
            return true;
        }
    }
}
