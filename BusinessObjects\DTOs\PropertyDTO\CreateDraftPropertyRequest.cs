﻿using BusinessObjects.Models;

public class CreateDraftPropertyRequest
{
    public string? title { get; set; }
    public string? name { get; set; }
    public string? description { get; set; }
    public PropertyTransactionType? transactionType { get; set; }
    public PropertyType? type { get; set; }
    public PropertyStatus? status { get; set; }
    public string? adminNote { get; set; }
    public string? code { get; set; }
    public string? ownerId { get; set; }
    public LocationDraftCreateRequest? location { get; set; }
    public PropertyDetail? propertyDetails { get; set; }
    public PriceDraftDetailCreateRequest? priceDetails { get; set; }
    public Amenity? amenities { get; set; }
    public List<string>? images { get; set; }
    public int? yearBuilt { get; set; }
    public List<string>? floorPlans { get; set; }
    public PropertyVideo? video { get; set; }
    public List<string>? legalDocuments { get; set; }
}

public class LocationDraftCreateRequest
{
    public string? address { get; set; }
    public string? city { get; set; }
    public string? district { get; set; }
    public string? ward { get; set; }
    public double? latitude { get; set; }
    public double? longitude { get; set; }
}

public class PriceDraftDetailCreateRequest
{
    public double? salePrice { get; set; }

    public double? rentalPrice { get; set; }

    public double? pricePerSquareMeter { get; set; }

    public string? currency { get; set; }
    public double? depositAmount { get; set; }

    public double? maintenanceFee { get; set; }

    public List<string>? paymentMethods { get; set; }
}
