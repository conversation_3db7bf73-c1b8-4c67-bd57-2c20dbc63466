﻿using BusinessObjects.Models;
using MongoDB.Driver;

namespace Repositories.Interfaces
{
    public interface IDealsActivityLogRepository : IGenericRepository<DealsActivityLog>
    {
        Task<List<DealsActivityLog>> GetDealsActivityLogByFilterAsync(
            FilterDefinition<DealsActivityLog> filter,
            SortDefinition<DealsActivityLog>? sort,
            int skip,
            int limit);

        Task<long> CountDealsActivityLogByFilterAsync(FilterDefinition<DealsActivityLog> filter);

    }

}
